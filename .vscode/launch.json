{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "name": "ticking_app",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "ticking_app (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "ticking_app (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "scrollable_positioned_list",
            "cwd": "packages\\scrollable_positioned_list",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "scrollable_positioned_list (profile mode)",
            "cwd": "packages\\scrollable_positioned_list",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "scrollable_positioned_list (release mode)",
            "cwd": "packages\\scrollable_positioned_list",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "ticking_api_client",
            "cwd": "packages\\ticking_api_client",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "ticking_api_client (profile mode)",
            "cwd": "packages\\ticking_api_client",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "ticking_api_client (release mode)",
            "cwd": "packages\\ticking_api_client",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "ticking_localizations",
            "cwd": "packages\\ticking_localizations",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "ticking_localizations (profile mode)",
            "cwd": "packages\\ticking_localizations",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "ticking_localizations (release mode)",
            "cwd": "packages\\ticking_localizations",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "ticking_log_service",
            "cwd": "packages\\ticking_log_service",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "ticking_log_service (profile mode)",
            "cwd": "packages\\ticking_log_service",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "ticking_log_service (release mode)",
            "cwd": "packages\\ticking_log_service",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}