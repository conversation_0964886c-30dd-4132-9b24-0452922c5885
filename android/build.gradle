buildscript {
    ext.kotlin_version = '1.8.22'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

// Force all subprojects to use the same Kotlin version
subprojects {
    buildscript {
        ext.kotlin_version = '1.8.22'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }

    configurations.all {
        resolutionStrategy {
            force "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
            force "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
            force "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
