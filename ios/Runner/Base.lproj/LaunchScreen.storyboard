<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="19455" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" launchScreen="YES" colorMatched="YES" initialViewController="01J-lp-oVM">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="19454"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="EHf-IW-A2E">
            <objects>
                <viewController id="01J-lp-oVM" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="Ydg-fD-yQy"/>
                        <viewControllerLayoutGuide type="bottom" id="xbc-2k-c8Z"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="Ze5-6b-2t3">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" image="splash" translatesAutoresizingMaskIntoConstraints="NO" id="kLY-Mb-E8T">
                                <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                            </imageView>
                        </subviews>
                        <color key="backgroundColor" systemColor="tintColor"/>
                        <constraints>
                            <constraint firstItem="kLY-Mb-E8T" firstAttribute="top" secondItem="Ze5-6b-2t3" secondAttribute="top" id="R0c-ov-hG2"/>
                            <constraint firstAttribute="bottom" secondItem="kLY-Mb-E8T" secondAttribute="bottom" id="ovY-5f-ulx"/>
                            <constraint firstAttribute="trailing" secondItem="kLY-Mb-E8T" secondAttribute="trailing" id="sTY-10-bdU"/>
                            <constraint firstItem="kLY-Mb-E8T" firstAttribute="leading" secondItem="Ze5-6b-2t3" secondAttribute="leading" id="y3n-ve-MLr"/>
                        </constraints>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iYj-Kq-Ea1" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="76.811594202898561" y="251.11607142857142"/>
        </scene>
    </scenes>
    <resources>
        <image name="splash" width="281.5" height="500.5"/>
        <systemColor name="tintColor">
            <color red="0.0" green="0.47843137254901963" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
    </resources>
</document>
