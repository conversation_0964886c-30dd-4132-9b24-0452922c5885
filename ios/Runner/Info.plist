<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app requires access to the photo library.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app does not require access to the microphone.</string>
	<key>NSCameraUsageDescription</key>
	<string>This app requires access to the camera.</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>BSP ticket</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>googlechromes</string>
		<string>comgooglemaps</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>NSLocationUsageDescription</key>
	<string>My description about why I need this capability</string>
	<key>NSCameraUsageDescription</key>
	<string>You need this permission to take a picture</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Program requires GPS to track location</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>This app needs access to location when in the background.</string>
	<key>NSLocationTemporaryUsageDescriptionDictionary</key>
	<dict>
		<key>YourPurposeKey</key>
		<string>The example App requires temporary access to the device's precise location.</string>
	</dict>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>This app needs your location to calculate the distances to the places of your assigned tasks.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>You need this permission to take a picture</string>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
