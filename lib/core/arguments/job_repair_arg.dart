import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/pages/complete_job/list/bloc/complete_job_list_bloc.dart';

///Argument for [JobRepairArg]
class JobRepairArg {
  JobRepairArg({
    required this.job,
    this.jobRepairType = JobRepairType.working,
    this.bloc,
  });

  final Job job;
  final JobRepairType jobRepairType;
  final CompleteJobListBloc? bloc;

  JobRepairArg copyWith({
    CompleteJobListBloc? bloc,
    Job? job,
    JobRepairType? jobRepairType,
  }) {
    return JobRepairArg(
      bloc: bloc ?? this.bloc,
      job: job ?? this.job,
      jobRepairType: jobRepairType ?? this.jobRepairType,
    );
  }
}

enum JobRepairType { working, reviewing }
