import 'package:ticking_api_client/ticking_api_client.dart';

///Argument for [PhotoPreviewPage]
class PreviewPhotoArg {
  PreviewPhotoArg({
    // required this.bytes,
    required this.job,
    required this.filePath,
    this.reasonValue = ReasonValue.start,
    this.jobTaskListRequest,
    this.onRepairImageCallBack,
  });

  // final Uint8List bytes;
  final String filePath;
  final Job job;
  final ReasonValue reasonValue;
  final JobTaskListRequest? jobTaskListRequest;
  final Function()? onRepairImageCallBack;

  PreviewPhotoArg copyWith({
    // Uint8List? bytes,
    Job? job,
    ReasonValue? reasonValue,
    String? filePath,
    JobTaskListRequest? jobTaskListRequest,
    Function()? onRepairImageCallBack,
  }) {
    return PreviewPhotoArg(
      onRepairImageCallBack:
          onRepairImageCallBack ?? this.onRepairImageCallBack,
      // bytes: bytes ?? this.bytes,
      filePath: filePath ?? this.filePath,
      jobTaskListRequest: jobTaskListRequest ?? this.jobTaskListRequest,
      job: job ?? this.job,
      reasonValue: reasonValue ?? this.reasonValue,
    );
  }
}

enum ReasonValue { start, complete, repair }
