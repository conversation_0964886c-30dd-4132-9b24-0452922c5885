import 'dart:typed_data';

import 'package:ticking_api_client/ticking_api_client.dart';

///Argument for [JobDetailArg]
class JobDetailArg {
  JobDetailArg({
    required this.job,
    this.jobDetailType = JobDetailType.working,
  });

  final Job job;
  final JobDetailType jobDetailType;

  JobDetailArg copyWith({
    Job? job,
    JobDetailType? jobDetailType,
  }) {
    return JobDetailArg(
      job: job ?? this.job,
      jobDetailType: jobDetailType ?? this.jobDetailType,
    );
  }
}

enum JobDetailType { working, reviewing }
