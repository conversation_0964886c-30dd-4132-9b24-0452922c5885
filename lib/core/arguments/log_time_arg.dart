import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/pages/complete_job/list/bloc/complete_job_list_bloc.dart';
import 'package:ticking_app/pages/log_time/bloc/log_time_bloc.dart';

///Argument for [LogTimeArg]
class LogTimeArg {
  LogTimeArg({
    this.workLog,
  });

  final WorkLog? workLog;

  LogTimeArg copyWith({
    WorkLog? workLog,
  }) {
    return LogTimeArg(
      workLog: workLog ?? this.workLog,
    );
  }
}

