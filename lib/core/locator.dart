import 'package:cookie_jar/cookie_jar.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/core/styles/app_style_config.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/cache_service/hive_cache_service.dart';
import 'package:ticking_app/services/config_service/app_config.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/config_service/secure_storage_service.dart';
import 'package:ticking_app/services/gelocator_service/gelocator_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/services/lock_service/lock_service_impl.dart';
import 'package:ticking_app/services/loop_service/loop_service.dart';
import 'package:ticking_app/services/loop_service/timer_loop_service.dart';
import 'package:ticking_app/services/network_service/connectity_service.dart';
import 'package:ticking_app/services/network_service/network_service.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/services/task_service/task_service_impl.dart';
import 'package:ticking_log_service/ticking_log_service.dart';

import '../services/dialog_service/dialog_service.dart';
import '../services/lock_service/lock_service.dart';

/// Setup services locator
/// Must call this function before the [startApp] is called.
void setupLocator() {
  _setupInDependentService();
  _setupDependentService();
  _setupDependentApi();
}

/// Register the services that it is not dependent on any services.
void _setupInDependentService() {
  GetIt.I.registerLazySingleton<AppStyleConfig>(() => AppStyleConfig());
  GetIt.I.registerLazySingleton<AppConfig>(() => AppConfig());
  GetIt.I.registerLazySingleton<CookieJar>(() => CookieJar());
}

/// Register the service that it is dependent on other services.
void _setupDependentService() {
  GetIt.I.registerFactory<TickingLogService>(() => LoggerService());
  GetIt.I
      .registerLazySingleton<SecureConfigService>(() => SecureStorageService());
  GetIt.I.registerLazySingleton<LocationService>(() => GeoLocatorService());
  GetIt.I.registerLazySingleton<CacheService>(() => HiveCacheService());
  GetIt.I.registerLazySingleton<NetworkService>(() => ConnectivityService());
  GetIt.I.registerLazySingleton<LoopService>(() => TimerLoopService());
  GetIt.I.registerLazySingleton<TaskService>(() => TaskServiceImpl());
  GetIt.I.registerLazySingleton<LockService>(() => LockServiceImpl());
  GetIt.I.registerLazySingleton<DefaultRoutingDestinationApi>(
      () => DefaultRoutingDestinationApiImpl());

  GetIt.instance.registerLazySingleton<DialogService>(() => DialogService());
}

/// All Api services are registered here.
void _setupDependentApi() {
  GetIt.I.registerLazySingleton<ApiClient>(() => HttpApiClient());
  GetIt.I.registerFactory<AuthApi>(() => AuthApiImpl());
  GetIt.I.registerFactory<JobApi>(() => JobApiImpl());
  GetIt.I.registerFactory<RepairRequestApi>(() => RepairRequestApiImpl());
  GetIt.I.registerFactory<ConfigurationApi>(() => ConfigurationApiImpl());
  GetIt.I.registerFactory<TemplateApi>(() => TemplateApiImpl());
  GetIt.I.registerFactory<WorkLogsApi>(() => WorkLogApiImpl());
  GetIt.I.registerFactory<CheckoutRequestApi>(() => CheckoutRequestApiImpl());
  GetIt.I.registerFactory<BillApi>(() => BillApiImpl());
}
