import 'package:ticking_api_client/ticking_api_client.dart';

import 'dart:async';

class AppSingleton {
  AppSingleton._();

  static final _commentsController =
      StreamController<List<JobComment>>.broadcast();
  static List<JobComment> _listCustomerComments = [];
  static Job? _job;

  static Job? get job => _job;

  static void setJob(Job? job) {
    _job = job;
  }

  static Stream<List<JobComment>> get commentsStream =>
      _commentsController.stream;

  static List<JobComment> get listCustomerComments => _listCustomerComments;

  static void setComments(List<JobComment> comments) {
    _listCustomerComments = comments;
    _commentsController.add(_listCustomerComments);
  }

  static void addComment(JobComment comment) {
    _listCustomerComments.insert(0, comment);
    _commentsController.add(_listCustomerComments);
  }

  static void updateComment(JobComment comment) {
    int index = _listCustomerComments.indexWhere((c) => c.id == comment.id);
    if (index != -1) {
      _listCustomerComments[index] = comment;
      _commentsController.add(_listCustomerComments);
    }
  }

  static void deleteComment(JobComment comment) {
    _listCustomerComments.remove(comment);
    _commentsController.add(_listCustomerComments);
  }

  static void dispose() {
    _commentsController.close();
  }
}
