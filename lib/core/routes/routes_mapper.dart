import 'package:flutter/material.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/edit_comment_arg.dart';
import 'package:ticking_app/core/arguments/job_detail_arg.dart';
import 'package:ticking_app/core/arguments/preview_photo_arg.dart';
import 'package:ticking_app/core/theme_provider.dart';
import 'package:ticking_app/pages/auth/ui/login_page.dart';
import 'package:ticking_app/pages/barcode/barcode_page.dart';
import 'package:ticking_app/pages/details/ui/job_details_page.dart';
import 'package:ticking_app/pages/home/<USER>/home_page.dart';
import 'package:ticking_app/pages/home/<USER>/share/edit_comment_page.dart';
import 'package:ticking_app/pages/log_time/log_time_page.dart';
import 'package:ticking_app/pages/preview/ui/photo_preview_page.dart';
import 'package:ticking_app/pages/repair_ticket/repair_ticket_page.dart';

import '../arguments/job_repair_arg.dart';
import '../arguments/log_time_arg.dart';

class RoutesMapper {
  RoutesMapper._();

  static Route _buildRoute(
      {required Widget page, required RouteSettings settings}) {
    return MaterialPageRoute(
      settings: settings,
      builder: (context) => _buildApplyTextOptionsPage(page),
    );
  }

  ///Return route with settings (you can pass parameters to pages)
  static Route? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRoutes.details:
        final args = settings.arguments! as JobDetailArg;
        return _buildRoute(
          page: JobDetailsPage(
            arg: args,
          ),
          settings: settings,
        );
      case AppRoutes.previewPhoto:
        final args = settings.arguments! as PreviewPhotoArg;
        return _buildRoute(
          page: PhotoPreviewPage(previewPhotoArg: args),
          settings: settings,
        );
      case AppRoutes.editComment:
        final args = settings.arguments! as EditCommentArg;
        return _buildRoute(
          page: EditCommentPage(args: args),
          settings: settings,
        );
      case AppRoutes.repair:
        final args = settings.arguments! as JobRepairArg;
        return _buildRoute(
          page: RepairTicketPage(
            arg: args,
          ),
          settings: settings,
        );

      case AppRoutes.logTime:
        LogTimeArg? args;
        if(settings.arguments != null){
          args = settings.arguments as LogTimeArg;
        }

        return _buildRoute(
          page: LogTimePage(arg: args),
          settings: settings,
        );

      case AppRoutes.barcode:
        return _buildRoute(
          page: const BarcodePage(),
          settings: settings,
        );
      //
      // case AppRoutes.home:
      //   return _buildRoute(
      //     page: const HomePage(),
      //     settings: settings,
      //   );
    }
    return null;
  }

  static Widget _buildApplyTextOptionsPage(Widget page) {
    return ApplyTextOptions(child: page);
  }

  ///Return route without settings
  static Map<String, WidgetBuilder> buildRoute() => {
        AppRoutes.login: (BuildContext context) =>
            _buildApplyTextOptionsPage(const LoginPage()),
        AppRoutes.home: (BuildContext context) =>
            _buildApplyTextOptionsPage(const HomePage()),

        // AppRoutes.previewPhoto: (BuildContext context)=> _buildApplyTextOptionsPage(const PhotoPreviewPage()),
      };
}
