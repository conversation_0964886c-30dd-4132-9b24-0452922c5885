import 'package:badges/badges.dart';
import 'package:flutter/material.dart' hide Badge;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/preview_photo_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/model/service_type.dart';
import 'package:ticking_app/pages/complete_job/list/complete_job_list_tab.dart';
import 'package:ticking_app/pages/home/<USER>/home_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/tab/job_tab.dart';
import 'package:ticking_app/pages/home/<USER>/tab/parts_tab.dart';
import 'package:ticking_app/pages/home/<USER>/tab/selected_job_tab.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';

import 'share/fade_indexed_stack.dart';

class HomePage extends StatefulWidget {
  const HomePage({Key? key}) : super(key: key);

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  void initState() {
    BlocProvider.of<JobListBloc>(context).add(JobListLoaded());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (BuildContext context) => HomeBloc()..add(HomeLoaded()),
      child: Scaffold(
        body: _buildBody(),
        bottomNavigationBar: Container(
          height: 70 + MediaQuery.of(context).padding.bottom,
          decoration: BoxDecoration(
            color: Theme.of(context).backgroundColor,
            // boxShadow: <BoxShadow>[
            //   BoxShadow(
            //       color: AppColors.appShadowColor.withOpacity(0.05),
            //       blurRadius: 10,
            //       spreadRadius: 10),
            // ],
          ),
          child: _buildBottomNavigationBar(),
        ),
      ),
    );
  }

  Widget _buildBody() {
    return _buildPage();
  }

  Widget _buildPage() {
    return BlocListener<JobListBloc, JobListState>(
      listenWhen: (_, current) => ![
        // JobListLoadFailure,
        JobListLoading,
        JobListMapUpdateSuccess,
      ].contains(current.runtimeType),
      listener: (context, state) {
        if (state is JobStartSuccess) {
          final job = state.data.job;
          final String? type = job?.type;
          String name = '';
          if (type.jobServiceType == ServiceType.cleaning) {
            name = 'to clean';
          } else if (type.jobServiceType == ServiceType.repair) {
            name = 'the Repair';
          }
          DialogHelper.showAlertIosDialog(
            title: 'Take Picture',
            content: 'Please take pictures of the pool before starting $name',
            onCanceled: () {
              App.pop();
            },
            onConfirmed: () async {
              App.pop();

              BlocProvider.of<JobListBloc>(context).add(JobListDisabled());
              final XFile? photo =
                  await ImagePicker().pickImage(source: ImageSource.camera);

              if (photo != null && job != null) {
                await App.pushNamed(
                  AppRoutes.previewPhoto,
                  PreviewPhotoArg(
                    filePath: photo.path,
                    // bytes: await photo.readAsBytes(),
                    job: job,
                  ),
                );
              } else {
                BlocProvider.of<JobListBloc>(context).add(JobListEnabled());
              }
            },
          );
          return;
        }

        // if(state)
      },
      child: BlocBuilder<HomeBloc, HomeState>(
        buildWhen: (_, current) => ![
          JobListBSUpdateSuccess,
          JobListMapUpdateSuccess,
        ].contains(current.runtimeType),
        builder: (context, state) {
          return FadeIndexedStack(
            index: state.tabIndex,
            duration: const Duration(milliseconds: 200),
            children: const [
              JobTab(),
              SelectedJobTab(),
              CompleteJobListTab(),
              PartsListTab(),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BlocBuilder<HomeBloc, HomeState>(
      builder: (context, state) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            BottomNavigationBar(
              showSelectedLabels: false,
              showUnselectedLabels: false,
              currentIndex: state.tabIndex,
              unselectedItemColor: Theme.of(context).disabledColor,
              selectedItemColor: Theme.of(context).primaryColor,
              elevation: 0,
              selectedFontSize: 10,
              type: BottomNavigationBarType.fixed,
              onTap: (int index) {
                BlocProvider.of<HomeBloc>(context).add(HomeTabChanged(index));
              },
              items: [
                _buildBottomNavigationItem(
                  icon: _buildNavigationItem(
                    icon: Assets.icon.home.svg(
                      width: 25,
                      height: 25,
                      color: state.tabIndex == 0
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).disabledColor,
                    ),
                    title: 'Assigned Jobs',
                    isSelected: state.tabIndex == 0,
                  ),
                  label: 'Assigned Jobs',
                ),
                _buildBottomNavigationItem(
                  icon: _buildBadge(
                    child: _buildNavigationItem(
                      icon: Assets.icon.pin.svg(
                        width: 25,
                        height: 25,
                        color: state.tabIndex == 1
                            ? Theme.of(context).primaryColor
                            : Theme.of(context).disabledColor,
                      ),
                      title: 'Selected Jobs',
                      isSelected: state.tabIndex == 1,
                    ),
                  ),
                  label: 'Selected Jobs',
                ),
                _buildBottomNavigationItem(
                  icon: _buildNavigationItem(
                    icon: Assets.icon.checklist.svg(
                      width: 25,
                      height: 25,
                      color: state.tabIndex == 2
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).disabledColor,
                    ),
                    title: 'Completed Jobs',
                    isSelected: state.tabIndex == 2,
                  ),
                  label: 'Completed Jobs',
                ),
                _buildBottomNavigationItem(
                  icon: _buildNavigationItem(
                    icon: Assets.icon.parts.svg(
                      width: 25,
                      height: 25,
                      color: state.tabIndex == 3
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).disabledColor,
                    ),
                    title: 'Parts list',
                    isSelected: state.tabIndex == 3,
                  ),
                  label: 'Parts list',
                ),
              ],
            ),
            Positioned(
              left: 0,
              right: 0,
              top: -2,
              child: Container(color: AppColors.grey5, height: 2),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: -2,
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: List.generate(
                    4,
                    (index) => Expanded(
                      child: Center(
                        child: Container(
                          width: 75,
                          color: index == state.tabIndex
                              ? Theme.of(context).primaryColor
                              : Colors.transparent,
                          height: 2,
                        ),
                      ),
                    ),
                  ).toList()),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNavigationItem(
      {required Widget icon, required String title, required bool isSelected}) {
    return Column(
      children: [
        icon,
        4.toVSizeBox(),
        Text(
          title,
          style: Theme.of(context).textTheme.caption?.copyWith(
                color: isSelected
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).disabledColor,
              ),
        ),
      ],
    );
  }

  BottomNavigationBarItem _buildBottomNavigationItem({
    required Widget icon,
    Widget? activeIcon,
    String? label,
  }) {
    return BottomNavigationBarItem(
      icon: icon,
      label: label ?? '',
      activeIcon: activeIcon,
    );
  }

  Widget _buildBadge({
    required Widget child,
  }) {
    return BlocBuilder<JobListBloc, JobListState>(
      builder: (context, state) {
        // Widget content = Container(
        //   width: 10,
        //   height: 10,
        //   decoration: BoxDecoration(
        //     color: AppColors.red,
        //     border: Border.all(
        //       color: Theme.of(context).backgroundColor,
        //       width: 1,
        //     ),
        //     borderRadius: BorderRadius.circular(20),
        //   ),
        // );
        Widget content = const SizedBox();
        BadgePosition position = BadgePosition.topEnd(top: 5, end: 10);
        EdgeInsets padding = const EdgeInsets.all(0);
        final number = state.data.selectedJobList.length;
        if (state.data.selectedJobList.isNotEmpty) {
          padding = const EdgeInsets.all(6);
          position = BadgePosition.topEnd(top: -10, end: -8);

          content = CircleAvatar(
            backgroundColor: AppColors.badgeBackground,
            minRadius: 12,
            child: Text(
              number.toString(),
              style: Theme.of(context)
                  .textTheme
                  .caption
                  ?.copyWith(color: Theme.of(context).backgroundColor),
            ),
          );
        }

        return Badge(
          toAnimate: false,
          padding: padding,
          position: position,
          alignment: Alignment.centerRight,
          // borderSide: const BorderSide(width: 1, color: AppColors.badgeText),
          shape: BadgeShape.circle,
          elevation: 0,
          badgeColor: Colors.transparent,
          borderRadius: BorderRadius.circular(15),
          child: child,
          badgeContent: content,
        );
      },
    );
  }
}
