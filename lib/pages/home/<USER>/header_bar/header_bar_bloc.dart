import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

part 'header_bar_event.dart';

part 'header_bar_state.dart';

class HeaderBarBloc extends Bloc<HeaderBarEvent, HeaderBarState> {
  HeaderBarBloc() : super(const HeaderBarState(true)) {
    on<HeaderBarTypeChanged>(_onTabChanged);
    on<HeaderBarSelectedDate>(_onSelectedDate);
  }

  Future<void> _onTabChanged(
      HeaderBarTypeChanged event,
      Emitter<HeaderBarState> emit,
      ) async {
    emit(HeaderBarState(event.isListType));
  }

  Future<void> _onSelectedDate(
      HeaderBarSelectedDate event,
      Emitter<HeaderBarState> emit,
      ) async {
    // emit(HeaderBarState(event.isListType));
  }
}
