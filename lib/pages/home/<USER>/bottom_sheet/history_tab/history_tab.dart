import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';

import 'bloc/history_bloc.dart';
import 'bloc/history_event.dart';
import 'bloc/history_state.dart';

class HistoryTab extends StatefulWidget {
  const HistoryTab({
    Key? key,
    required this.job,
  }) : super(key: key);
  final Job job;

  @override
  State<HistoryTab> createState() => _HistoryTabState();
}

class _HistoryTabState extends State<HistoryTab> {
  final HistoryBloc _bloc = HistoryBloc();

  @override
  void initState() {
    _bloc.add(HistoryLoaded(job: widget.job));
    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: SingleChildScrollView(
        child: Column(
          children: [
            BlocBuilder<HistoryBloc, HistoryState>(
              buildWhen: (previous, current) =>
                  previous.dateTimeList != current.dateTimeList,
              builder: (context, state) {
                List<DateTime> datetimeHeaders = state.dateTimeList;
                if (datetimeHeaders.isEmpty) return const SizedBox();
                DateTime? value1;
                DateTime? value2;
                DateTime? value3;
                DateTime? value4;
                if (datetimeHeaders.isNotEmpty) {
                  value1 = datetimeHeaders[0];
                }
                if (datetimeHeaders.length > 1) {
                  value2 = datetimeHeaders[1];
                }
                if (datetimeHeaders.length > 2) {
                  value3 = datetimeHeaders[2];
                }
                if (datetimeHeaders.length > 3) {
                  value4 = datetimeHeaders[3];
                }
                return Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _HeaderColumnWidget(
                      value: value1,
                      color: AppColors.grey13,
                    ),
                    _HeaderColumnWidget(
                      value: value2,
                    ),
                    _HeaderColumnWidget(
                      value: value3,
                      color: AppColors.grey13,
                    ),
                    _HeaderColumnWidget(
                      value: value4,
                    ),
                  ],
                );
              },
            ),
            const Divider(
              color: AppColors.grey11,
            ),
            BlocBuilder<HistoryBloc, HistoryState>(
              buildWhen: (previous, current) =>
                  previous.maintenanceTasks != current.maintenanceTasks,
              builder: (context, state) {
                List<MaintenanceTask> maintenanceTasks =
                    List.from(state.maintenanceTasks);

                List<DateTime> datetimeHeaders = state.dateTimeList;
                if (datetimeHeaders.isEmpty) return const SizedBox();
                return SizedBox(
                  height: 200,
                  child: ListView.separated(
                    itemBuilder: (context, index) {
                      final item = maintenanceTasks[index];
                      List<HistoricalValue> historicalValues =
                          item.historicalValues;
                      HistoricalValue? value1;
                      HistoricalValue? value2;
                      HistoricalValue? value3;
                      HistoricalValue? value4;
                      if (datetimeHeaders.isNotEmpty) {
                        value1 = historicalValues.firstWhereOrNull(
                          (element) => element.date == datetimeHeaders[0],
                        );
                      }
                      if (datetimeHeaders.length > 1) {
                        value2 = historicalValues.firstWhereOrNull(
                          (element) => element.date == datetimeHeaders[1],
                        );
                      }
                      if (datetimeHeaders.length > 2) {
                        value3 = historicalValues.firstWhereOrNull(
                          (element) => element.date == datetimeHeaders[2],
                        );
                      }
                      if (datetimeHeaders.length > 3) {
                        value4 = historicalValues.firstWhereOrNull(
                          (element) => element.date == datetimeHeaders[3],
                        );
                      }

                      return Stack(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              _ValueColumnWidget(
                                value: value1,
                                color: AppColors.grey13,
                              ),
                              _ValueColumnWidget(
                                value: value2,
                              ),
                              _ValueColumnWidget(
                                value: value3,
                                color: AppColors.grey13,
                              ),
                              _ValueColumnWidget(
                                value: value4,
                              ),
                            ],
                          ),
                          Text(
                            item.name,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF363636),
                              fontWeight: FontWeight.w700,
                            ),
                          ).pl(value: 16).pt(value: 12),
                        ],
                      );
                    },
                    itemCount: maintenanceTasks.length,
                    separatorBuilder: (BuildContext context, int index) {
                      return const Divider(
                        color: AppColors.grey11,
                      );
                    },
                  ),
                );
              },
            ),
            const SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}

class _ValueColumnWidget extends StatelessWidget {
  const _ValueColumnWidget({
    Key? key,
    this.value,
    this.color = Colors.white,
  }) : super(key: key);
  final HistoricalValue? value;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        color: color,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(
            16,
            48,
            16,
            12,
          ),
          child: Text(
            _convertValue(value?.value),
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF363636),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  String _convertValue(dynamic value) {
    if (value == null) {
      return 'NA';
    }

    ///Convert value to String
    String stringValue = value.toString();

    ///If String is number
    if (_isNumeric(stringValue)) {
      ///Convert string to double
      final doubleValue = double.tryParse(stringValue) ?? 0;

      ///If value is int
      if (_isInteger(doubleValue)) {
        return doubleValue.toInt().toString();
      } else {
        return doubleValue.toStringAsFixed(2);
      }
    }

    ///If value is boolean
    if (stringValue == "true") {
      stringValue = "Yes";
    } else if (stringValue == "false") {
      stringValue = "No";
    }
    return stringValue;
  }

  bool _isInteger(num value) => (value % 1) == 0;

  bool _isNumeric(String? s) {
    if (s == null) {
      return false;
    }
    return double.tryParse(s) != null;
  }
}

class _HeaderColumnWidget extends StatelessWidget {
  const _HeaderColumnWidget({
    Key? key,
    this.value,
    this.color = Colors.white,
  }) : super(key: key);
  final DateTime? value;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        color: color,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 24),
          child: Text(
            value != null ? DateFormat('MM/dd/yyyy').format(value!) : 'NA',
            textAlign: TextAlign.center,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF363636),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }
}
