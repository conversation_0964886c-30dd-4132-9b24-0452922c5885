import 'dart:typed_data';
import 'package:ticking_api_client/ticking_api_client.dart';

class HistoryState {
  const HistoryState({
    this.job,
    this.status = HistoryStatus.load,
    this.uInt8Lists,
    this.error,
    this.firstVisit = false,
    this.poolIsDown = false,
    this.dateTimeList = const [],
    this.maintenanceTasks = const [],
  });

  HistoryState copyWith({
    Job? job,
    HistoryStatus? status,
    List<Uint8List>? uInt8Lists,
    String? error,
    bool? firstVisit,
    bool? poolIsDown,
    List<DateTime>? dateTimeList,
    List<MaintenanceTask>? maintenanceTasks,
  }) {
    return HistoryState(
      dateTimeList: dateTimeList ?? this.dateTimeList,
      firstVisit: firstVisit ?? this.firstVisit,
      poolIsDown: poolIsDown ?? this.poolIsDown,
      status: status ?? this.status,
      job: job ?? this.job,
      error: error ?? this.error,
      uInt8Lists: uInt8Lists ?? this.uInt8Lists,
      maintenanceTasks: maintenanceTasks ?? this.maintenanceTasks,
    );
  }

  final Job? job;
  final String? error;
  final HistoryStatus status;
  final List<Uint8List>? uInt8Lists;
  final bool firstVisit;
  final bool poolIsDown;
  final List<DateTime> dateTimeList;
  final List<MaintenanceTask> maintenanceTasks;

  @override
  String toString() {
    return 'HistoryState{job: $job, error: $error, status: $status, uInt8Lists: $uInt8Lists, firstVisit: $firstVisit, poolIsDown: $poolIsDown, dateTimeList: $dateTimeList, maintenanceTasks: $maintenanceTasks}';
  }
}

enum HistoryStatus {
  loading,
  load,
  failure,
  imageInsertSuccess,
  submitSuccess,
  submitLocalSave,
}
