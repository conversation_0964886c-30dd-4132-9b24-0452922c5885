import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'history_event.dart';
import 'history_state.dart';

///Bloc for repair ticket
class HistoryBloc extends Bloc<HistoryEvent, HistoryState> {
  HistoryBloc({
    JobApi? jobApi,
    TickingLogService? logService,
    TaskService? taskService,
    CacheService? cacheService,
    SecureConfigService? secureConfigService,
  }) : super(const HistoryState()) {
    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _tickingLogService.setClassName(toString());
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
    on<HistoryLoaded>(_onLoaded);
  }

  late CacheService _cacheService;
  late final JobApi _jobApi;
  late TickingLogService _tickingLogService;
  late final TaskService _taskService;
  late final SecureConfigService _secureConfigService;

  // Job job = Job.fromJson({
  //   "id": "4806",
  //   "employee": {"name": "Sarah Robinson"},
  //   "type": "cleaninng",
  //   "status": "to_do",
  //   "schedule_date": "2021-02-18",
  //   "completed_date": null,
  //   "contact": {
  //     "name": "Jennifer Ray",
  //     "street": "05306 Neal Streets",
  //     "city": "New Robert",
  //     "state": {"code": "CA", "name": "California"},
  //     "country": {"code": "US", "name": "United States"},
  //     "zip_code": "94117",
  //     "metro": "New Jillview",
  //     "service_zone": "New Jillview Zone 3",
  //     "field_manager": {
  //       "name": "Richard Bennett",
  //       "phone": "************",
  //       "mobile": "7--1054"
  //     },
  //     "email": "<EMAIL>",
  //     "phone": "******-462-3268x28053",
  //     "mobile": "001-608-194-1592x3797",
  //     "account_note":
  //         "Order up federal person force number line. Person early leave. Administration better different last.\nArtist study score wear say word prove. Very production the if fly out.",
  //     "gps": {"latitude": "33.92946", "longitude": "-116.97725"}
  //   },
  //   "details": {
  //     "name": "JOB/2021/01760",
  //     "service_type": "Pool Cleaning",
  //     "service_level": "Full",
  //     "timer": 3600,
  //     "estimated_time": null,
  //     "description":
  //         "Current sport face instead wonder chance for now. Seek purpose fish writer buy. Finally ever stand group.\nElse shoulder program campaign. White age try blood condition decade as.",
  //     "reoccurrence": "Daily",
  //     "notify_repair": true,
  //     "maintenance_tasks": [
  //       {
  //         "id": "93729",
  //         "template_id": "15",
  //         "job_id": "4806",
  //         "name": "Backwash filter",
  //         "last_done_on": null,
  //         "value": null,
  //         "is_required": false,
  //         "historical_values": [
  //           {"date": "2021-11-11", "value": 6},
  //           {"date": "2021-11-18", "value": 7},
  //           {"date": "2021-11-25", "value": 8}
  //         ]
  //       },
  //       {
  //         "id": "53952",
  //         "template_id": "32",
  //         "job_id": "4806",
  //         "name": "Filter Type",
  //         "last_done_on": "2021-11-18",
  //         "value": null,
  //         "is_required": false,
  //         "historical_values": [
  //           {"date": "2021-11-11", "value": null},
  //           {"date": "2021-11-18", "value": "Cartridge"},
  //           {"date": "2021-11-25", "value": null}
  //         ]
  //       },
  //       {
  //         "id": "85531",
  //         "template_id": "38",
  //         "job_id": "4806",
  //         "name": "Metal control",
  //         "last_done_on": null,
  //         "value": "96",
  //         "is_required": false,
  //         "historical_values": [
  //           {"date": "2021-11-11", "value": 2},
  //           {"date": "2021-11-18", "value": 4},
  //           {"date": "2021-11-25", "value": 1}
  //         ]
  //       },
  //     ],
  //     "repair_parts": null
  //   },
  //   "events": [],
  //   "comments": []
  // });

  Future<void> _onLoaded(
    HistoryLoaded event,
    Emitter<HistoryState> emit,
  ) async {
    List<DateTime> dateTimeList = [];
    List<MaintenanceTask> maintenanceTasks = List.from(
      event.job.details.maintenanceTasks,
    );
    for (final MaintenanceTask task in maintenanceTasks) {
      final historyList = task.historicalValues;
      for (final history in historyList) {
        if (!dateTimeList.any(
          (element) => element.compareTo(history.date) == 0,
        )) {
          dateTimeList.add(history.date);
        }
      }
    }
    dateTimeList.sort((a, b) => a.compareTo(b));
    emit(
      state.copyWith(
        dateTimeList: dateTimeList,
        maintenanceTasks: maintenanceTasks,
      ),
    );
  }
}
