import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:mime/mime.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_event.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_state.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/task_service/model/job_repair_image_task.dart';
import 'package:ticking_app/services/task_service/model/job_repair_task.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/uuid_util.dart';

///Bloc for repair ticket
class RepairRequestsBloc
    extends Bloc<RepairRequestsEvent, RepairRequestsState> {
  RepairRequestsBloc({
    JobApi? jobApi,
    TickingLogService? logService,
    TaskService? taskService,
    CacheService? cacheService,
    SecureConfigService? secureConfigService,
  }) : super(const RepairRequestsState()) {
    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _tickingLogService.setClassName(toString());
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
    on<RepairRequestsLoaded>(_onLoaded);
    on<RepairTicketImagesInserted>(_onImagesInserted);
    on<RepairRequestsPoolIsDownChanged>(_onPoolIsDownChanged);
    on<RepairRequestsDrainChanged>(_onDrainChanged);
    on<RepairRequestsReplasterChanged>(_onReplasterChanged);
    on<RepairRequestsFirstVisitChanged>(_onFirstVisitChanged);
    on<RepairRequestsSubmitted>(_onRequestsSubmitted);
  }

  late CacheService _cacheService;
  late final JobApi _jobApi;
  late TickingLogService _tickingLogService;
  late final TaskService _taskService;
  late final SecureConfigService _secureConfigService;

  Future<void> _onLoaded(
    RepairRequestsLoaded event,
    Emitter<RepairRequestsState> emit,
  ) async {
    emit(state.copyWith(
      status: RepairRequestsStatus.load,
      uInt8Lists: [],
      error: null,
      job: event.job,
      poolIsDown: false,
      firstVisit: false,
      replaster: false,
      drain: false,
      filePathList: [],
    ));
  }

  Future<void> _onPoolIsDownChanged(
    RepairRequestsPoolIsDownChanged event,
    Emitter<RepairRequestsState> emit,
  ) async {
    emit(state.copyWith(poolIsDown: !state.poolIsDown));
  }

  Future<void> _onDrainChanged(
    RepairRequestsDrainChanged event,
    Emitter<RepairRequestsState> emit,
  ) async {
    final newValue = !state.drain;
    if (newValue == false) {
      emit(state.copyWith(
        drain: false,
      ));
      return;
    }
    emit(state.copyWith(
      drain: true,
      replaster: false,
    ));
  }

  Future<void> _onReplasterChanged(
    RepairRequestsReplasterChanged event,
    Emitter<RepairRequestsState> emit,
  ) async {
    final newValue = !state.replaster;
    if (newValue == false) {
      emit(state.copyWith(
        replaster: false,
      ));
      return;
    }
    emit(state.copyWith(
      drain: false,
      replaster: true,
    ));
  }

  Future<void> _onFirstVisitChanged(
    RepairRequestsFirstVisitChanged event,
    Emitter<RepairRequestsState> emit,
  ) async {
    emit(state.copyWith(firstVisit: !state.firstVisit));
  }

  Future<void> _onImagesInserted(
    RepairTicketImagesInserted event,
    Emitter<RepairRequestsState> emit,
  ) async {
    await EasyLoading.show(status: '');

    List<String> files = state.filePathList ?? [];
    List<Uint8List> uInt8List = state.uInt8Lists ?? [];
    for (int i = 0; i < event.files.length; i++) {
      final File file = File(event.files[i].path);
      Uint8List bytes = await file.readAsBytes();
      uInt8List.add(bytes);
      files.add(event.files[i].path);
    }

    emit(
      state.copyWith(
        status: RepairRequestsStatus.imageInsertSuccess,
        uInt8Lists: uInt8List,
        filePathList: files,
      ),
    );

    await EasyLoading.dismiss();
  }

  // Future<Uint8List> _compressImage(
  //     String filePath, double targetFileSizeMB) async {
  //   try {
  //     // Recording the start time
  //     DateTime startTime = DateTime.now();

  //     ImageProperties properties =
  //         await FlutterNativeImage.getImageProperties(filePath);
  //     final imageBytes = await File(filePath).readAsBytes();
  //     // Uint8List newImageBytes = Uint8List.fromList(imageBytes);
  //     int imageSizeInBytes = imageBytes.length;
  //     double imageSizeInMB = imageSizeInBytes / 1048576.0;
  //     // double ratio = 1;
  //     // width: (image.width * ratio).floor(),
  //     //   height: (image.height * ratio).floor(),

  //     if (imageSizeInMB <= targetFileSizeMB) {
  //       return imageBytes;
  //     }

  //     double ratio = 0.1;
  //     // if (imageSizeInMB > targetFileSizeMB) {
  //     //   ratio = targetFileSizeMB / imageSizeInMB;
  //     // }

  //     final width = properties.width ?? 800;
  //     final height = properties.height ?? 800;
  //     final targetWidth = (width * ratio).floor();
  //     final targetHeight = (height * ratio).floor();
  //     final compressFile = await FlutterNativeImage.compressImage(
  //       filePath,
  //       quality: 100,
  //       targetWidth: targetWidth,
  //       targetHeight: targetHeight,
  //     );

  //     // Recording the end time
  //     DateTime endTime = DateTime.now();
  //     int timeTakenInMillis = endTime.difference(startTime).inMilliseconds;

  //     _tickingLogService.info('CompressImage: $timeTakenInMillis');
  //     return await compressFile.readAsBytes();
  //   } catch (e) {
  //     rethrow;
  //   }
  // }

  Future<void> _onRequestsSubmitted(
    RepairRequestsSubmitted event,
    Emitter<RepairRequestsState> emit,
  ) async {
    String? requestId;
    await EasyLoading.show(status: '');

    List<String> filePathList = state.filePathList ?? [];
    try {
      final configuration = _secureConfigService.configuration;

      if (configuration == null) {
        throw Exception('Can not find configuration');
      }

      ApiResult<RepairRequest> result = await _jobApi.requestsRepair(
        jobId: event.job.id,
        description: event.description,
        poolIsDown: state.poolIsDown,
        firstVisit: state.firstVisit,
        replaster: state.replaster,
        drain: state.drain,
        reason: event.reason,
      );

      final double threshold = configuration.imageSizeThreshold.value;

      requestId = result.data.id;

      for (int i = 0; i < filePathList.length; i++) {
        final filePath = filePathList[i];
        final resizeBytes = await compressImage(filePath, threshold);

        // Recording the start time
        // DateTime startTime = DateTime.now();

        final base64Image = base64.encode(Uint8List.fromList(resizeBytes));

        // Recording the end time
        // DateTime endTime = DateTime.now();

        // int timeTakenInMillis = endTime.difference(startTime).inMilliseconds;

        // _tickingLogService.info('Base64: $timeTakenInMillis');

        String? mimeType = lookupMimeType(filePath);

        if (mimeType == null || mimeType == '') {
          mimeType = 'image/jpeg';
        }
        try {
          // startTime = DateTime.now();

          await _jobApi.requestsRepairImages(
            content: JobImage(
              mimetype: mimeType,
              src: base64Image,
            ),
            repairRequestId: requestId,
          );

          // endTime = DateTime.now();

          // timeTakenInMillis = endTime.difference(startTime).inMilliseconds;

          // _tickingLogService.info('Upload: $timeTakenInMillis');
        } catch (e, stack) {
          if (e is SocketException || e is ApiTimeoutException) {
            const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
            final now = DateTime.now();
            final taskId = '${uuid.v4()}_${now.millisecond.toString()}';

            DataTask task = DataTask(
              taskId: taskId,
              task: JobRepairImageTask(
                base64: base64Image,
                repairId: requestId,
                mine: mimeType,
              ),
              type: TaskType.repairImageTask,
            );

            _taskService.addDataTask(task);

            _cacheService.addDataTask(task);
          } else {
            emit(
              state.copyWith(
                status: RepairRequestsStatus.failure,
                error: e.toString(),
              ),
            );
          }

          _tickingLogService.error(
              'RepairRequestsSubmitted', e.toString(), stack);
        }
      }

      // for (int i = 0; i < uInt8List.length; i++) {
      //   int byteSize = uInt8List[i].lengthInBytes;

      //   double byteInMB = byteSize / 1048576.0;
      //   late String base64Image;

      //   double ratio = 1;
      //   if (byteInMB > threshold) {
      //     ratio = threshold / byteInMB;
      //   }

      //   try {
      //     await _jobApi.requestsRepairImages(
      //         content: JobImage(
      //           mimetype: 'image/jpeg',
      //           src: base64Image,
      //         ),
      //         repairRequestId: requestId);
      //   } catch (e, stack) {
      //     if (e is SocketException || e is ApiTimeoutException) {
      //       const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
      //       final now = DateTime.now();
      //       final taskId = '${uuid.v4()}_${now.millisecond.toString()}';

      //       DataTask task = DataTask(
      //         taskId: taskId,
      //         task:
      //             JobRepairImageTask(base64: base64Image, repairId: requestId),
      //         type: TaskType.repairImageTask,
      //       );

      //       _taskService.addDataTask(task);

      //       _cacheService.addDataTask(task);
      //     } else {
      //       emit(
      //         state.copyWith(
      //           status: RepairRequestsStatus.failure,
      //           error: e.toString(),
      //         ),
      //       );
      //     }

      //     _tickingLogService.error(
      //         'RepairRequestsSubmitted', e.toString(), stack);
      //   }
      // }
      await EasyLoading.dismiss();
      emit(state.copyWith(status: RepairRequestsStatus.submitSuccess));
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        _handleSubmitOffline(event);

        emit(state.copyWith(status: RepairRequestsStatus.submitLocalSave));
        await EasyLoading.dismiss();

        return;
      }

      emit(
        state.copyWith(
          status: RepairRequestsStatus.failure,
          error: e.toString(),
        ),
      );
      await EasyLoading.dismiss();

      _tickingLogService.error('RepairRequestsSubmitted', e.toString(), stack);
    }
  }

  void _handleSubmitOffline(RepairRequestsSubmitted event) {
    List<Uint8List> uInt8List = state.uInt8Lists ?? [];
    List<String> mines = state.filePathList ?? [];
    const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
    final now = DateTime.now();
    final taskId = '${uuid.v4()}_${now.millisecond.toString()}';

    DataTask task = DataTask(
      taskId: taskId,
      task: JobRepairTask(
        mines: mines,
        reason: event.reason,
        description: event.description,
        jobId: event.job.id,
        images: uInt8List,
        drain: state.drain,
        replaster: state.replaster,
        isRepairRequest: true,
        firstVisit: state.firstVisit,
        poolIsDown: state.poolIsDown,
      ),
      type: TaskType.jobRepairTask,
    );

    _cacheService.addDataTask(task);

    _taskService.addDataTask(task);
  }
}
