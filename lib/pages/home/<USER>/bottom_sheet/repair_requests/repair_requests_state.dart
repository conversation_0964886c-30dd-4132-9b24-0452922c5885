import 'dart:typed_data';
import 'package:ticking_api_client/ticking_api_client.dart';

class RepairRequestsState {
  const RepairRequestsState({
    this.job,
    this.status = RepairRequestsStatus.load,
    this.uInt8Lists,
    this.error,
    this.firstVisit = false,
    this.poolIsDown = false,
    this.drain = false,
    this.replaster = false,
    this.filePathList,
  });

  RepairRequestsState copyWith({
    Job? job,
    RepairRequestsStatus? status,
    List<Uint8List>? uInt8Lists,
    List<String>? filePathList,
    String? error,
    bool? firstVisit,
    bool? poolIsDown,
    bool? drain,
    bool? replaster,
  }) {
    return RepairRequestsState(
      firstVisit: firstVisit ?? this.firstVisit,
      poolIsDown: poolIsDown ?? this.poolIsDown,
      replaster: replaster ?? this.replaster,
      drain: drain ?? this.drain,
      status: status ?? this.status,
      job: job ?? this.job,
      error: error ?? this.error,
      uInt8Lists: uInt8Lists ?? this.uInt8Lists,
      filePathList: filePathList ?? this.filePathList,
    );
  }

  final Job? job;
  final String? error;
  final RepairRequestsStatus status;
  final List<Uint8List>? uInt8Lists;
  final List<String>? filePathList;
  final bool firstVisit;
  final bool poolIsDown;
  final bool drain;
  final bool replaster;

  @override
  String toString() {
    return 'RepairRequestsState(job: $job, error: $error, status: $status)';
  }
}

enum RepairRequestsStatus {
  loading,
  load,
  failure,
  imageInsertSuccess,
  submitSuccess,
  submitLocalSave,
}
