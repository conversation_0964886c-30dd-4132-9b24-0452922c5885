import 'package:image_picker/image_picker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';

abstract class RepairRequestsEvent {}

///Load data
class RepairRequestsLoaded extends RepairRequestsEvent {
  RepairRequestsLoaded({required this.job});

  final Job job;

  @override
  String toString() {
    return 'RepairRequestsLoaded{job: $job}';
  }
}

///Insert image to screen
class RepairTicketImagesInserted extends RepairRequestsEvent {
  RepairTicketImagesInserted({required this.files});

  final List<XFile> files;

  @override
  String toString() {
    return 'RepairTicketImagesInserted{url: $files}';
  }
}

class RepairRequestsPoolIsDownChanged extends RepairRequestsEvent {
  RepairRequestsPoolIsDownChanged();

  @override
  String toString() {
    return 'RepairRequestsPoolIsDownChanged{}';
  }
}

class RepairRequestsReplasterChanged extends RepairRequestsEvent {
  RepairRequestsReplasterChanged();

  @override
  String toString() {
    return 'RepairRequestsReplasterChanged{}';
  }
}

class RepairRequestsFirstVisitChanged extends RepairRequestsEvent {
  RepairRequestsFirstVisitChanged();

  @override
  String toString() {
    return 'RepairRequestsFirstVisitChanged{}';
  }
}

class RepairRequestsDrainChanged extends RepairRequestsEvent {
  RepairRequestsDrainChanged();

  @override
  String toString() {
    return 'RepairRequestsDrainChanged{}';
  }
}

class RepairRequestsSubmitted extends RepairRequestsEvent {
  final Job job;
  final String description;
  final String reason;

  RepairRequestsSubmitted({
    required this.job,
    required this.description,
    required this.reason,

  });
}
