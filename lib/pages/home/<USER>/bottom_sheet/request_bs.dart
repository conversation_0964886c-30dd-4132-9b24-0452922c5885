import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_event.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_state.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/utils/extend/list_extend.dart';
import 'package:ticking_app/utils/toast_utils.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/drop_down/app_drop_down.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

import '../../../../widgets/image_area_widget.dart';

void showRequestBS({
  BuildContext? context,
  // Function(String)? onSubmitted,
  required Job job,
}) {
  showModalBottomSheet(
    context: context ?? App.overlayContext!,
    builder: (context) => _RequestBottomSheet(
      // onSubmitted: onSubmitted,
      job: job,
    ),
    // backgroundColor: Colors.green,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(Dimens.rad_XXXSL),
        topRight: Radius.circular(Dimens.rad_XXXSL),
      ),
    ),
  );
}

class _RequestBottomSheet extends StatefulWidget {
  const _RequestBottomSheet({Key? key, required this.job}) : super(key: key);
  // final Function(String)? onSubmitted;
  final Job job;
  @override
  _RequestBottomSheetState createState() => _RequestBottomSheetState();
}

class _RequestBottomSheetState extends State<_RequestBottomSheet> {
  final TextEditingController _controller = TextEditingController();
  late final RepairRequestsBloc _bloc;
  bool firstOpen = true;
  late final SecureConfigService _secureConfigService;
  late final List<String> _repairRequestReason;
  String _currentValue = "";
  @override
  void initState() {
    _bloc = BlocProvider.of<RepairRequestsBloc>(context);
    _bloc.add(RepairRequestsLoaded(job: widget.job));
    _secureConfigService = GetIt.I<SecureConfigService>();
    _repairRequestReason =
        _secureConfigService.configuration?.repairRequestReasons ?? [];
    if (_repairRequestReason.isNotEmpty) {
      if (_repairRequestReason[0] != "") {
        _repairRequestReason.insert(0, "");
      }
    }
    super.initState();
  }

  @override
  void dispose() {
    // _bloc.close();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<RepairRequestsBloc, RepairRequestsState>(
      listener: (context, state) {
        if (state.status == RepairRequestsStatus.failure) {
          DialogHelper.showError(content: state.error ?? '');
          return;
        }

        if (state.status == RepairRequestsStatus.submitSuccess) {
          ToastUtils.showToast(
            msg: 'Submitted successfully!',
            icon: Assets.icon.check.svg().pr(value: 10),
          );

          App.pop();
          return;
        }

        if (state.status == RepairRequestsStatus.submitLocalSave) {
          ToastUtils.showToast(
            msg: 'Saved locally.',
            icon: Assets.icon.check.svg().pr(value: 10),
          );

          App.pop();
          return;
        }
      },
      builder: (context, state) {
        return SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 20),
                Text(
                  'Repair request',
                  style: Theme.of(context)
                      .textTheme
                      .bodyText1
                      ?.copyWith(color: AppColors.black, fontSize: 18),
                ),
                const SizedBox(height: 12),
                const ImageAreaWidget(),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        'Pool is down?',
                        style: Theme.of(context)
                            .titleTextStyle
                            .copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                    FlutterSwitch(
                      width: 60.0,
                      height: 28.0,
                      value: state.poolIsDown,
                      borderRadius: 40.0,
                      activeColor: AppColors.primaryColor,
                      inactiveColor: Colors.white,
                      inactiveToggleColor: AppColors.grey3,
                      activeToggleColor: Colors.white,
                      padding: 4.0,
                      switchBorder: Border.all(
                        color: AppColors.grey3,
                        width: 1.0,
                      ),
                      onToggle: (val) {
                        _bloc.add(RepairRequestsPoolIsDownChanged());
                      },
                    ).pl(value: 8),
                    SizedBox(
                      width: 100,
                      child: Text(
                        'First Visit?',
                        style: Theme.of(context)
                            .titleTextStyle
                            .copyWith(fontWeight: FontWeight.w700),
                      ).pl(value: 16),
                    ),
                    FlutterSwitch(
                      width: 60.0,
                      height: 28.0,
                      value: state.firstVisit,
                      borderRadius: 40.0,
                      activeColor: AppColors.primaryColor,
                      inactiveColor: Colors.white,
                      inactiveToggleColor: AppColors.grey3,
                      activeToggleColor: Colors.white,
                      padding: 4.0,
                      switchBorder: Border.all(
                        color: AppColors.grey3,
                        width: 1.0,
                      ),
                      onToggle: (val) {
                        _bloc.add(RepairRequestsFirstVisitChanged());
                      },
                    ).pl(value: 8),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        'Replaster?',
                        style: Theme.of(context)
                            .titleTextStyle
                            .copyWith(fontWeight: FontWeight.w700),
                      ),
                    ),
                    FlutterSwitch(
                      width: 60.0,
                      height: 28.0,
                      value: state.replaster,
                      borderRadius: 40.0,
                      activeColor: AppColors.primaryColor,
                      inactiveColor: Colors.white,
                      inactiveToggleColor: AppColors.grey3,
                      activeToggleColor: Colors.white,
                      padding: 4.0,
                      switchBorder: Border.all(
                        color: AppColors.grey3,
                        width: 1.0,
                      ),
                      onToggle: (val) {
                        _bloc.add(RepairRequestsReplasterChanged());
                      },
                    ).pl(value: 8),
                    SizedBox(
                      width: 100,
                      child: Text(
                        'Drain?',
                        style: Theme.of(context)
                            .titleTextStyle
                            .copyWith(fontWeight: FontWeight.w700),
                      ).pl(value: 16),
                    ),
                    FlutterSwitch(
                      width: 60.0,
                      height: 28.0,
                      value: state.drain,
                      borderRadius: 40.0,
                      activeColor: AppColors.primaryColor,
                      inactiveColor: Colors.white,
                      inactiveToggleColor: AppColors.grey3,
                      activeToggleColor: Colors.white,
                      padding: 4.0,
                      switchBorder: Border.all(
                        color: AppColors.grey3,
                        width: 1.0,
                      ),
                      onToggle: (val) {
                        _bloc.add(RepairRequestsDrainChanged());
                      },
                    ).pl(value: 8),
                  ],
                ),
                const SizedBox(height: 12),
                if (_repairRequestReason.isNotEmpty)
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.only(top: 8, bottom: 5),
                        width: 100,
                        child: Text(
                          'ER Reason',
                          style: Theme.of(context)
                              .titleTextStyle
                              .copyWith(fontWeight: FontWeight.w700),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: 20,
                        ),
                        child: AppDropDown(
                          color: AppColors.grey4,
                          textColor: AppColors.black1,
                          iconColor: AppColors.grey12,
                          groupNameList: _repairRequestReason,
                          // margin: const EdgeInsets.only(left: 16, right: 16),
                          onSelectedItem: (index) {
                            setState(() {
                              _currentValue = _repairRequestReason[index];
                              if (_repairRequestReason[0] == "") {
                                _repairRequestReason.removeAt(0);
                              }
                            });
                          },
                          index: _repairRequestReason.indexWhere(
                              (element) => element == _currentValue),
                        ),
                      ),
                    ],
                  ),
                OutLineTextField(
                  autoFocus: true,
                  controller: _controller,
                  onChanged: (_) {
                    setState(() {});
                  },
                  maxLines: 6,
                  scrollPadding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  radius: 10,
                  hintText: 'Type repair request notes here.',
                  backgroundColor: AppColors.grey4,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  showBorder: false,
                  hintStyle: Theme.of(context)
                      .textTheme
                      .bodyText2
                      ?.copyWith(color: AppColors.grey),
                  // onFieldSubmitted: widget.onSubmitted,
                ).pOnly(bottom: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CommonOutlineButton(
                      text: 'CANCEL',
                      padding: const EdgeInsets.symmetric(
                        vertical: 14,
                        horizontal: 25,
                      ),
                      fontWeight: FontWeight.w700,
                      onPressed: () => App.pop(),
                    ),
                    12.toHSizeBox(),
                    Btn.main(
                      text: 'SUBMIT',
                      fontSize: 16,
                      style: (_controller.text.isEmpty ||
                              _controller.text.isEmpty ||
                              state.uInt8Lists == null ||
                              state.uInt8Lists!.isEmpty)
                          ? AppButtonStyle.ghostStyle(
                              context,
                              props: BtnStyleProps(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 16, horizontal: 25),
                              ),
                            ).copyWith(
                              shape: MaterialStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.circular(Dimens.rad),
                                ),
                              ),
                            )
                          : AppButtonStyle.primaryStyle(
                              context,
                              props: BtnStyleProps(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 16, horizontal: 25),
                              ),
                            ).copyWith(
                              shape: MaterialStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.circular(Dimens.rad),
                                ),
                              ),
                            ),
                      onPressed: (_controller.text.isEmpty ||
                              _controller.text.isEmpty ||
                              state.uInt8Lists == null ||
                              state.uInt8Lists!.isEmpty)
                          ? null
                          : () {
                              FocusScope.of(context).unfocus();
                              _bloc.add(
                                RepairRequestsSubmitted(
                                  job: widget.job,
                                  description: _controller.text,
                                  reason: _currentValue,
                                ),
                              );
                            },
                    ),
                  ],
                ),
                SizedBox(height: MediaQuery.of(context).viewInsets.bottom + 20),
              ],
            ),
          ),
        );
      },
    );
  }
}
