import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/edit_comment_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/complete_job/list/bloc/complete_job_list_bloc.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/repair_ticket/bloc/repair_ticket_bloc.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

enum CommentAction { delete, edit }

class TicketTab extends StatefulWidget {
  const TicketTab({
    Key? key,
    required this.job,
    required this.isKeyBoardVisible,
    this.jobDetailsBloc,
    this.isStart = false,
    this.onCommentDeleted,
    this.onCommentEdit,
    this.onCommentPosted,
    this.repairTicketBloc,
    this.completeJobListBloc,
  }) : super(key: key);
  final bool isKeyBoardVisible;
  final bool isStart;
  final JobDetailsBloc? jobDetailsBloc;
  final Job job;
  final Function(JobComment comment, Job job)? onCommentDeleted;
  final Function(String comment, Job job)? onCommentPosted;
  final Function(JobComment comment, Job job, String body)? onCommentEdit;
  final RepairTicketBloc? repairTicketBloc;
  final CompleteJobListBloc? completeJobListBloc;

  @override
  State<TicketTab> createState() => _TicketTabState();
}

class _TicketTabState extends State<TicketTab> {
  final TextEditingController _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onCommentSubmit(BuildContext context, String value) {
    FocusScope.of(context).unfocus();
    if (value.isEmptyOrNull && value.trim() == '') return;
    _controller.clear();

    if (widget.onCommentPosted != null) {
      widget.onCommentPosted?.call(value, widget.job);
    } else {
      if (widget.isStart) {
        widget.jobDetailsBloc?.add(JobDetailsCommentPosted(value));
      } else if (widget.repairTicketBloc != null) {
        widget.repairTicketBloc?.add(
          RepairTicketCommentPosted(
            job: widget.job,
            comment: value,
          ),
        );
      } else {
        context.read<JobListBloc>().add(
              JobCommentPosted(
                job: widget.job,
                comment: value,
              ),
            );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // final isKeyBoardVisible = MediaQuery.of(context).viewInsets.bottom > 0;
    return Column(
      children: [
        OutLineTextField(
          controller: _controller,
          scrollPadding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          radius: 66,
          hintText: 'Your comment',
          backgroundColor: AppColors.grey4,
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          showBorder: false,
          hintStyle: Theme.of(context)
              .textTheme
              .bodyText2
              ?.copyWith(color: AppColors.grey),
          onFieldSubmitted: (value) => _onCommentSubmit(context, value),
        ).pt8(),
        Gaps.vGap16,
        widget.isKeyBoardVisible
            ? _wComments(context, widget.isKeyBoardVisible)
            : _wComments(context, widget.isKeyBoardVisible).expand()
      ],
    );
  }

  Widget _buildComments(List<JobComment> comments, bool isKeyBoardVisible) {
    return Scrollbar(
      child: ListView.separated(
        separatorBuilder: (context, index) => Gaps.vGap16,
        itemCount: comments.length,
        shrinkWrap: true,
        padding: const EdgeInsets.only(bottom: 20),
        physics:
            isKeyBoardVisible ? const NeverScrollableScrollPhysics() : null,
        itemBuilder: (context, index) => _wCommentItem(
          context,
          comments[index],
        ),
      ),
    );
  }

  Widget _wComments(BuildContext context, bool isKeyBoardVisible) {
    if (widget.completeJobListBloc != null) {
      return BlocBuilder<CompleteJobListBloc, CompleteJobListState>(
        bloc: widget.completeJobListBloc,
        builder: (context, state) {
          List<JobComment> comments = state.detailJob?.comments ?? [];
          return _buildComments(
            comments,
            isKeyBoardVisible,
          );
        },
      );
    } else if (widget.isStart) {
      return BlocBuilder<JobDetailsBloc, JobDetailsState>(
        bloc: widget.jobDetailsBloc,
        builder: (context, state) {
          return _buildComments(
            state.data.job.comments,
            isKeyBoardVisible,
          );
        },
      );
    } else if (widget.repairTicketBloc != null) {
      return BlocBuilder<RepairTicketBloc, RepairTicketState>(
        bloc: widget.repairTicketBloc,
        builder: (context, state) {
          List<JobComment> comments = state.job?.comments ?? [];
          return _buildComments(
            comments,
            isKeyBoardVisible,
          );
        },
      );
    }

    return BlocBuilder<JobListBloc, JobListState>(builder: (context, state) {
      final jobList = state.data.jobList;
      final curJobIndex =
          jobList.indexWhere((element) => element.id == widget.job.id);
      if (curJobIndex == -1) return const SizedBox();
      final jobComments = state.data.jobList[curJobIndex].comments;
      return _buildComments(jobComments, isKeyBoardVisible);
    });
  }

  Widget _wCommentItem(BuildContext context, JobComment jobComment) {
    final canEdit = widget.job.employee.name == jobComment.employee.name;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ClipOval(
                child: Assets.icon.noAvatar
                    .svg(width: 32, height: 32)
                    .backgroundColor(AppColors.grey)),
            Gaps.hGap8,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  (jobComment.employee.name)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .bold
                      .make(),
                  Gaps.vGap4,
                  TimeUtils.dateTimeToStr(jobComment.date)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .fontWeight(FontWeight.w600)
                      .color(AppColors.selectColor)
                      .make()
                ],
              ),
            ),
            if (canEdit)
              PopupMenuButton<String>(
                child: Assets.icon.moreHorizontal.svg(),
                onSelected: (String result) {
                  switch (result) {
                    case 'edit':
                      App.pushNamed(
                        AppRoutes.editComment,
                        EditCommentArg(
                          comment: jobComment,
                          job: widget.job,
                          onCommentEdit: widget.onCommentEdit,
                        ),
                      );
                      break;

                    case 'delete':
                      widget.onCommentDeleted?.call(jobComment, widget.job);
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  PopupMenuItem<String>(
                    value: 'edit',
                    child: Row(
                      children: [
                        Assets.icon.edit.svg(width: 20, height: 20),
                        10.toHSizeBox(),
                        // Assets.icon.edit.svg(),
                        Text(
                          'Edit',
                          style: Theme.of(context)
                              .textTheme
                              .bodyText1
                              ?.copyWith(fontSize: 18),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        Assets.icon.delete.svg(width: 20, height: 20),
                        10.toHSizeBox(),
                        Text(
                          'Delete',
                          style: Theme.of(context)
                              .textTheme
                              .bodyText1
                              ?.copyWith(fontSize: 18),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            // CircleButton(
            //   onPressed: () {},
            //   child: Assets.icon.moreHorizontal.svg(),
            // )
          ],
        ),
        Gaps.vGap4,
        (jobComment.body)
            .text
            .bodyText2(context)
            .size(Dimens.text)
            .fontWeight(FontWeight.normal)
            .lineHeight(1.5)
            .make(),
      ],
    );
  }
}
