import 'package:flutter/material.dart';
import 'package:ticking_app/all_file/all_file.dart';

class SpecialRequestTab extends StatefulWidget {
  const SpecialRequestTab({
    Key? key,
    this.text,
  }) : super(key: key);
  final String? text;
  @override
  State<SpecialRequestTab> createState() => _SpecialRequestTabState();
}

class _SpecialRequestTabState extends State<SpecialRequestTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        child: (widget.text??"")
            .text
            .bodyText2(context)
            .size(Dimens.text)
            .fontWeight(FontWeight.normal)
            .lineHeight(1.5)
            .make(),
      ),
    );
  }
}
