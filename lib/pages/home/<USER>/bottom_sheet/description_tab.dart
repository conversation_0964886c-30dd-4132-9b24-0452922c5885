import 'package:flutter/material.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';

class DescriptionTab extends StatelessWidget {
  const DescriptionTab({Key? key, required this.job}) : super(key: key);

  final Job job;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        child: ('${job.details.description}')
            .text
            .bodyText2(context)
            .size(Dimens.text)
            .fontWeight(FontWeight.normal)
            .lineHeight(1.5)
            .make(),
      ),
    );
  }
}
