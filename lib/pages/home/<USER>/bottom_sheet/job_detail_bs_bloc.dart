import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:ticking_api_client/ticking_api_client.dart';

part 'job_detail_bs_event.dart';

part 'job_detail_bs_state.dart';

class JobDetailBsBloc extends Bloc<JobDetailBsEvent, JobDetailBsState> {
  JobDetailBsBloc() : super(const JobDetailBsState(0)) {
    on<JobTabChanged>(_onTabChanged);
  }

  Future<void> _onTabChanged(
      JobTabChanged event,
      Emitter<JobDetailBsState> emit,
      ) async {
    emit(JobDetailBsState(event.page));
  }
}
