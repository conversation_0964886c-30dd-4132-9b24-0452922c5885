import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:simple_connection_checker/simple_connection_checker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/app_single_ton.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/model/service_type.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/access_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/description_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/request_bs.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/ticket_tab.dart';
import 'package:ticking_app/pages/home/<USER>/share/fade_indexed_stack.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_issue_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/on_the_way_widget.dart';
import 'package:ticking_app/pages/home/<USER>/tab/parts_tab.dart';
import 'package:ticking_app/pages/repair_ticket/bloc/repair_ticket_bloc.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_until.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/app_button.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';
import 'package:ticking_app/widgets/button/circle_shadow_button.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_localizations/ticking_localizations.dart';
import 'package:url_launcher/url_launcher.dart';

import '../share/contact_customer_widget.dart';
import 'history_tab/history_tab.dart';
import 'special_request_tab.dart';

void showJobDetailBS(
    {BuildContext? context,
    required Job job,
    JobDetailsBloc? jobDetailsBloc,
    RepairTicketBloc? repairTicketBloc,
    String? distance,
    required String group,
    required Function onUnableToAccess,
    bool isStart = false,
    SmsTemplate? smsTemplate,
    Function(JobComment comment, Job job)? onCommentDeleted,
    Function(JobComment comment, Job job, String body)? onCommentEdit,
    Function(String comment, Job job)? onCommentPosted,
    Function(JobComment comment, Job job)? onCustomerCommentDeleted,
    Function(JobComment comment, Job job, String body)? onCustomerCommentEdit,
    Function(String comment, Job job)? onCustomerCommentPosted,
    // Function(Job job, String comment)? onRepairPosted,
    Function(Job job)? onCompleteJob,
    bool isDoneButtonEnable = true}) async {
  AppSingleton.setJob(job);
  showModalBottomSheet(
    context: context ?? App.overlayContext!,
    builder: (context) => BlocProvider(
      create: (BuildContext context) => JobDetailBsBloc(),
      child: JobDetailBS(
        job: job,
        isDoneButtonEnable: isDoneButtonEnable,
        distance: distance,
        group: group,
        smsTemplate: smsTemplate,
        isStart: isStart,
        jobDetailsBloc: jobDetailsBloc,
        repairTicketBloc: repairTicketBloc,
        onUnableToAccess: onUnableToAccess,
        onCommentDeleted: onCommentDeleted,
        onCommentEdit: onCommentEdit,
        onCommentPosted: onCommentPosted,
        onCustomerCommentDeleted: onCustomerCommentDeleted,
        onCustomerCommentEdit: onCustomerCommentEdit,
        onCustomerCommentPosted: onCustomerCommentPosted,
        onCompleteJob: onCompleteJob,
        // onRepairPosted: onRepairPosted,
      ),
    ),
    // backgroundColor: Colors.green,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(Dimens.rad_XXXSL),
        topRight: Radius.circular(Dimens.rad_XXXSL),
      ),
    ),
  );
}

class JobDetailBS extends StatefulWidget {
  const JobDetailBS({
    Key? key,
    required this.job,
    required this.distance,
    required this.onUnableToAccess,
    required this.group,
    this.smsTemplate,
    this.jobDetailsBloc,
    this.isStart = false,
    this.onCommentDeleted,
    this.onCommentEdit,
    this.onCommentPosted,
    this.onCustomerCommentDeleted,
    this.onCustomerCommentEdit,
    this.onCustomerCommentPosted,
    this.onCompleteJob,
    // this.onRepairPosted,
    this.repairTicketBloc,
    this.isDoneButtonEnable = true,
  }) : super(key: key);
  final Job job;
  final String? distance;
  final String group;
  final bool isStart;
  final JobDetailsBloc? jobDetailsBloc;
  final SmsTemplate? smsTemplate;
  final Function onUnableToAccess;
  final Function(Job job)? onCompleteJob;
  final Function(JobComment comment, Job job)? onCommentDeleted;
  final Function(String comment, Job job)? onCommentPosted;
  final Function(JobComment comment, Job job, String body)? onCommentEdit;
  final Function(JobComment comment, Job job)? onCustomerCommentDeleted;
  final Function(String comment, Job job)? onCustomerCommentPosted;
  final Function(JobComment comment, Job job, String body)?
      onCustomerCommentEdit;

  // final Function(Job job, String comment)? onRepairPosted;
  final RepairTicketBloc? repairTicketBloc;
  final bool isDoneButtonEnable;

  @override
  State<JobDetailBS> createState() => _JobDetailBSState();
}

class _JobDetailBSState extends State<JobDetailBS> {
  List<JobComment> customerComments = [];
  @override
  void initState() {
    _handleInit();
    super.initState();
  }

  Future<void> _handleInit() async {
    BlocProvider.of<JobListBloc>(context)
        .add(JobCustomerCommentLoad(widget.job));

    if (!widget.isStart) {
      BlocProvider.of<JobListBloc>(context).add(JobListBSUpdated(true));
    }
  }

  @override
  void dispose() {
    if (!widget.isStart) {
      BlocProvider.of<JobListBloc>(App.overlayContext!)
          .add(JobListBSUpdated(false));
    }

    super.dispose();
  }

  void _onTogglePinJob(BuildContext context, bool isSelectedState) {
    if (isSelectedState) {
      BlocProvider.of<JobListBloc>(context).add(JobUnSelected(widget.job));
    } else {
      BlocProvider.of<JobListBloc>(context).add(JobSelected(widget.job));
    }
  }

  // void _onMapSelected(
  //     {required double? latitude, required double? longitude}) async {
  //   final mapUrl =
  //       'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
  //   if (!await launch(mapUrl)) throw 'Could not launch $mapUrl';
  // }

  Future<void> _launchMaps({
    required double? latitude,
    required double? longitude,
  }) async {
    // final street = widget.job.contact.street;
    // final city = widget.job.contact.city;
    // final state = widget.job.contact.state?.name;
    // final zipCode = widget.job.contact.zipCode;
    if (Platform.isIOS) {
      String googleUrl =
          'comgooglemapsurl://maps.google.com/?f=d&daddr=$latitude,$longitude&directionsmode=driving';
      String appleUrl = 'https://maps.apple.com/?sll=$latitude,$longitude';

      if (await canLaunch("comgooglemaps://")) {
        await launch(googleUrl);
      } else if (await canLaunch(appleUrl)) {
        await launch(appleUrl);
      } else {
        MapsLauncher.launchCoordinates(latitude ?? 0, longitude ?? 0);
      }
    } else {
      String googleUrl = 'google.navigation:q=$latitude,$longitude&mode=d';
      await launch(googleUrl);
    }
  }

  void _onTabChanged(BuildContext context, int index) {
    BlocProvider.of<JobDetailBsBloc>(context).add(JobTabChanged(index));
  }

  void _onStartJob(BuildContext context) async {
    final _isConnected = await SimpleConnectionChecker.isConnectedToInternet();
    if (widget.job.details.isLightningService == true && _isConnected == true) {
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                100.toVSizeBox(),
                Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: GestureDetector(
                      onTap: () {
                        App.pop();
                      },
                      child: const Icon(
                        Icons.close,
                        size: 40,
                      )),
                ),
                AlertDialog(
                    shape: const RoundedRectangleBorder(
                        borderRadius: BorderRadius.all(Radius.circular(24.0))),
                    contentPadding: const EdgeInsets.only(top: 10.0),
                    insetPadding: const EdgeInsets.only(left: 10, right: 10),
                    content: Container(
                      width: context.screenWidth * 0.9,
                      height: context.screenHeight * 0.35,
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            'Lightning Service',
                            style: Theme.of(context).normalStyle.copyWith(
                                  color: AppColors.black,
                                  fontSize: 17,
                                  fontWeight: FontWeight.bold,
                                ),
                          ).pt(value: 5),
                          Text(
                            'Lightning Service has been enabled for your area. Would you like to proceed with this job as a Lightning Service?',
                            style: Theme.of(context).normalStyle.copyWith(
                                  color: AppColors.red,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                ),
                          ).pt(value: 5),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CommonOutlineButton(
                                text: 'NO',
                                padding: const EdgeInsets.symmetric(
                                  vertical: 12,
                                  horizontal: 20,
                                ),
                                fontWeight: FontWeight.bold,
                                onPressed: () async {
                                  final result = await putSpecialJob(
                                    isLightningService: false,
                                  );
                                  if (result == true) {
                                    await Future.delayed(
                                      const Duration(seconds: 2),
                                    );
                                    App.pushNamedAndPopUntil(
                                        AppRoutes.home, null, '/');
                                  }
                                },
                              ).expand(),
                              Gaps.hGap32,
                              if (widget.isDoneButtonEnable)
                                CommonPrimaryButton(
                                  child: 'YES'
                                      .text
                                      .textStyle(Theme.of(context)
                                          .textTheme
                                          .bodyText1!)
                                      .size(Dimens.text_XL)
                                      .color(AppColors.materialWhite)
                                      .fontWeight(FontWeight.w600)
                                      .make(),
                                  onPressed: () {
                                    BlocProvider.of<JobListBloc>(context)
                                        .add(JobStarted(widget.job));
                                  },
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: 20,
                                  ),
                                ).expand()
                            ],
                          ),
                        ],
                      ),
                    )),
              ],
            );
          });
      return;
    }

    BlocProvider.of<JobListBloc>(context).add(JobStarted(widget.job));
  }

  Future<bool> putSpecialJob({required bool isLightningService}) async {
    try {
      final _jobApi = GetIt.I<JobApi>();
      await _jobApi.putSpecificJob(
        jobId: widget.job.id,
        isLightningService: isLightningService,
      );
      return true;
    } catch (e) {
      DialogHelper.showError(content: e.toString());
      return false;
    }
  }

  void _onCompleteJob(BuildContext context) {
    widget.onCompleteJob?.call(widget.job) ??
        widget.jobDetailsBloc?.add(
          JobDetailsCompleted(
            request: JobTaskListRequest(
              endDate: DateTime.now(),
              reason: 'complete',
              tasks: [],
              startDate: DateTime.now(),
            ),
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return _wWithKeyBoardVisible(context);
  }

  Widget _wWithKeyBoardVisible(BuildContext context) {
    return SizedBox(
      height: ViewUtils.getPercentHeight(percent: 0.8),
      child: Column(
        children: [
          _wRowNameAndPinIcon(context).px16().pt8(),
          _wBodyWithKeyBoardVisible(context).px16().py8().expand(),
          widget.repairTicketBloc == null
              ? _wFooterButtons(context)
              : _wRepairFooterButtons(context),
        ],
      ),
    );
  }

  Widget _wBodyWithKeyBoardVisible(BuildContext context) {
    return Scrollbar(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _wRowJobInfo(context),
            Gaps.vGap12,
            _wGroupButton(context),
            Gaps.vGap12,
            _wTabBar(context),
            Gaps.vGap8,
            _wBodyOfTabWithKeyBoardVisible(context),
            MediaQuery.of(context).viewInsets.bottom.toVSizeBox(),
            // Gaps.vGap20
          ],
        ),
      ),
    );
  }

  Widget _wRowNameAndPinIcon(BuildContext context) {
    return Row(
      children: [
        // (widget.job.id)
        //     .text
        //     .bodyText1(context)
        //     .fontWeight(FontWeight.bold)
        //     .size(Dimens.text_XXL)
        //     .make(),
        (widget.job.contact.name ?? '')
            .text
            .bodyText1(context)
            .fontWeight(FontWeight.bold)
            .size(Dimens.text_XXL)
            .make()
            .expand(),

        BlocBuilder<JobListBloc, JobListState>(builder: (context, state) {
          final isSelectedState = state.data.selectedJobList
                  .any((element) => element.id == widget.job.id) &&
              !widget.isStart;
          return Row(
            children: [
              CircleButton(
                child: Assets.icon.call.svg(),
                backgroundColor: const Color(0xff3f3fff),
                onPressed: () {
                  showContactCustomerDialog(
                    email: widget.job.contact.email,
                    phoneNumber: widget.job.contact.phone,
                    smsTemplate: null,
                    job: widget.job,
                  );
                },
                size: Dimens.ic_4XL,
              ).pr(value: 10),
              CircleButton(
                child: Assets.icon.pin.svg(),
                backgroundColor: isSelectedState
                    ? Theme.of(context).primaryColor
                    : AppColors.grey2,
                onPressed: widget.isStart
                    ? null
                    : () {
                        _onTogglePinJob(context, isSelectedState);
                      },
                size: Dimens.ic_4XL,
              )
            ],
          );
        }),
      ],
    );
  }

  Widget _wRowJobInfo(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              // mainAxisAlignment: MainAxisAlignment.,
              // crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.home,
                  color: AppColors.grey,
                ),
                Gaps.hGap12,
                widget.job.contact
                    .getFullLocation()
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .lineHeight(1.5)
                    .make()
                    .expand(),
              ],
            ),
            Gaps.vGap12,
            Row(
              children: [
                const Icon(Icons.mail, color: AppColors.grey),
                Gaps.hGap12,
                (widget.job.contact.email ?? '')
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .make()
                    .expand(),
              ],
            ),
            Gaps.vGap12,
            Row(
              children: [
                const Icon(Icons.phone, color: AppColors.grey),
                Gaps.hGap12,
                (widget.job.contact.phone ?? '')
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .make(),
              ],
            ),
            if (widget.job.contact.lastVacuumDate != null) ...[
              Gaps.vGap12,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Assets.icon.calendar.svg(
                    color: AppColors.grey,
                    fit: BoxFit.fill,
                    width: Dimens.ic,
                  ),
                  Gaps.hGap17,
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      'Last Vaccum Date'
                          .text
                          .bodyText2(context)
                          .color(AppColors.grey)
                          .size(Dimens.text)
                          .fontWeight(FontWeight.normal)
                          .make(),
                      Gaps.vGap2,
                      (widget.job.contact.lastVacuumDate ?? '')
                          .text
                          .size(Dimens.text)
                          .fontWeight(FontWeight.normal)
                          .make(),
                    ],
                  ),
                ],
              ),
            ],
            Gaps.vGap12,
            Row(
              children: [
                CircleButton(
                  child: Assets.icon.running.svg(
                    color: AppColors.grey,
                  ),
                  size: Dimens.ic,
                ),
                Gaps.hGap12,
                InkWell(
                  onTap: () async {
                    final _isConnected =
                        await SimpleConnectionChecker.isConnectedToInternet();

                    if (_isConnected == true) {
                      showOnTheWayDialog(
                        job: widget.job,
                      );
                    }
                  },
                  child: ('On the Way')
                      .text
                      .bodyText2(context)
                      .size(Dimens.text_L)
                      .color(AppColors.textIcon)
                      .fontWeight(FontWeight.bold)
                      .make(),
                ),
              ],
            ),
          ],
        ).expand(flex: 7),
        Gaps.hGap16,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            'Due date'
                .text
                .bodyText2(context)
                .color(AppColors.grey)
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .lineHeight(1.5)
                .make(),
            Gaps.vGap2,
            TimeUtils.dateToStr(widget.job.scheduleDate)
                .text
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            if (widget.job.details.estimatedTime != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gaps.vGap8,
                  'Estimate time'
                      .text
                      .bodyText2(context)
                      .color(AppColors.grey)
                      .size(Dimens.text)
                      .fontWeight(FontWeight.normal)
                      .lineHeight(1.5)
                      .make(),
                  Gaps.vGap2,
                  printDuration(
                    Duration(
                      seconds: widget.job.details.estimatedTime!,
                    ),
                  ).text.size(Dimens.text).fontWeight(FontWeight.normal).make(),
                ],
              ),
            Gaps.vGap8,
            'Distance'
                .text
                .bodyText2(context)
                .color(AppColors.grey)
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            Gaps.vGap2,
            (widget.distance ?? '')
                .text
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            Gaps.vGap12,
            CircleShadowButton(
              child: Assets.image.googleMap.image().p8(),
              backgroundColor: Colors.white,
              onPressed: () {
                // _onMapSelected(
                //   latitude: widget.job.contact.gps?.latitude,
                //   longitude: widget.job.contact.gps?.longitude,
                // );

                _launchMaps(
                  latitude: widget.job.contact.gps?.latitude,
                  longitude: widget.job.contact.gps?.longitude,
                );
              },
              size: Dimens.ic_4XL,
            ),
          ],
        ).expand(flex: 3),
      ],
    );
  }

  Widget _wGroupButton(BuildContext context) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      alignment: WrapAlignment.start,
      runAlignment: WrapAlignment.start,
      spacing: 10,
      children: [
        Btn(
          style: AppButtonStyle.filterCleanStyle(context),
          text: widget.job.details.serviceType,
        ),
        if (widget.job.contact.isFiberglassPool == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: "Fiber Glass",
          ),
        if (!widget.job.details.reoccurrence.isEmptyOrNull)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: '${widget.job.details.reoccurrence}',
          ),
        if (widget.job.details.serviceLevel != null)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: widget.job.details.serviceLevel,
          ),

        if (widget.job.contact.filterType !=null && widget.job.contact.filterType != "")
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: widget.job.contact.filterType,
          ),

        if (widget.job.details.isLightningService == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: 'Lightning Service',
          ),



        if (widget.group == 'Overdue')
          Btn(
            style: widget.group == 'Overdue'
                ? AppButtonStyle.overDueStyle(context)
                : AppButtonStyle.filterCleanStyle(context).copyWith(
                    foregroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) =>
                            AppColors.primaryColor2)),
            text: 'Overdue',
          ),
        //TODO: is doing...



        if (widget.job.contact.isSaltWater == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: "Salt Water",
          ),

        if (widget.job.details.specialRequests != null)
          Btn(
            style: AppButtonStyle.specialStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.specialColor)),
            text: 'Special Requests',
          ),
      ],
    ).pb(value: Dimens.rad_S);
  }

  Widget _wTabBar(BuildContext context) {
    return BlocBuilder<JobDetailBsBloc, JobDetailBsState>(
        builder: (context, state) {
      final isShowPartTab = widget.job.details.repairParts.isNotEmpty;
      final isCleaning = widget.job.type.jobServiceType == ServiceType.cleaning;
      int countTab = 3;
      if (isCleaning) {
        countTab++;
      } else if (isShowPartTab) {
        countTab++;
      } else if (widget.job.details.specialRequests != null) {
        countTab++;
      }
      return DefaultTabController(
        length: countTab,
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                    border: Border(
                        bottom:
                            BorderSide(color: AppColors.grey4, width: 2.0))),
              ),
            ),
            SizedBox(
              height: 50,
              child: TabBar(
                tabs: <Widget>[
                  if (widget.job.details.specialRequests != null)
                    const Tab(text: ' Special\nRequests'),
                  const Tab(text: 'Access'),
                  const Tab(text: 'Description'),
                  const Tab(text: 'Ticket'),
                  if (isCleaning)
                    const Tab(text: 'History ')
                  else if (isShowPartTab)
                    const Tab(text: 'Parts'),
                ],
                onTap: (index) {
                  // BlocProvider.of<HomeBloc>(context).add(HomeTabChanged(index));
                  _onTabChanged(context, index);
                },
                indicatorColor: Theme.of(context).primaryColor,
                labelColor: AppColors.primaryColor,
                padding: EdgeInsets.zero,
                labelPadding: EdgeInsets.zero,
                indicatorPadding: EdgeInsets.zero,
                labelStyle: Theme.of(context).textTheme.bodyText1?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontSize: Dimens.font_sp12,
                    fontWeight: FontWeight.bold),
                unselectedLabelColor: AppColors.grey,
                indicator: UnderlineTabIndicator(
                    borderSide: BorderSide(
                        color: Theme.of(context).primaryColor, width: 2.0),
                    insets: const EdgeInsets.symmetric(horizontal: 10.0)),
              ),
            )
          ],
        ),
      );
    });
  }

  Widget _wBodyOfTabWithKeyBoardVisible(BuildContext context) {
    final isShowPartTab = widget.job.details.repairParts.isNotEmpty;
    final isCleaning = widget.job.type.jobServiceType == ServiceType.cleaning;
    return BlocBuilder<JobDetailBsBloc, JobDetailBsState>(
      buildWhen: (_, current) => current is! JobCustomerCommentLoaded,
      builder: (context, state) {
        return FadeIndexedStack(
          index: state.tabIndex,
          duration: const Duration(milliseconds: 200),
          children: [
            if (widget.job.details.specialRequests != null)
              SpecialRequestTab(
                text: widget.job.details.specialRequests,
              ),
            const AccessTab(),
            DescriptionTab(
              job: widget.job,
            ),
            TicketTab(
              job: widget.job,
              isKeyBoardVisible: true,
              isStart: widget.isStart,
              jobDetailsBloc: widget.jobDetailsBloc,
              repairTicketBloc: widget.repairTicketBloc,
              onCommentEdit: widget.onCommentEdit,
              onCommentDeleted: widget.onCommentDeleted,
              onCommentPosted: widget.onCommentPosted,
            ),
            if (isCleaning)
              HistoryTab(
                job: widget.job,
              )
            else if (isShowPartTab)
              PartsTab(
                job: widget.job,
              ),
          ],
        );
      },
    );
  }

  Widget _wRepairFooterButtons(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: AppDecorStyle.topShadowDecor(
          surfaceColor: AppColors.materialWhite,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CommonOutlineButton(
              text: 'REPAIR',
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
              onPressed: () {
                showRequestBS(job: widget.job);
              },
            ),
            12.toHSizeBox(),
            CommonOutlineButton(
              text: 'ISSUE',
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 15),
              onPressed: () {
                handleShowComment(
                  context: context,
                  job: widget.job,
                  smsTemplate: widget.smsTemplate,
                  onNextStep: _handleValidateGps,
                );
                // showIssueDialog(
                //   email: widget.job.contact.email,
                //   phoneNumber: widget.job.contact.phone,
                //   smsTemplate: widget.smsTemplate,
                //   job: widget.job,
                //   onUnableToAccess: (String reason) {
                //     App.pop();
                //     widget.onUnableToAccess();
                //     widget.repairTicketBloc
                //         ?.add(RepairTicketsUnableAccessed(reason));
                //   },
                // );
              },
            ),
            12.toHSizeBox(),
            CommonPrimaryButton(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 60),
              child: 'DONE'
                  .text
                  .textStyle(Theme.of(context).textTheme.bodyText1!)
                  .size(Dimens.text_XL)
                  .color(AppColors.materialWhite)
                  .fontWeight(FontWeight.w600)
                  .make(),
              onPressed: () {
                widget.onCompleteJob?.call(widget.job) ??
                widget.repairTicketBloc?.add(RepairTicketCompleted(
                  request: JobTaskListRequest(
                    endDate: DateTime.now(),
                    reason: 'complete',
                    tasks: [],
                    startDate: DateTime.now(),
                  ),
                ));
              },
            ),
          ],
        ).px16().pOnly(bottom: 10),
      ),
    );
  }

  Future<bool> _validateGps() async {
    final gps = await GetIt.I<LocationService>().getCurrentPosition();
    final configuration = GetIt.I<SecureConfigService>().configuration;
    final jobGps = widget.job.contact.gps;
    if (configuration == null || jobGps == null) {
      DialogHelper.showError(
        content: "Can not get configuration",
      );
      return false;
    }

    final distance = GeoLocatorUtils.distanceBetweenCoordinatesInMiles(
        gps.latitude, gps.longitude, jobGps.latitude, jobGps.longitude);

    if (distance > configuration.issueDistanceThreshold.value) {
      DialogHelper.showError(
        content:
            "Your report location is out of range from the customer's location. Please, move to closer to the customer's location and report the issue request again",
        actions: [
          AppButton(
            borderRadius: Platform.isIOS ? 0 : 8,
            backgroundColor: Platform.isIOS
                ? Colors.transparent
                : Theme.of(context).primaryColor,
            onPressed: () async {
              Navigator.of(context).pop(true);
            },
            child: Center(
              child: Text(
                "Retry",
                style: Theme.of(context).textTheme.bodyText1!.copyWith(
                    color: Platform.isIOS
                        ? Theme.of(context).textColor()
                        : Theme.of(context).backgroundColor),
              ),
            ),
          ),
        ],
        icon: const SizedBox(),
      );
      return false;
    }
    return true;
  }

  Widget _wFooterButtons(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: AppDecorStyle.topShadowDecor(
          surfaceColor: AppColors.materialWhite,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            CommonOutlineButton(
              text: 'REPAIR',
              onPressed: () {
                showRequestBS(
                  // onSubmitted: (description) => widget.onRepairPosted != null
                  //     ? widget.onRepairPosted?.call(widget.job, description)
                  //     : BlocProvider.of<JobListBloc>(context).add(
                  //         JobRepairPosted(
                  //           job: widget.job,
                  //           description: description,
                  //         ),
                  //       ),
                  job: widget.job,
                );
              },
            ),
            Gaps.hGap12,
            CommonOutlineButton(
              text: 'ISSUE',
              onPressed: () {
                // _handleValidateGps();
                handleShowComment(
                  context: context,
                  job: widget.job,
                  smsTemplate: widget.smsTemplate,
                  onNextStep: _handleValidateGps,
                );
              },
            ),
            Gaps.hGap12,
            if (widget.isDoneButtonEnable)
              CommonPrimaryButton(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    (widget.isStart ? 'DONE' : 'START')
                        .text
                        .textStyle(Theme.of(context).textTheme.bodyText1!)
                        .size(Dimens.text_XL)
                        .color(AppColors.materialWhite)
                        .fontWeight(FontWeight.w600)
                        .make(),
                    const Icon(
                      Icons.arrow_forward_ios_sharp,
                      color: AppColors.materialWhite,
                      size: 16,
                    )
                  ],
                ),
                onPressed: () {
                  if (!widget.isStart) {
                    _onStartJob(context);
                  } else {
                    _onCompleteJob(context);
                  }
                },
              ).expand()
          ],
        ).px16().pOnly(bottom: 10),
      ),
    );
  }

  Future<void> _handleValidateGps() async {
    final bool valid = await _validateGps();
    if (valid) {
      showIssueDialog(
        email: widget.job.contact.email,
        phoneNumber: widget.job.contact.phone,
        smsTemplate: widget.smsTemplate,
        job: widget.job,
        onUnableToAccess: (String reason) {
          widget.onUnableToAccess();
          if (widget.isStart) {
            widget.jobDetailsBloc?.add(
              JobDetailsUnableAccessed(reason),
            );
          } else {
            BlocProvider.of<JobListBloc>(context).add(
              JobUnableToAccess(
                job: widget.job,
                reason: reason,
              ),
            );
          }
          App.pushNamed(AppRoutes.home);
        },
      );
    }
  }
}

Future<void> handleShowComment({
  required BuildContext context,
  required Job job,
  required SmsTemplate? smsTemplate,
  required VoidCallback onNextStep,
}) async {
  final comments = AppSingleton.listCustomerComments;
  showIssueDialog(
      email: job.contact.email,
      phoneNumber: job.contact.phone,
      smsTemplate: smsTemplate,
      job: job,
      childWidget: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height *
            (comments.isNotEmpty ? 0.56 : 0.31),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Text(
                'Access',
                style: Theme.of(context).normalStyle.copyWith(
                    color: AppColors.black, fontWeight: FontWeight.bold),
              ).pt(value: 14),
            ),
            (job.contact.accountNote.isNotEmptyAndNotNull)
                ? Padding(
                    padding: const EdgeInsets.all(8),
                    child: Text(
                      '${job.contact.accountNote}',
                      maxLines: 2,
                      style: Theme.of(context).normalStyle.copyWith(
                          color: AppColors.black,
                          fontWeight: FontWeight.normal),
                    ).pt(value: 14),
                  )
                : const SizedBox(
                    height: 10,
                  ),
            if (comments.isNotEmpty)
              Container(
                color: Colors.white,
                width: MediaQuery.of(context).size.width * 0.8,
                height: MediaQuery.of(context).size.height * 0.25,
                padding: const EdgeInsets.all(16),
                child: ListView.separated(
                  separatorBuilder: (context, index) => Gaps.vGap16,
                  itemCount: comments.length,
                  shrinkWrap: true,
                  padding: const EdgeInsets.only(bottom: 20),
                  itemBuilder: (context, index) => _wCommentItem(
                    context,
                    comments[index],
                  ),
                ),
              ),
            Center(
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Text(
                  'Please recheck the Access Notes before reporting lockout/dog out for this job',
                  style: Theme.of(context).normalStyle.copyWith(
                        color: AppColors.error,
                        fontWeight: FontWeight.normal,
                      ),
                ).pt(value: 14),
              ),
            ),
            const Spacer(),
            Center(
              child: CommonPrimaryButton(
                child: ("CONTINUE")
                    .text
                    .textStyle(Theme.of(context).textTheme.bodyText1!)
                    .size(Dimens.text_XL)
                    .color(AppColors.materialWhite)
                    .fontWeight(FontWeight.w600)
                    .make(),
                onPressed: () {
                  App.pop();
                  onNextStep();
                },
              ),
            ),
            const Spacer(),
          ],
        ),
      ),
      onUnableToAccess: (String reason) {
        onNextStep();
      });
}

Widget _wCommentItem(BuildContext context, JobComment jobComment) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Row(
        children: [
          ClipOval(
              child: Assets.icon.noAvatar
                  .svg(width: 32, height: 32)
                  .backgroundColor(AppColors.grey)),
          Gaps.hGap8,
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                (jobComment.employee.name)
                    .text
                    .textStyle(Theme.of(context).textTheme.bodyText2!)
                    .bold
                    .make(),
                Gaps.vGap4,
                TimeUtils.dateTimeToStr(jobComment.date)
                    .text
                    .textStyle(Theme.of(context).textTheme.bodyText2!)
                    .fontWeight(FontWeight.w600)
                    .color(AppColors.selectColor)
                    .make()
              ],
            ),
          ),

          // CircleButton(
          //   onPressed: () {},
          //   child: Assets.icon.moreHorizontal.svg(),
          // )
        ],
      ),
      Gaps.vGap4,
      (jobComment.body)
          .text
          .bodyText2(context)
          .size(Dimens.text)
          .fontWeight(FontWeight.normal)
          .lineHeight(1.5)
          .make(),
    ],
  );
}
