import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/app_single_ton.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

enum CommentAction { delete, edit }

class AccessTab extends StatefulWidget {
  const AccessTab({
    Key? key,
  }) : super(key: key);

  @override
  State<AccessTab> createState() => _AccessTabState();
}

class _AccessTabState extends State<AccessTab> {
  final TextEditingController _controller = TextEditingController();
  final JobApi _jobApi = GetIt.I<JobApi>();
  bool isLoading = false;
  Job? job;
  @override
  void initState() {
    super.initState();
    getData();
  }

  void getData() async {
    setState(() {
      isLoading = true;
    });
    try {
      job = AppSingleton.job;
      final result =
          await _jobApi.createJobCustomerComment(jobId: job?.id ?? "");
      if (result != null) {
        final list = List.from(result as List)
            .map((e) => JobComment.fromJson(Map<String, dynamic>.from(e)))
            .toList();
        AppSingleton.setComments(list);
      }
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onCommentSubmit(BuildContext context, String value) async {
    FocusScope.of(context).unfocus();
    if (value.isEmptyOrNull && value.trim() == '') return;
    _controller.clear();
    final result = await _jobApi.postJobCustomerComment(
      jobId: job?.id ?? "",
      request: JobCommentRequest(
        body: value,
        date: DateTime.now(),
      ),
    );

    AppSingleton.addComment(result.data);
  }

  void _onCommentUpdate(
      BuildContext context, String value, JobComment jobComment) async {
    FocusScope.of(context).unfocus();
    if (value.isEmptyOrNull && value.trim() == '') return;
    _controller.clear();
    final result = await _jobApi.updateJobCustomerComment(
      commentId: jobComment.id,
      jobId: job?.id ?? "",
      request: JobCommentRequest(body: value, date: DateTime.now()),
    );

    AppSingleton.updateComment(result.data);
  }

  void _onCommentDelete(BuildContext context, JobComment jobComment) async {
    FocusScope.of(context).unfocus();

    await _jobApi.deleteJobCustomerComment(
        commentId: jobComment.id,
        jobId: job?.id ?? "",
        dateTime: DateTime.now());

    AppSingleton.deleteComment(jobComment);
  }

  @override
  Widget build(BuildContext context) {
    return isLoading
        ? const Center(child: LoadingIndicator())
        : StreamBuilder<List<JobComment>>(
            stream: AppSingleton.commentsStream,
            initialData: AppSingleton.listCustomerComments,
            builder: (context, snapshot) {
              final comments = snapshot.data ?? [];
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if ((job?.contact.accountNote ?? "").isNotEmptyAndNotNull)
                    Container(
                      child: (job?.contact.accountNote ?? "")
                          .text
                          .bodyText2(context)
                          .size(Dimens.text)
                          .fontWeight(FontWeight.normal)
                          .lineHeight(1.5)
                          .make(),
                    ),
                  OutLineTextField(
                    controller: _controller,
                    scrollPadding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewInsets.bottom),
                    radius: 66,
                    hintText: 'Your comment',
                    backgroundColor: AppColors.grey4,
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 16),
                    showBorder: false,
                    hintStyle: Theme.of(context)
                        .textTheme
                        .bodyText2
                        ?.copyWith(color: AppColors.grey),
                    onFieldSubmitted: (value) =>
                        _onCommentSubmit(context, value),
                  ).pt8(),
                  Gaps.vGap16,
                  _buildComments(comments),
                ],
              );
            },
          );
  }

  Widget _buildComments(List<JobComment> comments) {
    return Scrollbar(
      child: ListView.separated(
        separatorBuilder: (context, index) => Gaps.vGap16,
        itemCount: comments.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: const EdgeInsets.only(bottom: 20),
        itemBuilder: (context, index) => _wCommentItem(
          context,
          comments[index],
        ),
      ),
    );
  }

  Widget _wCommentItem(BuildContext context, JobComment jobComment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ClipOval(
                child: Assets.icon.noAvatar
                    .svg(width: 32, height: 32)
                    .backgroundColor(AppColors.grey)),
            Gaps.hGap8,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  (jobComment.employee.name)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .bold
                      .make(),
                  Gaps.vGap4,
                  TimeUtils.dateTimeToStr(jobComment.date)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .fontWeight(FontWeight.w600)
                      .color(AppColors.selectColor)
                      .make()
                ],
              ),
            ),
            if (job?.employee.name == jobComment.employee.name)
              PopupMenuButton<String>(
                child: Assets.icon.moreHorizontal.svg(),
                onSelected: (String result) {
                  switch (result) {
                    case 'edit':
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => EditCommentNewPage(
                            jobComment: jobComment,
                            onUpdate: (newComment) {
                              _onCommentUpdate(context, newComment, jobComment);
                            },
                          ),
                        ),
                      );
                      break;

                    case 'delete':
                      _onCommentDelete(context, jobComment);
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
                  PopupMenuItem<String>(
                    value: 'edit',
                    child: Row(
                      children: [
                        Assets.icon.edit.svg(width: 20, height: 20),
                        10.toHSizeBox(),
                        Text(
                          'Edit',
                          style: Theme.of(context)
                              .textTheme
                              .bodyText1
                              ?.copyWith(fontSize: 18),
                        ),
                      ],
                    ),
                  ),
                  PopupMenuItem<String>(
                    value: 'delete',
                    child: Row(
                      children: [
                        Assets.icon.delete.svg(width: 20, height: 20),
                        10.toHSizeBox(),
                        Text(
                          'Delete',
                          style: Theme.of(context)
                              .textTheme
                              .bodyText1
                              ?.copyWith(fontSize: 18),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
          ],
        ),
        Gaps.vGap4,
        (jobComment.body)
            .text
            .bodyText2(context)
            .size(Dimens.text)
            .fontWeight(FontWeight.normal)
            .lineHeight(1.5)
            .make(),
      ],
    );
  }
}

class EditCommentNewPage extends StatefulWidget {
  const EditCommentNewPage(
      {Key? key, required this.jobComment, required this.onUpdate})
      : super(key: key);

  final JobComment jobComment;
  final Function(String) onUpdate;

  @override
  State<EditCommentNewPage> createState() => _EditCommentNewPageState();
}

class _EditCommentNewPageState extends State<EditCommentNewPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    _controller.text = widget.jobComment.body;
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: Column(
        children: [
          40.toVSizeBox(),
          Expanded(
            child: Container(
                decoration: BoxDecoration(
                    color: Theme.of(context).backgroundColor,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(Dimens.rad_XXL),
                      topLeft: Radius.circular(Dimens.rad_XXL),
                    )),
                child: Column(
                  children: [
                    16.toVSizeBox(),
                    _buildAvatar(widget.jobComment),
                    OutLineTextField.big(
                      controller: _controller,
                      radius: 8,
                      maxLines: 5,
                      autoFocus: false,
                      hintText: 'Your comment',
                      backgroundColor: AppColors.grey4,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 16),
                      showBorder: false,
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodyText2
                          ?.copyWith(color: AppColors.grey),
                      // onFieldSubmitted: (value) => _onCommentSubmit(context, value),
                    ),
                    Gaps.vGap12,
                    _buildButton(),
                  ],
                ).px16()),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(JobComment jobComment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ClipOval(
                child: Assets.icon.noAvatar
                    .svg(width: 32, height: 32)
                    .backgroundColor(AppColors.grey)),
            Gaps.hGap8,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  (jobComment.employee.name)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .bold
                      .make(),
                  Gaps.vGap4,
                  TimeUtils.dateTimeToStr(jobComment.date)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .fontWeight(FontWeight.w600)
                      .color(AppColors.selectColor)
                      .make()
                ],
              ),
            ),
          ],
        ),
        Gaps.vGap4,
      ],
    );
  }

  Widget _buildButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      // mainAxisSize: MainAxisSize.min,
      children: [
        CommonOutlineButton(
          text: 'CANCEL',
          onPressed: () {
            App.pop();
          },
        ),
        Gaps.hGap12,
        CommonPrimaryButton(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 35),
          child: 'UPDATE'
              .text
              .textStyle(Theme.of(context).textTheme.bodyText1!)
              .size(Dimens.text_XL)
              .color(AppColors.materialWhite)
              .fontWeight(FontWeight.w600)
              .make(),
          onPressed: () {
            widget.onUpdate(
              _controller.text,
            );
            App.pop();
          },
        )
      ],
    );
  }
}
