import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/pages/home/<USER>/job_model.dart';
import 'package:ticking_app/services/task_service/task_service.dart';

part 'qa_job_event.dart';
part 'qa_job_state.dart';

class QaJobBloc extends Bloc<QaJobEvent, QaJobState> {
  QaJobBloc() : super(QaJobInitial()) {
    on<QaJobEvent>((event, emit) {
      // TODO: implement event handler
    });
  }
}
