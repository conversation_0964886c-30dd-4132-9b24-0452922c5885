part of 'qa_job_bloc.dart';

@immutable
abstract class Qa<PERSON>obEvent {}

class QaJobLoaded extends QaJobEvent {}

class QaJobUpdated extends QaJobEvent {}

class QaJobDisabled extends QaJobEvent {}

class QaJobEnabled extends QaJobEvent {}

class QaJobTabChanged extends QaJobEvent {}

class QaJobFiltered extends QaJobEvent {
  final DateTime filterDate;

  QaJobFiltered(this.filterDate);
}

class QaJobRefreshed extends QaJobEvent {}

class QaJobUnInit extends QaJobEvent {}

class QaJobBSUpdated extends QaJobEvent {
  QaJobBSUpdated(this.showJobDetails);

  final bool showJobDetails;
}

class QaJobSelectedMapGpsUpdated extends QaJobEvent {}

class QaJobMapGpsUpdated extends QaJobEvent {
  final Gps gps;

  QaJobMapGpsUpdated(this.gps);
}

class QaJobNetworkStatusChanged extends QaJobEvent {
  final bool isHasConnection;

  QaJobNetworkStatusChanged(this.isHasConnection);
}

class JobSelected extends QaJobEvent {
  final Job job;

  JobSelected(this.job);
}

class JobUnSelected extends QaJobEvent {
  final Job job;

  JobUnSelected(this.job);
}

class QaJobGpsUpdated extends QaJobEvent {
  final Gps gps;

  QaJobGpsUpdated(this.gps);
}

class JobReset extends QaJobEvent {}

class JobAutoRefreshDisabled extends QaJobEvent {}

class JobAutoRefreshEnabled extends QaJobEvent {}

class JobCommentDeleted extends QaJobEvent {
  final JobComment comment;

  final Job job;

  JobCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class JobCommentUpdated extends QaJobEvent {
  final JobComment comment;

  final String body;
  final Job job;

  JobCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}

class QaJobMapUpdated extends QaJobEvent {
  final bool isUpdated;

  QaJobMapUpdated(this.isUpdated);
}

class QaJobTaskUpdated extends QaJobEvent {
  final DataTask task;

  QaJobTaskUpdated(this.task);
}

class QaJobPositionUpdated extends QaJobEvent {
  final bool isPositionUpdated;

  QaJobPositionUpdated(this.isPositionUpdated);
}

class JobStarted extends QaJobEvent {
  final Job job;

  JobStarted(this.job);
}

class JobIssuePosted extends QaJobEvent {
  final Job job;
  final String reason;

  JobIssuePosted({
    required this.job,
    required this.reason,
  });
}

class JobSMSIssuePosted extends QaJobEvent {
  final Job job;
  final String reason;

  JobSMSIssuePosted({
    required this.job,
    required this.reason,
  });
}

class JobCommentPosted extends QaJobEvent {
  final String comment;

  final Job job;

  JobCommentPosted({required this.comment, required this.job});
}

class JobUnableToAccess extends QaJobEvent {
  final Job job;
  final String reason;

  JobUnableToAccess({required this.job, required this.reason});
}

class JobPostCommentEvent extends QaJobEvent {
  final Job job;
  final String commentContent;

  JobPostCommentEvent({required this.job, required this.commentContent});
}
