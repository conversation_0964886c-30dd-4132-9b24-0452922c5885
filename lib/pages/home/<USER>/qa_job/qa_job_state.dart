part of 'qa_job_bloc.dart';

class QAJobData {
  QAJobData({
    required this.jobList,
    required this.selectedJobList,
    required this.viewMode,
    required this.jobListGroup,
    required this.selectedJobListGroup,
    this.curLocation,
    required this.filterDate,
    required this.isHasConnection,
    this.smsTemplate,
    this.isInSelectedTab = false,
    this.lastLatLng,
    this.lastSelectedLatLng,
    this.isMapUpdated = false,
    this.showJobDetails = false,
    this.isPositionUpdated = false,
    this.isDisabled = false,
    this.job,
  });

  final List<QAJob> jobList;
  final List<QAJob> selectedJobList;
  final JobViewMode viewMode;
  final DateTime filterDate;
  final Gps? curLocation;
  final Map<String, List<QAJob>> jobListGroup;
  final Map<String, List<QAJob>> selectedJobListGroup;
  final bool isHasConnection;
  final bool showJobDetails;
  final SmsTemplate? smsTemplate;
  final bool isInSelectedTab;
  final Gps? lastSelectedLatLng;
  final Gps? lastLatLng;
  final bool isMapUpdated;
  final bool isPositionUpdated;
  final Job? job;
  final bool isDisabled;

  QAJobData copyWith({
    List<QAJob>? jobList,
    Job? job,
    List<QAJob>? selectedJobList,
    JobViewMode? viewMode,
    DateTime? filterDate,
    Gps? curLocation,
    Gps? lastSelectedLatLng,
    Gps? lastLatLng,
    bool? isHasConnection,
    bool? isMapUpdated,
    bool? showJobDetails,
    bool? isDisabled,
    bool? isPositionUpdated,
    Map<String, List<QAJob>>? jobListGroup,
    Map<String, List<QAJob>>? selectedJobListGroup,
    SmsTemplate? smsTemplate,
    bool? isInSelectedTab,
  }) {
    return QAJobData(
      jobList: jobList ?? this.jobList,
      isDisabled: isDisabled ?? this.isDisabled,
      isPositionUpdated: isPositionUpdated ?? this.isPositionUpdated,
      job: job ?? this.job,
      isMapUpdated: isMapUpdated ?? this.isMapUpdated,
      lastLatLng: lastLatLng ?? this.lastLatLng,
      lastSelectedLatLng: lastSelectedLatLng ?? this.lastSelectedLatLng,
      isInSelectedTab: isInSelectedTab ?? this.isInSelectedTab,
      showJobDetails: showJobDetails ?? this.showJobDetails,
      isHasConnection: isHasConnection ?? this.isHasConnection,
      selectedJobListGroup: selectedJobListGroup ?? this.selectedJobListGroup,
      selectedJobList: selectedJobList ?? this.selectedJobList,
      viewMode: viewMode ?? this.viewMode,
      filterDate: filterDate ?? this.filterDate,
      curLocation: curLocation ?? this.curLocation,
      jobListGroup: jobListGroup ?? this.jobListGroup,
      smsTemplate: smsTemplate ?? this.smsTemplate,
    );
  }
}

@immutable
abstract class QaJobState {}

class QaJobInitial extends QaJobState {}
