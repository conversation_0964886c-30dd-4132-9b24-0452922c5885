part of 'job_list_bloc.dart';

@immutable
abstract class JobListEvent {}

class JobListLoaded extends JobListEvent {}

class JobListUpdated extends JobListEvent {}

class JobListDisabled extends JobListEvent {}

class JobListEnabled extends JobListEvent {}

class JobListTabChanged extends JobListEvent {}

class JobListFiltered extends JobListEvent {
  final DateTime filterDate;

  JobListFiltered(this.filterDate);
}

class JobListRefreshed extends JobListEvent {}

class JobListUnInit extends JobListEvent {}

class JobListBSUpdated extends JobListEvent {
  JobListBSUpdated(this.showJobDetails);

  final bool showJobDetails;
}

class JobCustomerCommentLoad extends JobListEvent {
  JobCustomerCommentLoad(this.job);

  final Job job;
}

class JobListSelectedMapGpsUpdated extends JobListEvent {}

class JobListMapGpsUpdated extends JobListEvent {
  final Gps gps;

  JobListMapGpsUpdated(this.gps);
}

class JobListNetworkStatus<PERSON>hang<PERSON> extends JobListEvent {
  final bool isHasConnection;

  JobListNetworkStatusChanged(this.isHasConnection);
}

class JobSelected extends JobListEvent {
  final Job job;

  JobSelected(this.job);
}

class JobUnSelected extends JobListEvent {
  final Job job;

  JobUnSelected(this.job);
}

class JobListGpsUpdated extends JobListEvent {
  final Gps gps;

  JobListGpsUpdated(this.gps);
}

class JobReset extends JobListEvent {}

class JobAutoRefreshDisabled extends JobListEvent {}

class JobAutoRefreshEnabled extends JobListEvent {}

class JobCommentDeleted extends JobListEvent {
  final JobComment comment;

  final Job job;

  JobCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class JobCustomerCommentDeleted extends JobListEvent {
  final JobComment comment;

  final Job job;

  JobCustomerCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class JobCommentUpdated extends JobListEvent {
  final JobComment comment;

  final String body;
  final Job job;

  JobCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}

class JobCustomerCommentUpdated extends JobListEvent {
  final JobComment comment;

  final String body;
  final Job job;

  JobCustomerCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}

class JobListMapUpdated extends JobListEvent {
  final bool isUpdated;

  JobListMapUpdated(this.isUpdated);
}

class JobListTaskUpdated extends JobListEvent {
  final DataTask task;

  JobListTaskUpdated(this.task);
}

class JobListPositionUpdated extends JobListEvent {
  final bool isPositionUpdated;

  JobListPositionUpdated(this.isPositionUpdated);
}

class JobStarted extends JobListEvent {
  final Job job;

  JobStarted(this.job);
}

class JobIssuePosted extends JobListEvent {
  final Job job;
  final String reason;

  JobIssuePosted({
    required this.job,
    required this.reason,
  });
}

class JobSMSIssuePosted extends JobListEvent {
  final Job job;
  final String reason;

  JobSMSIssuePosted({
    required this.job,
    required this.reason,
  });
}

// class JobRepairPosted extends JobListEvent {
//   final Job job;
//   final String description;
//
//   JobRepairPosted({
//     required this.job,
//     required this.description,
//   });
// }

class JobCommentPosted extends JobListEvent {
  final String comment;

  final Job job;

  JobCommentPosted({required this.comment, required this.job});
}

class JobCustomerCommentPosted extends JobListEvent {
  final String comment;

  final Job job;

  JobCustomerCommentPosted({required this.comment, required this.job});
}

class JobUnableToAccess extends JobListEvent {
  final Job job;
  final String reason;

  JobUnableToAccess({required this.job, required this.reason});
}

class JobPostCommentEvent extends JobListEvent {
  final Job job;
  final String commentContent;

  JobPostCommentEvent({required this.job, required this.commentContent});
}

class JobSelectSorted extends JobListEvent {}
