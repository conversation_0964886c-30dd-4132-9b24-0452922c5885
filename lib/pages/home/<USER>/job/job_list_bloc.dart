import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:synchronized/synchronized.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/pages/home/<USER>/job_model.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/dialog_service/dialog_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/services/loop_service/loop_service.dart';
import 'package:ticking_app/services/network_service/network_service.dart';
import 'package:ticking_app/services/task_service/model/job_create_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_delete_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_reschedule_task.dart';
import 'package:ticking_app/services/task_service/model/job_update_comment_task.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/utils/dialog_helper.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/utils.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/uuid_util.dart';

part 'job_list_event.dart';

part 'job_list_state.dart';

class JobListBloc extends Bloc<JobListEvent, JobListState> {
  JobListBloc(
      {SecureConfigService? secureConfigService,
      JobApi? jobApi,
      TickingLogService? logService,
      LocationService? locationService,
      CacheService? cacheService,
      NetworkService? networkService,
      LoopService? loopService,
      TaskService? taskService,
      DialogService? dialogService})
      : super(
          JobListLoading(
            JobListData(
              jobList: [],
              selectedJobList: [],
              viewMode: JobViewMode.list,
              jobListGroup: {},
              // selectedJobListGroup: {},
              isHasConnection: true,
              isDisabled: false,
              filterDate: DateTime.now(),
              repairParts: [],
            ),
          ),
        ) {
    ///Get services
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();

    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _locationService = locationService ?? GetIt.I<LocationService>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _networkService = networkService ?? GetIt.I<NetworkService>();
    _loopService = loopService ?? GetIt.I<LoopService>();
    // _lockService = lockService ?? GetIt.I<LockService>();
    _tickingLogService.setClassName(toString());
    _dialogService = dialogService ?? GetIt.I<DialogService>();
    on<JobListLoaded>(_onLoaded);
    on<JobListUnInit>(_onUnInit);
    on<JobListUpdated>(_onUpdated);
    on<JobSelected>(_onJobSelected);
    on<JobUnSelected>(_onUnJobSelected);
    on<JobListRefreshed>(_onJobListRefresh);
    on<JobListNetworkStatusChanged>(_onNetworkStatusChanged);
    on<JobListFiltered>(_onFiltered);
    on<JobStarted>(_onStarted);
    on<JobUnableToAccess>(_onUnableToAccess);
    on<JobListBSUpdated>(_onBSUpdated);
    on<JobCustomerCommentLoad>(_onListCustomerCommentsUpdated);
    on<JobReset>(_onReset);
    on<JobListMapGpsUpdated>(_onMapGpsUpdated);
    on<JobListMapUpdated>(_onMapUpdated);
    on<JobListPositionUpdated>(_onPositionUpdated);
    on<JobAutoRefreshDisabled>(_onAutoRefreshDisabled);
    on<JobAutoRefreshEnabled>(_onAutoRefreshEnabled);
    on<JobIssuePosted>(_onIssuePosted);
    on<JobSMSIssuePosted>(_onSMSIssuePosted);
    on<JobListGpsUpdated>(_onGpsUpdated);
    on<JobListDisabled>(_onDisabled);
    on<JobListEnabled>(_onEnabled);
    on<JobCommentPosted>(_onCommentPosted);
    on<JobCustomerCommentPosted>(_onCustomerCommentPosted);
    on<JobCommentDeleted>(_onCommentDeleted);
    on<JobCustomerCommentDeleted>(_onCustomerCommentDeleted);
    on<JobCommentUpdated>(_onCommentUpdated);
    on<JobCustomerCommentUpdated>(_onCustomerCommentUpdated);
    on<JobListTaskUpdated>(_onTaskUpdated);
    on<JobSelectSorted>(_onSorted);
    // on<JobRepairPosted>(_onRepairPosted);

    // JobListTaskUpdated
  }

  StreamSubscription? _loopStreamSubscription;
  StreamSubscription? _networkStreamSubscription;
  StreamSubscription? _taskCompleteStreamSubscription;
  StreamSubscription? _locationSubscription;
  late final JobApi _jobApi;
  late final TaskService _taskService;
  late final LoopService _loopService;
  late SecureConfigService _secureConfigService;
  late LocationService _locationService;
  late TickingLogService _tickingLogService;
  late CacheService _cacheService;
  late NetworkService _networkService;
  late final DialogService _dialogService;

  // late LockService _lockService;

  ///Check if load job list
  bool _isInitialized = false;

  // Completer<void>? _completer;
  final Lock _lock = Lock();

  FutureOr<void> _onSorted(
      JobSelectSorted event, Emitter<JobListState> emit) async {
    emit(JobListLoading(state.data));
    final selectedJobList = state.data.selectedJobList;

    try {
      if (selectedJobList.isEmpty) {
        throw 'Can not optimize empty selected jobs.';
      }

      Gps curLocation = await _locationService.getCurrentPosition();

      List<String> ids = [];
      for (int i = 0; i < selectedJobList.length; i++) {
        ids.add(selectedJobList[i].id);
      }
      ApiResult<SuggestedRoute> result =
          await _jobApi.suggestedRoute(ids: ids, source: curLocation);
      if (_cacheService.isOptimizeRoute == false) {
        _cacheService.setStartLocation(curLocation);
        emit(
          JobListSortSuccess(
            state.data.copyWith(startDestination: curLocation),
          ),
        );
      }
      final data = result.data;

      if (data.warning != null) {
        bool? result = await _dialogService.showWarning(
            title: '', content: data.warning ?? '');
        if (result == false) {
          emit(JobListSortSuccess(state.data));
          return;
        }
      }
      _cacheService.setOptimizeRoute(true);
      _cacheService.setSuggestedRoute(data);

      final List<Job> selectedJobListBySort = [];

      ///Cache List<GPS>
      final List<Gps> gpsList = [];
      List<Job> jobs = data.jobs ?? [];
      for (int i = 0; i < jobs.length; i++) {
        Job? job = selectedJobList
            .firstWhereOrNull((element) => element.id == jobs[i].id);
        if (job != null) {
          selectedJobListBySort.add(job);
        }
        Gps? gps = jobs[i].gps;
        if (gps != null) {
          gpsList.add(gps);
        }
      }
      _cacheService.setOptimizeRouteWarning(false);
      emit(
        JobListSortSuccess(
          state.data.copyWithMyDestination(
              suggestedRoute: data,
              gpsList: gpsList,
              myDestination: data.destination,
              startDestination: curLocation,
              selectedJobList: selectedJobListBySort,
              isOptimizeRoute: _cacheService.isOptimizeRoute,
              isOptimizeRouteWarning: _cacheService.isOptimizeRouteWarning),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException) {
        emit(
          JobListError(data: state.data, error: 'No internet connection'),
        );
        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('JobSelectSorted', e.toString(), stack);
    }
  }

  Future<void> _onTaskUpdated(
    JobListTaskUpdated event,
    Emitter<JobListState> emit,
  ) async {
    final jobList = state.data.jobList;

    final selectedJobList = state.data.selectedJobList;

    final dataTask = event.task;

    switch (dataTask.task.runtimeType) {
      case JobCreateCommentTask:
        final jobCommentTask = dataTask.task as JobCreateCommentTask;
        final commentId = dataTask.taskId;
        final jobId = jobCommentTask.jobId;

        final findJobIndex =
            jobList.indexWhere((element) => element.id == jobId);
        final jobComment = dataTask.completeData as JobComment;
        if (findJobIndex > -1) {
          final comments = jobList[findJobIndex].comments;

          final findCommentIndex =
              comments.indexWhere((element) => element.id == commentId);

          if (findCommentIndex > -1) {
            comments[findCommentIndex] = jobComment;
          }
        }

        final findSelectedJobIndex =
            selectedJobList.indexWhere((element) => element.id == jobId);
        if (findSelectedJobIndex > -1) {
          final comments = selectedJobList[findSelectedJobIndex].comments;

          final findCommentIndex =
              comments.indexWhere((element) => element.id == commentId);

          if (findCommentIndex > -1) {
            comments[findSelectedJobIndex] = jobComment;
          }
        }

        _cacheService.updateJobList(jobList);

        emit(
          JobPostCommentSuccess(
            data: state.data.copyWith(
              jobList: jobList,
              selectedJobList: selectedJobList,
            ),
          ),
        );

        break;
    }
  }


  Future<void> _onCustomerCommentUpdated(
      JobCustomerCommentUpdated event,
      Emitter<JobListState> emit,
      ) async {
    final jobList = state.data.jobList;

    final selectedJobList = state.data.selectedJobList;

    final jobIndex =
    state.data.jobList.indexWhere((element) => element.id == event.job.id);
    final selectedJobIndex = state.data.selectedJobList
        .indexWhere((element) => element.id == event.job.id);

    try {
      late JobComment newComment;

      if (!event.comment.id.contains('commentTask')) {
        ApiResult<JobComment> commentResult = await _jobApi.updateJobCustomerComment(
          jobId: event.job.id,
          commentId: event.comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
        newComment = commentResult.data;
      } else {
        ApiResult<JobComment> commentResult = await _jobApi.postJobCustomerComment(
          jobId: event.job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
        newComment = commentResult.data;
      }

      if (jobIndex > -1) {
        final commentIndex = jobList[jobIndex]
            .customerComments
            .indexWhere((element) => element == event.comment);

        if (commentIndex >= 0) {
          jobList[jobIndex].customerComments[commentIndex] =
              jobList[jobIndex].customerComments[commentIndex].copyWith(
                body: event.body,
                id: newComment.id,
              );
        }
      }

      if (selectedJobIndex > -1) {
        final commentIndex = selectedJobList[selectedJobIndex]
            .customerComments
            .indexWhere((element) => element == event.comment);

        if (commentIndex >= 0) {
          selectedJobList[selectedJobIndex].customerComments[commentIndex] =
              selectedJobList[selectedJobIndex].customerComments[commentIndex].copyWith(
                body: event.body,
                id: newComment.id,
              );
        }
      }

      _cacheService.updateJobList(jobList);

      emit(
        JobPostCommentSuccess(
          data: state.data.copyWith(jobList: jobList),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        late DataTask task;
        if (event.comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.createJobComment,
          );
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: event.comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );
        }

        if (jobIndex >= 0) {
          final commentIndex = jobList[jobIndex].comments.indexWhere(
                (element) => element == event.comment,
          );

          if (commentIndex >= 0) {
            jobList[jobIndex].comments[commentIndex] = jobList[jobIndex]
                .comments[commentIndex]
                .copyWith(
              body: event.body,
              id: event.comment.id.contains('commentTask') ? taskId : null,
            );
          }

          if (selectedJobIndex > -1) {
            // selectedJobList[selectedJobIndex].comments.remove(event.comment);

            final commentIndex =
            selectedJobList[selectedJobIndex].comments.indexWhere(
                  (element) => element == event.comment,
            );

            if (commentIndex >= 0) {
              selectedJobList[selectedJobIndex].comments[commentIndex] =
                  selectedJobList[selectedJobIndex]
                      .comments[commentIndex]
                      .copyWith(
                    body: event.body,
                    id: event.comment.id.contains('commentTask')
                        ? taskId
                        : null,
                  );
            }
          }

          _cacheService.updateJobList(jobList);

          emit(
            JobPostCommentSuccess(
              data: state.data.copyWith(jobList: jobList),
            ),
          );
        }

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCommentUpdated(
    JobCommentUpdated event,
    Emitter<JobListState> emit,
  ) async {
    final jobList = state.data.jobList;

    final selectedJobList = state.data.selectedJobList;

    final jobIndex =
        state.data.jobList.indexWhere((element) => element.id == event.job.id);
    final selectedJobIndex = state.data.selectedJobList
        .indexWhere((element) => element.id == event.job.id);


    try {
      late JobComment newComment;

      if (!event.comment.id.contains('commentTask')) {
        ApiResult<JobComment> commentResult = await _jobApi.updateJobComment(
          jobId: event.job.id,
          commentId: event.comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
        newComment = commentResult.data;
      } else {
        ApiResult<JobComment> commentResult = await _jobApi.createJobComment(
          jobId: event.job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
        newComment = commentResult.data;
      }

      if (jobIndex > -1) {
        final commentIndex = jobList[jobIndex]
            .comments
            .indexWhere((element) => element == event.comment);

        if (commentIndex >= 0) {
          jobList[jobIndex].comments[commentIndex] =
              jobList[jobIndex].comments[commentIndex].copyWith(
                    body: event.body,
                    id: newComment.id,
                  );
        }
      }

      if (selectedJobIndex > -1) {
        final commentIndex = selectedJobList[selectedJobIndex]
            .comments
            .indexWhere((element) => element == event.comment);

        if (commentIndex >= 0) {
          selectedJobList[selectedJobIndex].comments[commentIndex] =
              selectedJobList[selectedJobIndex].comments[commentIndex].copyWith(
                    body: event.body,
                    id: newComment.id,
                  );
        }
      }

      _cacheService.updateJobList(jobList);

      emit(
        JobPostCommentSuccess(
          data: state.data.copyWith(jobList: jobList),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        late DataTask task;
        if (event.comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.createJobComment,
          );
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: event.comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );
        }

        if (jobIndex >= 0) {
          final commentIndex = jobList[jobIndex].comments.indexWhere(
                (element) => element == event.comment,
              );

          if (commentIndex >= 0) {
            jobList[jobIndex].comments[commentIndex] = jobList[jobIndex]
                .comments[commentIndex]
                .copyWith(
                  body: event.body,
                  id: event.comment.id.contains('commentTask') ? taskId : null,
                );
          }

          if (selectedJobIndex > -1) {
            // selectedJobList[selectedJobIndex].comments.remove(event.comment);

            final commentIndex =
                selectedJobList[selectedJobIndex].comments.indexWhere(
                      (element) => element == event.comment,
                    );

            if (commentIndex >= 0) {
              selectedJobList[selectedJobIndex].comments[commentIndex] =
                  selectedJobList[selectedJobIndex]
                      .comments[commentIndex]
                      .copyWith(
                        body: event.body,
                        id: event.comment.id.contains('commentTask')
                            ? taskId
                            : null,
                      );
            }
          }

          _cacheService.updateJobList(jobList);

          emit(
            JobPostCommentSuccess(
              data: state.data.copyWith(jobList: jobList),
            ),
          );
        }

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentDeleted(
      JobCustomerCommentDeleted event,
      Emitter<JobListState> emit,
      ) async {

    final jobList = state.data.jobList;

    final selectedJobList = state.data.selectedJobList;
    try {
      final jobIndex = state.data.jobList
          .indexWhere((element) => element.id == event.job.id);

      if (jobIndex > -1) {
        if (!event.comment.id.contains('commentTask')) {
          await _jobApi.deleteJobCustomerComment(
            jobId: event.job.id,
            dateTime: DateTime.now(),
            commentId: event.comment.id,
          );
        }

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        final jobComments = jobList[jobIndex].customerComments;

        jobComments.remove(event.comment);

        jobList[jobIndex].customerComments.remove(event.comment);
      }
      final selectedJobIndex = state.data.selectedJobList
          .indexWhere((element) => element.id == event.job.id);
      if (selectedJobIndex > -1) {
        selectedJobList[selectedJobIndex].customerComments.remove(event.comment);
      }

      _cacheService.updateJobList(jobList);

      emit(
        JobPostCommentSuccess(
          data: state.data.copyWith(jobList: jobList),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        final curJobIndex =
        jobList.indexWhere((element) => element.id == event.job.id);

        final selectedJobIndex =
        selectedJobList.indexWhere((element) => element.id == event.job.id);
        if (selectedJobIndex > -1) {
          selectedJobList[selectedJobIndex].comments.remove(event.comment);
        }

        if (curJobIndex > -1) {
          jobList[curJobIndex].comments.remove(event.comment);
          _cacheService.updateJobList(jobList);
        }

        emit(
          JobPostCommentSuccess(
            data: state.data.copyWith(
              jobList: jobList,
              selectedJobList: selectedJobList,
            ),
          ),
        );

        if (event.comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: event.comment.id,
            dateTime: now,
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCommentDeleted(
    JobCommentDeleted event,
    Emitter<JobListState> emit,
  ) async {
    await _taskService.executeAllTasks();

    final jobList = state.data.jobList;

    final selectedJobList = state.data.selectedJobList;
    try {
      final jobIndex = state.data.jobList
          .indexWhere((element) => element.id == event.job.id);

      if (jobIndex > -1) {
        if (!event.comment.id.contains('commentTask')) {
          await _jobApi.deleteJobComment(
            jobId: event.job.id,
            dateTime: DateTime.now(),
            commentId: event.comment.id,
          );
        }

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        final jobComments = jobList[jobIndex].comments;

        jobComments.remove(event.comment);

        jobList[jobIndex].comments.remove(event.comment);
      }
      final selectedJobIndex = state.data.selectedJobList
          .indexWhere((element) => element.id == event.job.id);
      if (selectedJobIndex > -1) {
        selectedJobList[selectedJobIndex].comments.remove(event.comment);
      }

      _cacheService.updateJobList(jobList);

      emit(
        JobPostCommentSuccess(
          data: state.data.copyWith(jobList: jobList),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

        final curJobIndex =
            jobList.indexWhere((element) => element.id == event.job.id);

        final selectedJobIndex =
            selectedJobList.indexWhere((element) => element.id == event.job.id);
        if (selectedJobIndex > -1) {
          selectedJobList[selectedJobIndex].comments.remove(event.comment);
        }

        if (curJobIndex > -1) {
          jobList[curJobIndex].comments.remove(event.comment);
          _cacheService.updateJobList(jobList);
        }

        emit(
          JobPostCommentSuccess(
            data: state.data.copyWith(
              jobList: jobList,
              selectedJobList: selectedJobList,
            ),
          ),
        );

        if (event.comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: event.comment.id,
            dateTime: now,
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onEnabled(
    JobListEnabled event,
    Emitter<JobListState> emit,
  ) async {
    emit(
      JobListLoadSuccess(
        state.data.copyWith(isDisabled: false),
      ),
    );
  }

  Future<void> _onDisabled(
    JobListDisabled event,
    Emitter<JobListState> emit,
  ) async {
    emit(
      JobListLoadSuccess(
        state.data.copyWith(isDisabled: true),
      ),
    );
  }

  Future<void> _onGpsUpdated(
    JobListGpsUpdated event,
    Emitter<JobListState> emit,
  ) async {
    if (state.data.isDisabled) {
      return;
    }

    await _lock.synchronized(() async {
      final jobList = state.data.jobList;
      final selectedJobList = state.data.selectedJobList;
      final bool isGpsEnabled =
          await _locationService.isLocationServiceEnabled();

      if (!isGpsEnabled) {
        return;
      }

      Gps curLocation = await _locationService.getCurrentPosition();

      emit(
        JobListMapUpdateSuccess(
          state.data.copyWith(
            isPositionUpdated: true,
            curLocation: curLocation,
            jobListGroup: _filterJobsToGroup(jobList, curLocation),
            // selectedJobListGroup:
            //     _filterJobsToGroup(selectedJobList, curLocation),
            selectedJobList: selectedJobList,
            isHasConnection: _networkService.hasConnection,
          ),
        ),
      );
    });
  }

  Future<void> _onIssuePosted(
    JobIssuePosted event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() async {
      _loopStreamSubscription?.cancel();

      try {
        emit(JobListBusy(state.data));

        await _jobApi.postIssue(
          id: event.job.id,
          request: JobIssueRequest(
            reason: event.reason,
            jobType: event.job.type,
            method: 'email',
            date: DateTime.now(),
          ),
        );

        emit(JobListEmailSendSuccess(state.data));
      } catch (e, stack) {
        String error = e.toString();
        if (e is SocketException) {
          error = 'No internet connection';
        }
        _tickingLogService.error('_onIssuePosted', error, stack);

        final bool isNotFound =
            e is ApiBadRequestException && e.statusCode == 401;

        emit(
          JobListEmailSendFailed(
            data: state.data,
            error: isNotFound ? error : 'Sent email failed!',
          ),
        );
      }

      ///Refresh job list every 3 minutes when have network connection
      _loopStreamSubscription = _loopService.onCall.listen((event) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListUpdated());
        }
      });
    });
  }

  Future<void> _onSMSIssuePosted(
      JobSMSIssuePosted event,
      Emitter<JobListState> emit,
      ) async {
    await _lock.synchronized(() async {
      _loopStreamSubscription?.cancel();

      try {
        emit(JobListBusy(state.data));

        await _jobApi.postIssue(
          id: event.job.id,
          request: JobIssueRequest(
            reason: event.reason,
            jobType: event.job.type,
            method: 'sms',
            date: DateTime.now(),
          ),
        );

        DialogHelper.showInfo(
          title: '',
          icon: const SizedBox(),
          content: 'SMS sent successfully',
        );

        emit(JobListSMSSendSuccess(state.data));
      } catch (e, stack) {
        String error = e.toString();
        if (e is SocketException) {
          error = 'No internet connection';
        }
        _tickingLogService.error('_onIssuePosted', error, stack);

        final bool isNotFound =
            e is ApiBadRequestException && e.statusCode == 401;

        emit(
          JobListEmailSendFailed(
            data: state.data,
            error: isNotFound ? error : 'Sent SMS failed!',
          ),
        );
      }

      ///Refresh job list every 3 minutes when have network connection
      _loopStreamSubscription = _loopService.onCall.listen((event) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListUpdated());
        }
      });
    });
  }

  Future<void> _onAutoRefreshEnabled(
    JobAutoRefreshEnabled event,
    Emitter<JobListState> emit,
  ) async {
    // await _completer?.future;
    //
    // _completer = Completer<void>();

    _locationSubscription?.cancel();
    _locationSubscription =
        _locationService.onCurrentPositionChanged.listen((gps) {
      if (_isInitialized && !state.data.isDisabled) {
        final now = DateTime.now();
        if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
          _lastLocationUpdateTime = now;
          add(JobListGpsUpdated(gps));
        }
      }
    });

    _loopStreamSubscription?.cancel();

    ///Refresh job list every 3 minutes when have network connection
    _loopStreamSubscription = _loopService.onCall.listen((event) {
      if (_isInitialized && !state.data.isDisabled) {
        add(JobListUpdated());
      }
    });

    emit(
      JobListLoadSuccess(
        state.data.copyWith(isDisabled: false),
      ),
    );
    // _completer?.complete();
  }

  Future<void> _onAutoRefreshDisabled(
    JobAutoRefreshDisabled event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() async {
      _locationSubscription?.cancel();
      _loopStreamSubscription?.cancel();

      emit(
        JobListLoadSuccess(
          state.data.copyWith(isDisabled: true),
        ),
      );
    });
  }

  Future<void> _onPositionUpdated(
    JobListPositionUpdated event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() async {
      emit(
        JobListMapUpdateSuccess(
          state.data.copyWith(
            isPositionUpdated: event.isPositionUpdated,
          ),
        ),
      );
    });
  }

  Future<void> _onMapUpdated(
    JobListMapUpdated event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() async {
      emit(
        JobListMapUpdateSuccess(
          state.data.copyWith(
            isMapUpdated: event.isUpdated,
          ),
        ),
      );
    });
  }

  Future<void> _onMapGpsUpdated(
    JobListMapGpsUpdated event,
    Emitter<JobListState> emit,
  ) async {
    emit(
      JobMapGpsUpdateSuccess(
        state.data.copyWith(
          lastLatLng: event.gps,
          isMapUpdated: false,
        ),
      ),
    );
  }

  Future<void> _onReset(
    JobReset event,
    Emitter<JobListState> emit,
  ) async {}

  Future<void> _onCommentPosted(
    JobCommentPosted event,
    Emitter<JobListState> emit,
  ) async {
    try {
      final result = await _jobApi.createJobComment(
        jobId: event.job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final jobList = state.data.jobList;
      // final selectedJobList = state.data.selectedJobList;
      final curJobIndex =
          jobList.indexWhere((element) => element.id == event.job.id);

      if (curJobIndex >= 0) {
        jobList[curJobIndex].comments.insert(0, result.data);
        _cacheService.updateJobList(jobList);

        emit(
          JobPostCommentSuccess(
            data: state.data.copyWith(jobList: jobList),
          ),
        );
      }
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(
              body: event.comment,
              date: now,
            ),
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        final jobList = state.data.jobList;

        final curJobIndex =
            jobList.indexWhere((element) => element.id == event.job.id);

        if (curJobIndex >= 0) {
          jobList[curJobIndex].comments.insert(
                0,
                JobComment(
                  date: now,
                  body: event.comment,
                  id: taskId,
                  employee: event.job.employee,
                  jobId: event.job.id,
                ),
              );

          _cacheService.updateJobList(jobList);

          emit(
            JobPostCommentSuccess(
              data: state.data.copyWith(jobList: jobList),
            ),
          );
        }
        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentPosted(
      JobCustomerCommentPosted event,
      Emitter<JobListState> emit,
      ) async {
    try {
      final result = await _jobApi.postJobCustomerComment(
        jobId: event.job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final jobList = state.data.jobList;
      // final selectedJobList = state.data.selectedJobList;
      final curJobIndex =
      jobList.indexWhere((element) => element.id == event.job.id);

      if (curJobIndex >= 0) {
        jobList[curJobIndex].customerComments.insert(0, result.data);
        _cacheService.updateJobList(jobList);

        emit(
          JobPostCustomerCommentSuccess(
            data: state.data.copyWith(jobList: jobList),
          ),
        );
      }
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(
              body: event.comment,
              date: now,
            ),
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        final jobList = state.data.jobList;

        final curJobIndex =
        jobList.indexWhere((element) => element.id == event.job.id);

        if (curJobIndex >= 0) {
          jobList[curJobIndex].comments.insert(
            0,
            JobComment(
              date: now,
              body: event.comment,
              id: taskId,
              employee: event.job.employee,
              jobId: event.job.id,
            ),
          );

          _cacheService.updateJobList(jobList);

          emit(
            JobPostCommentSuccess(
              data: state.data.copyWith(jobList: jobList),
            ),
          );
        }
        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onBSUpdated(
    JobListBSUpdated event,
    Emitter<JobListState> emit,
  ) async {
    if (state.data.isDisabled) {
      return;
    }

    emit(
      JobListBSUpdateSuccess(
        state.data.copyWith(showJobDetails: event.showJobDetails),
      ),
    );
  }

  Future<void> _onListCustomerCommentsUpdated(
    JobCustomerCommentLoad event,
    Emitter<JobListState> emit,
  ) async {
    final result = await _jobApi.createJobCustomerComment(jobId: event.job.id);


    if (result != null) {
      event.job.customerComments = List.from(result as List)
          .map((e) => JobComment.fromJson(Map<String, dynamic>.from(e)))
          .toList();
      emit(
        JobListBSUpdateSuccess(
          state.data.copyWith(job: event.job),
        ),
      );
    }
  }

  Future<void> _onFiltered(
    JobListFiltered event,
    Emitter<JobListState> emit,
  ) async {
    if (!_networkService.hasConnection) {
      return;
    }

    await _onRefresh(emit, event.filterDate);
  }

  Future<void> _onUnInit(
    JobListUnInit event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() async {
      _isInitialized = false;
      _loopStreamSubscription?.cancel();
      _networkStreamSubscription?.cancel();
      _taskCompleteStreamSubscription?.cancel();
      _locationSubscription?.cancel();
      _loopService.stop();
      _cacheService.emptyCache();
      _loopStreamSubscription = null;
      _networkStreamSubscription = null;
      _taskCompleteStreamSubscription = null;
      _locationSubscription = null;
      // await Future.delayed(const Duration(milliseconds: 1000));
      emit(
        JobListLoadSuccess(
          state.data.copyWith(
            isHasConnection: true,
            jobList: [],
            selectedJobList: [],
            viewMode: JobViewMode.list,
            jobListGroup: {},
            isOptimizeRouteWarning: false,
            isOptimizeRoute: false,
            // selectedJobListGroup: {},
            filterDate: DateTime.now(),
          ),
        ),
      );
    });
  }

  Future<void> _onUpdated(
    JobListUpdated event,
    Emitter<JobListState> emit,
  ) async {
    await _onRefresh(emit);
  }

  Future<void> _onStarted(
    JobStarted event,
    Emitter<JobListState> emit,
  ) async {
    emit(
      JobStartSuccess(state.data.copyWith(job: event.job)),
    );
  }

  Future<void> _onNetworkStatusChanged(
    JobListNetworkStatusChanged event,
    Emitter<JobListState> emit,
  ) async {
    await _lock.synchronized(() {
      if (state.data.isDisabled || !_isInitialized) {
        return;
      }

      emit(
        JobListLoadSuccess(
          state.data.copyWith(
            isHasConnection: _networkService.hasConnection,
          ),
        ),
      );
    });
  }

  Future<void> _onUnJobSelected(
    JobUnSelected event,
    Emitter<JobListState> emit,
  ) async {
    final selectedJobList = state.data.selectedJobList;
    // final repairParts = state.data.repairParts;
    if (selectedJobList.any((element) => element.id == event.job.id)) {
      selectedJobList.removeWhere((element) => element.id == event.job.id);
      _cacheService.unSelectedJob(event.job);
      // final detailsPartList = event.job.details.repairParts;
      final List<RepairParts> repairParts = [];

      for (final job in selectedJobList) {
        _addPartToJobList(repairParts, job.details.repairParts);
      }
      _changedSelectedJobs();

      emit(
        JobUnSelectSuccess(
          state.data.copyWith(
            repairParts: repairParts,
            selectedJobList: selectedJobList,
            isOptimizeRouteWarning: _cacheService.isOptimizeRouteWarning,
            isOptimizeRoute: _cacheService.isOptimizeRoute,
            // selectedJobListGroup:
            //     _filterJobsToGroup(selectedJobList, state.data.curLocation),
            isMapUpdated: true,
          ),
        ),
      );
    }
  }

  Future<void> _onJobSelected(
    JobSelected event,
    Emitter<JobListState> emit,
  ) async {
    final selectedJobList = state.data.selectedJobList;
    final jobList = state.data.jobList;
    final repairParts = state.data.repairParts;
    if (!selectedJobList.any((element) => element.id == event.job.id)) {
      final detailsPartList = event.job.details.repairParts;
      _addPartToJobList(repairParts, detailsPartList);

      selectedJobList.add(event.job);
      _cacheService.selectedJob(event.job);
      _changedSelectedJobs();

      emit(
        JobSelectSuccess(
          state.data.copyWith(
            repairParts: repairParts,
            selectedJobList: selectedJobList,
            jobListGroup: _filterJobsToGroup(jobList, state.data.curLocation),
            isOptimizeRouteWarning: _cacheService.isOptimizeRouteWarning,
            isOptimizeRoute: _cacheService.isOptimizeRoute,
            // selectedJobListGroup:
            //     _filterJobsToGroup(selectedJobList, state.data.curLocation),
            isMapUpdated: true,
          ),
        ),
      );
    }
  }

  DateTime _lastLocationUpdateTime = DateTime.now();

  ///Load data event
  Future<void> _onLoaded(
    JobListLoaded event,
    Emitter<JobListState> emit,
  ) async {
    bool isOptimizeRoute = _cacheService.isOptimizeRoute;
    bool isOptimizeRouteWarning = _cacheService.isOptimizeRouteWarning;
    Gps? curLocation;
    _isInitialized = false;
    SuggestedRoute? suggestedRoute = _cacheService.suggestedRoute;
    List<Gps> gpsList = [];
    if (suggestedRoute != null) {
      List<Job> jobs = suggestedRoute.jobs ?? [];
      for (int i = 0; i < jobs.length; i++) {
        Gps? gps = jobs[i].gps;
        if (gps != null) {
          gpsList.add(gps);
        }
      }
    }
    emit(
      JobListLoading(
        state.data.copyWith(
          isHasConnection: true,
          myDestination: suggestedRoute?.destination,
          startDestination: _cacheService.startLocation,
          gpsList: gpsList,
          isDisabled: false,
          showJobDetails: false,
        ),
      ),
    );

    try {
      final now = DateTime.now();

      await _taskService.executeAllTasks();

      _taskService.startLoop();

      _loopService.init(const Duration(minutes: 5));

      _loopStreamSubscription?.cancel();
      _loopStreamSubscription = _loopService.onCall.listen((event) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListUpdated());
        }
      });

      _networkStreamSubscription?.cancel();
      _networkStreamSubscription =
          _networkService.onInternetConnectionChanged.listen((event) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListNetworkStatusChanged(event));
        }
      });

      _taskCompleteStreamSubscription?.cancel();
      _taskCompleteStreamSubscription =
          _taskService.taskCompleteStream.listen((task) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListTaskUpdated(task));
        }
      });

      _loopService.start();

      await _locationService.requestPermission();

      _locationSubscription?.cancel();
      _locationSubscription =
          _locationService.onCurrentPositionChanged.listen((gps) {
        if (_isInitialized && !state.data.isDisabled) {
          final now = DateTime.now();
          if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
            _lastLocationUpdateTime = now;
            add(JobListGpsUpdated(gps));
          }
        }
      });

      final bool isGpsEnabled =
          await _locationService.isLocationServiceEnabled();

      if (!isGpsEnabled) {
        throw Exception(
          'Can not get your location. Please enable GPS and press refresh button!',
        );
      }

      curLocation = await _locationService.getCurrentPosition();

      List<Job> jobList = [];
      List<Job> selectedJobList = [];
      List<RepairParts> repairParts = [];

      // final jobListModel = _cacheService.getJobList;
      //
      // for (final jobModel in jobListModel) {
      //   if (jobModel.isSelected) {
      //
      //   }
      // }

      if (!_networkService.hasConnection) {
        final jobListModel = _cacheService.getJobList;
        for (final jobModel in jobListModel) {
          if (jobModel.isSelected) {
            selectedJobList.add(jobModel.job);
            // repairParts.addAll(jobModel.job.details.repairParts);
            _addPartToJobList(repairParts, jobModel.job.details.repairParts);
          }

          jobList.add(jobModel.job);
        }
      } else {
        final request = JobListRequest(lteScheduleDate: now, limit: 500);

        final ApiListResult<Job> result = await _jobApi.getJobs(request);

        jobList = result.items;

        final jobListModel = _cacheService.getJobList;

        for (final job in jobList) {
          final jobModelIndex =
              jobListModel.indexWhere((element) => element.job.id == job.id);

          if (jobModelIndex > -1 && jobListModel[jobModelIndex].isSelected) {
            selectedJobList.add(job);
            _addPartToJobList(repairParts, job.details.repairParts);
            // repairParts.addAll(job.details.repairParts);
          }
        }
        // final data = ApiListResult<Job>.fromJson(mockData);
        //
        // jobList = data.items;

        await _cacheService.updateJobList(jobList);
      }

      emit(
        JobListLoadSuccess(
          state.data.copyWith(
            jobList: jobList,
            repairParts: repairParts,
            isOptimizeRoute: isOptimizeRoute,
            isOptimizeRouteWarning: isOptimizeRouteWarning,
            filterDate: now,
            curLocation: curLocation,
            jobListGroup: _filterJobsToGroup(jobList, curLocation),
            // selectedJobListGroup:
            //     _filterJobsToGroup(selectedJobList, curLocation),
            selectedJobList: selectedJobList,
            isHasConnection: _networkService.hasConnection,
            smsTemplate: _cacheService.smsTemplate,
            isDisabled: false,
            roles: _secureConfigService.userInfo?.roles,
            myDestination: suggestedRoute?.destination,
          ),
        ),
      );
    } catch (e, stack) {
      if (e is ApiBadRequestException &&
          e.error?.code == 'JOB-0010' &&
          e.statusCode == 400) {
        String message = e.error?.message ?? '';
        if (message.isNotEmpty) {
          final Map map = jsonDecode(message);

          emit(
            JobListLatePayrollFailure(
              data: state.data,
              title: map['title'] ?? '',
              message: map['message'] ?? 'No Data',
            ),
          );
          return;
        }
      }

      List<Job> jobList = [];
      List<Job> selectedJobList = [];
      List<RepairParts> repairParts = [];
      try {
        final jobListModel = _cacheService.getJobList;
        for (final jobModel in jobListModel) {
          if (jobModel.isSelected) {
            selectedJobList.add(jobModel.job);
            _addPartToJobList(repairParts, jobModel.job.details.repairParts);
          }

          jobList.add(jobModel.job);
        }

        _tickingLogService.error('JobListLoadFailure', e.toString(), stack);

        emit(
          JobListLoadFailure(
            data: state.data.copyWith(
              jobList: jobList,
              roles: _secureConfigService.userInfo?.roles,
              repairParts: repairParts,
              curLocation: curLocation,
              jobListGroup: _filterJobsToGroup(jobList, curLocation),
              isOptimizeRoute: isOptimizeRoute,
              isOptimizeRouteWarning: isOptimizeRouteWarning,
              // selectedJobListGroup:
              //     _filterJobsToGroup(selectedJobList, curLocation),
              selectedJobList: selectedJobList,
              isHasConnection: _networkService.hasConnection,
              isDisabled: false,
              myDestination: suggestedRoute?.destination,
            ),
            error: e.toString(),
          ),
        );
      } catch (e) {
        emit(
          JobListLoadFailure(
            data: state.data.copyWith(
              jobList: jobList,
              repairParts: repairParts,
              curLocation: curLocation,
              isOptimizeRoute: isOptimizeRoute,
              isOptimizeRouteWarning: isOptimizeRouteWarning,
              jobListGroup: _filterJobsToGroup(jobList, curLocation),
              // selectedJobListGroup:
              //     _filterJobsToGroup(selectedJobList, curLocation),
              selectedJobList: selectedJobList,
              isHasConnection: _networkService.hasConnection,
              isDisabled: false,
              roles: _secureConfigService.userInfo?.roles,
              myDestination: suggestedRoute?.destination,
            ),
            error: e.toString(),
          ),
        );
      }
    }

    _isInitialized = true;
  }

  void _addPartToJobList(
      List<RepairParts> repairParts, List<RepairParts> items) {
    for (final part in items) {
      final findIndex =
          repairParts.indexWhere((element) => element.id == part.id);
      if (findIndex > -1) {
        final consumedQuantity =
            (repairParts[findIndex].consumedQuantity ?? 0) +
                (part.consumedQuantity ?? 0);
        final estimatedQuantity =
            repairParts[findIndex].estimatedQuantity + part.estimatedQuantity;

        repairParts[findIndex] = repairParts[findIndex].copyWith(
            consumedQuantity: consumedQuantity,
            estimatedQuantity: estimatedQuantity);
      } else {
        repairParts.add(part);
      }
    }
    repairParts.sortBy((element) => element.details?.name ?? '');
  }

  void _removePartToJobList(
      List<RepairParts> repairParts, List<RepairParts> items) {
    for (final part in items) {
      final findIndex =
          repairParts.indexWhere((element) => element.id == part.id);
      if (findIndex > -1) {
        final consumedQuantity =
            (repairParts[findIndex].consumedQuantity ?? 0) -
                (part.consumedQuantity ?? 0);
        final estimatedQuantity =
            repairParts[findIndex].estimatedQuantity - part.estimatedQuantity;

        repairParts[findIndex] = repairParts[findIndex].copyWith(
            consumedQuantity: consumedQuantity,
            estimatedQuantity: estimatedQuantity);

        if (consumedQuantity <= 0 || estimatedQuantity <= 0) {
          repairParts.removeWhere((element) => element.id == part.id);
        }
      }
    }
    repairParts.sortBy((element) => element.details?.name ?? '');
  }

  Map<String, List<Job>> _filterJobsToGroup(List<Job> jobList, [Gps? userGps]) {
    final result = <String, List<Job>>{};

    if (jobList.isEmpty) {
      return result;
    }

    final filter = List<Job>.from(jobList);
    filter.sort((a, b) => a.scheduleDate!.compareTo(b.scheduleDate!));
    final DateTime now = DateTime.now().clearTime();
    for (final element in filter) {
      final jobDate = element.scheduleDate ?? now;

      final String groupName = jobDate.dateGroupName;

      if (!result.containsKey(groupName)) {
        result[groupName] = [];
      }

      result[groupName]?.add(element);
      if (userGps != null) {
        result[groupName]?.sort(
          (a, b) => _sortJobByteDistance(
            jobA: a,
            jobB: b,
            userGps: userGps,
          ),
        );
      }
    }
    return result;
  }

  int _sortJobByteDistance(
      {required Gps userGps, required Job jobA, required Job jobB}) {
    final gpsA = jobA.contact.gps;
    final gpsB = jobB.contact.gps;

    final jobADate = jobA.scheduleDate?.clearTime();
    final jobBDate = jobB.scheduleDate?.clearTime();
    if (jobADate != null &&
        jobBDate != null &&
        jobADate.difference(jobBDate).inDays != 0) {
      return jobADate.compareTo(jobBDate);
    }

    if (gpsB != null && gpsA != null) {
      final jobADistance = GeoLocatorUtils.distanceBetweenGps(userGps, gpsA);
      final jobBDistance = GeoLocatorUtils.distanceBetweenGps(userGps, gpsB);

      return jobADistance.compareTo(jobBDistance);
    }

    return 0;
  }

  FutureOr<void> _onRefresh(Emitter<JobListState> emit,
      [DateTime? filterDate]) async {
    if (state.data.isDisabled) {
      return;
    }

    final filter = filterDate ?? state.data.filterDate;

    _loopStreamSubscription?.cancel();
    _locationSubscription?.cancel();
    _lastLocationUpdateTime = DateTime.now();

    if (!_networkService.hasConnection) {
      final data = state.data;
      await _locationService.requestPermission();

      final curLocation = await _locationService.getCurrentPosition();
      final List<RepairParts> repairParts = [];
      for (final job in data.selectedJobList) {
        _addPartToJobList(repairParts, job.details.repairParts);
      }

      emit(
        JobListLoadSuccess(
          state.data.copyWith(
            repairParts: repairParts,
            isMapUpdated: false,
            isPositionUpdated: true,
            isHasConnection: _networkService.hasConnection,
            curLocation: curLocation,
            jobListGroup: _filterJobsToGroup(data.jobList, data.curLocation),
            // selectedJobListGroup: _filterJobsToGroup(
            //   data.selectedJobList,
            //   data.curLocation,
            // ),
          ),
        ),
      );

      _loopService.stop();
      _loopService.start();

      ///Refresh job list every 3 minutes when have network connection
      _loopStreamSubscription = _loopService.onCall.listen((event) {
        if (_isInitialized && !state.data.isDisabled) {
          add(JobListUpdated());
        }
      });

      _locationSubscription =
          _locationService.onCurrentPositionChanged.listen((gps) {
        if (_isInitialized && !state.data.isDisabled) {
          final now = DateTime.now();
          if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
            _lastLocationUpdateTime = now;
            add(JobListGpsUpdated(gps));
          }
        }
      });

      return;
    }

    emit(
      JobListLoading(
        state.data.copyWith(
          filterDate: filter,
          isHasConnection: _networkService.hasConnection,
          isMapUpdated: false,
          isPositionUpdated: false,
        ),
      ),
    );

    try {
      await _taskService.executeAllTasks();
    } catch (_) {}

    try {
      JobListData data;

      data = await _onNetworkLoad(filter);

      emit(
        JobListLoadSuccess(
          data.copyWith(
            isMapUpdated: true,
            isPositionUpdated: false,
            filterDate: filter,
            isHasConnection: _networkService.hasConnection,
          ),
        ),
      );
    } catch (e, stack) {
      if (e is ApiBadRequestException &&
          e.error?.code == 'JOB-0010' &&
          e.statusCode == 400) {
        String message = e.error?.message ?? '';
        if (message.isNotEmpty) {
          final Map map = jsonDecode(message);

          emit(
            JobListLatePayrollFailure(
              data: state.data,
              title: map['title'] ?? '',
              message: map['message'] ?? 'No Data',
            ),
          );
          return;
        }
      } else if (e is ApiBadRequestException && e.statusCode == 401) {
        emit(
          JobListError(
            data: state.data,
            error: e.toString(),
          ),
        );
        return;
      }
      emit(
        JobListLoadFailure(
          data: state.data.copyWith(
            isHasConnection: _networkService.hasConnection,
            filterDate: filter,
          ),
          error: e.toString(),
        ),
      );

      _tickingLogService.error('JobListLoadFailure', e.toString(), stack);
    }

    _loopService.stop();
    _loopService.start();

    ///Refresh job list every 3 minutes when have network connection
    _loopStreamSubscription = _loopService.onCall.listen((event) {
      if (_isInitialized && !state.data.isDisabled) {
        add(JobListUpdated());
      }
    });

    _locationSubscription =
        _locationService.onCurrentPositionChanged.listen((gps) {
      if (_isInitialized && !state.data.isDisabled) {
        final now = DateTime.now();
        if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
          _lastLocationUpdateTime = now;
          add(JobListGpsUpdated(gps));
        }
      }
    });
  }

  FutureOr<void> _onJobListRefresh(
      JobListRefreshed event, Emitter<JobListState> emit) async {
    await _onRefresh(emit);
  }

  Future<JobListData> _onNetworkLoad(DateTime filterDate) async {
    await _locationService.requestPermission();

    final curLocation = await _locationService.getCurrentPosition();

    final request = JobListRequest(lteScheduleDate: filterDate, limit: 500);

    final result = await _jobApi.getJobs(request);

    final selectedJobList = state.data.selectedJobList;

    final jobList = result.items;

    // final unLinkJobs = selectedJobList
    //     .where((selectedJob) => !jobList.any((job) => selectedJob.id == job.id))
    //     .toList();

    final selectedLinkJobs = jobList
        .where((job) =>
            selectedJobList.any((selectedJob) => selectedJob.id == job.id))
        .toList();

    // for (final unLinkJob in unLinkJobs) {
    //   selectedJobList.removeWhere((job) => unLinkJob.id == job.id);
    // }

    selectedJobList.clear();
    selectedJobList.addAll(selectedLinkJobs);
    final List<RepairParts> repairParts = [];
    for (final job in selectedJobList) {
      _addPartToJobList(repairParts, job.details.repairParts);
    }

    // await _cacheService.updateJobList([]);
    // for (final job in selectedLinkJobs) {
    //
    // }

    if (filterDate.clearTime().difference(DateTime.now().clearTime()).inDays ==
        0) {
      await _cacheService.updateJobList(jobList);
    }

    return state.data.copyWith(
      isHasConnection: _networkService.hasConnection,
      filterDate: filterDate,
      repairParts: repairParts,
      jobList: jobList,
      curLocation: curLocation,
      selectedJobList: selectedJobList,
      jobListGroup: _filterJobsToGroup(jobList, curLocation),
      // selectedJobListGroup: _filterJobsToGroup(selectedJobList, curLocation),
    );
  }

  void _changedSelectedJobs() {
    if (_cacheService.isOptimizeRoute) {
      _cacheService.setOptimizeRouteWarning(true);
    }
  }

  FutureOr<void> _onUnableToAccess(
      JobUnableToAccess event, Emitter<JobListState> emit) async {
    try {
      await _jobApi.unableToAccess(
        id: event.job.id,
        request: JobReschedulingRequest(
          reason: event.reason,
          date: DateTime.now(),
        ),
      );

      final jobList = state.data.jobList;
      final selectedJobList = state.data.selectedJobList;

      jobList.removeWhere((element) => element.id == event.job.id);
      selectedJobList.removeWhere((element) => element.id == event.job.id);

      _cacheService.updateJobList(jobList);
      int findIndex = state.data.selectedJobList
          .indexWhere((element) => element.id == event.job.id);

      if (findIndex != -1) {
        _changedSelectedJobs();
      }
      emit(
        JobListLoadSuccess(
          state.data.copyWith(
            isOptimizeRouteWarning: _cacheService.isOptimizeRouteWarning,
          ),
        ),
      );
      add(JobListRefreshed());
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}';

        final task = DataTask(
          taskId: taskId,
          task: JobRescheduleTask(
            request: JobReschedulingRequest(
              date: now,
              reason: event.reason,
            ),
            jobId: event.job.id,
          ),
          type: TaskType.jobReschedule,
        );

        final jobList = state.data.jobList;
        final selectedJobList = state.data.selectedJobList;

        jobList.removeWhere((element) => element.id == event.job.id);
        selectedJobList.removeWhere((element) => element.id == event.job.id);

        final List<RepairParts> repairParts = [];
        for (final job in selectedJobList) {
          _addPartToJobList(repairParts, job.details.repairParts);
        }

        _cacheService.updateJobList(jobList);
        int findIndex = state.data.selectedJobList
            .indexWhere((element) => element.id == event.job.id);

        if (findIndex != -1) {
          _changedSelectedJobs();
        }
        emit(
          JobListLoadSuccess(
            state.data.copyWith(
              jobList: jobList,
              repairParts: repairParts,
              isMapUpdated: true,
              selectedJobList: selectedJobList,
              jobListGroup: _filterJobsToGroup(jobList, state.data.curLocation),
              isOptimizeRouteWarning: _cacheService.isOptimizeRouteWarning,
              // selectedJobListGroup:
              //     _filterJobsToGroup(selectedJobList, state.data.curLocation),
            ),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobListError(
          data: state.data,
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }

      _tickingLogService.error('_onUnableToAccess', e.toString(), stack);
    }
  }

  @override
  Future<void> close() {
    _loopStreamSubscription?.cancel();
    _networkStreamSubscription?.cancel();
    _taskCompleteStreamSubscription?.cancel();
    _locationSubscription?.cancel();
    return super.close();
  }
}
