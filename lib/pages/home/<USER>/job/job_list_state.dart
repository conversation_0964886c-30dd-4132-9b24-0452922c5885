part of 'job_list_bloc.dart';

class JobListData {
  JobListData({
    required this.jobList,
    required this.selectedJobList,
    required this.viewMode,
    required this.jobListGroup,
    // required this.selectedJobListGroup,
    this.curLocation,
    required this.filterDate,
    required this.isHasConnection,
    this.smsTemplate,
    this.isInSelectedTab = false,
    this.lastLatLng,
    this.lastSelectedLatLng,
    this.isMapUpdated = false,
    this.showJobDetails = false,
    this.isPositionUpdated = false,
    this.isDisabled = false,
    this.job,
    required this.repairParts,
    this.roles = const [],
    this.suggestedRoute,
    this.isOptimizeRouteWarning = false,
    this.isOptimizeRoute = false,
    this.gpsList = const [],
    this.myDestination,
    this.startDestination,
  });

  final List<Job> jobList;
  final List<Job> selectedJobList;
  final JobViewMode viewMode;
  final DateTime filterDate;
  final Gps? curLocation;
  final Map<String, List<Job>> jobListGroup;

  // final Map<String, List<Job>> selectedJobListGroup;
  final bool isHasConnection;
  final bool showJobDetails;
  final SmsTemplate? smsTemplate;
  final bool isInSelectedTab;
  final Gps? lastSelectedLatLng;
  final Gps? lastLatLng;
  final bool isMapUpdated;
  final bool isPositionUpdated;
  final Job? job;
  final bool isDisabled;
  final List<RepairParts> repairParts;
  final List<String> roles;
  final SuggestedRoute? suggestedRoute;
  final bool isOptimizeRouteWarning;
  final bool isOptimizeRoute;
  final List<Gps> gpsList;
  final Gps? myDestination;
  final Gps? startDestination;

  @override
  String toString() {
    return 'JobListData{isHasConnection: $isHasConnection}';
  }

  JobListData copyWith({
    List<Job>? jobList,
    List<Job>? selectedJobList,
    JobViewMode? viewMode,
    DateTime? filterDate,
    Gps? curLocation,
    Map<String, List<Job>>? jobListGroup,
    bool? isHasConnection,
    bool? showJobDetails,
    SmsTemplate? smsTemplate,
    bool? isInSelectedTab,
    Gps? lastSelectedLatLng,
    Gps? lastLatLng,
    bool? isMapUpdated,
    bool? isPositionUpdated,
    Job? job,
    bool? isDisabled,
    List<RepairParts>? repairParts,
    List<String>? roles,
    SuggestedRoute? suggestedRoute,
    bool? isOptimizeRouteWarning,
    bool? isOptimizeRoute,
    List<Gps>? gpsList,
    Gps? myDestination,
    Gps? startDestination,
  }) {
    return JobListData(
      startDestination: startDestination ?? this.startDestination,
      jobList: jobList ?? this.jobList,
      selectedJobList: selectedJobList ?? this.selectedJobList,
      viewMode: viewMode ?? this.viewMode,
      filterDate: filterDate ?? this.filterDate,
      curLocation: curLocation ?? this.curLocation,
      jobListGroup: jobListGroup ?? this.jobListGroup,
      isHasConnection: isHasConnection ?? this.isHasConnection,
      showJobDetails: showJobDetails ?? this.showJobDetails,
      smsTemplate: smsTemplate ?? this.smsTemplate,
      isInSelectedTab: isInSelectedTab ?? this.isInSelectedTab,
      lastSelectedLatLng: lastSelectedLatLng ?? this.lastSelectedLatLng,
      lastLatLng: lastLatLng ?? this.lastLatLng,
      isMapUpdated: isMapUpdated ?? this.isMapUpdated,
      isPositionUpdated: isPositionUpdated ?? this.isPositionUpdated,
      job: job ?? this.job,
      isDisabled: isDisabled ?? this.isDisabled,
      repairParts: repairParts ?? this.repairParts,
      roles: roles ?? this.roles,
      suggestedRoute: suggestedRoute ?? this.suggestedRoute,
      isOptimizeRouteWarning:
          isOptimizeRouteWarning ?? this.isOptimizeRouteWarning,
      isOptimizeRoute: isOptimizeRoute ?? this.isOptimizeRoute,
      gpsList: gpsList ?? this.gpsList,
      myDestination: myDestination ?? this.myDestination,
    );
  }

  JobListData copyWithMyDestination(
      {List<Job>? jobList,
      List<Job>? selectedJobList,
      JobViewMode? viewMode,
      DateTime? filterDate,
      Gps? curLocation,
      Map<String, List<Job>>? jobListGroup,
      bool? isHasConnection,
      bool? showJobDetails,
      SmsTemplate? smsTemplate,
      bool? isInSelectedTab,
      Gps? lastSelectedLatLng,
      Gps? lastLatLng,
      bool? isMapUpdated,
      bool? isPositionUpdated,
      Job? job,
      bool? isDisabled,
      List<RepairParts>? repairParts,
      List<String>? roles,
      SuggestedRoute? suggestedRoute,
      bool? isOptimizeRouteWarning,
      bool? isOptimizeRoute,
      List<Gps>? gpsList,
      Gps? myDestination,
      Gps? startDestination}) {
    return JobListData(
      startDestination: startDestination ?? this.startDestination,
      jobList: jobList ?? this.jobList,
      selectedJobList: selectedJobList ?? this.selectedJobList,
      viewMode: viewMode ?? this.viewMode,
      filterDate: filterDate ?? this.filterDate,
      curLocation: curLocation ?? this.curLocation,
      jobListGroup: jobListGroup ?? this.jobListGroup,
      isHasConnection: isHasConnection ?? this.isHasConnection,
      showJobDetails: showJobDetails ?? this.showJobDetails,
      smsTemplate: smsTemplate ?? this.smsTemplate,
      isInSelectedTab: isInSelectedTab ?? this.isInSelectedTab,
      lastSelectedLatLng: lastSelectedLatLng ?? this.lastSelectedLatLng,
      lastLatLng: lastLatLng ?? this.lastLatLng,
      isMapUpdated: isMapUpdated ?? this.isMapUpdated,
      isPositionUpdated: isPositionUpdated ?? this.isPositionUpdated,
      job: job ?? this.job,
      isDisabled: isDisabled ?? this.isDisabled,
      repairParts: repairParts ?? this.repairParts,
      roles: roles ?? this.roles,
      suggestedRoute: suggestedRoute,
      isOptimizeRouteWarning:
          isOptimizeRouteWarning ?? this.isOptimizeRouteWarning,
      isOptimizeRoute: isOptimizeRoute ?? this.isOptimizeRoute,
      gpsList: gpsList ?? this.gpsList,
      myDestination: myDestination,
    );
  }
}

// extension JobListDataExtenstion on JobListData {}

@immutable
abstract class JobListState {
  final JobListData data;

  const JobListState(this.data);

  @override
  String toString() {
    return 'JobListState{data: $data}';
  }
}

class JobListMapUpdateSuccess extends JobListState {
  const JobListMapUpdateSuccess(JobListData data) : super(data);
}

class JobListLoading extends JobListState {
  const JobListLoading(JobListData data) : super(data);
}

class JobListInitialSuccess extends JobListState {
  const JobListInitialSuccess(JobListData data) : super(data);
}

class JobListBSUpdateSuccess extends JobListState {
  const JobListBSUpdateSuccess(JobListData data) : super(data);
}

class JobListLoadSuccess extends JobListState {
  const JobListLoadSuccess(JobListData data) : super(data);
}

class JobListBusy extends JobListState {
  const JobListBusy(JobListData data) : super(data);
}

class JobSelectSuccess extends JobListState {
  const JobSelectSuccess(JobListData data) : super(data);
}


class JobCustomerCommentLoaded extends JobListState {
  const JobCustomerCommentLoaded(JobListData data) : super(data);
}

class JobUnSelectSuccess extends JobListState {
  const JobUnSelectSuccess(JobListData data) : super(data);
}

class JobMapGpsUpdateSuccess extends JobListState {
  const JobMapGpsUpdateSuccess(JobListData data) : super(data);
}

class JobStartSuccess extends JobListState {
  const JobStartSuccess(JobListData data) : super(data);
}

class JobListEmpty extends JobListState {
  const JobListEmpty(JobListData data) : super(data);
}

class JobListNetworkStatusUpdate extends JobListState {
  const JobListNetworkStatusUpdate({
    required JobListData data,
    required this.isHasConnection,
  }) : super(data);
  final bool isHasConnection;
}

class JobPostCommentSuccess extends JobListState {
  const JobPostCommentSuccess({required JobListData data}) : super(data);
}

class JobPostCustomerCommentSuccess extends JobListState {
  const JobPostCustomerCommentSuccess({required JobListData data}) : super(data);
}

class JobListEmailSendSuccess extends JobListState {
  const JobListEmailSendSuccess(JobListData data) : super(data);
}

class JobListSMSSendSuccess extends JobListState {
  const JobListSMSSendSuccess(JobListData data) : super(data);
}

class JobListLoadFailure extends JobListState {
  const JobListLoadFailure({required JobListData data, required this.error})
      : super(data);

  final String error;
}

class JobListLatePayrollFailure extends JobListState {
  const JobListLatePayrollFailure({
    required JobListData data,
    required this.title,
    required this.message,
  }) : super(data);

  final String title;

  final String message;
}

class JobListEmailSendFailed extends JobListState {
  const JobListEmailSendFailed({
    required JobListData data,
    required this.error,
  }) : super(data);

  final String error;
}

class JobListError extends JobListState {
  const JobListError({
    required JobListData data,
    required this.error,
  }) : super(data);

  final String error;
}

class JobListWaring extends JobListState {
  const JobListWaring({
    required JobListData data,
    required this.error,
  }) : super(data);

  final String error;
}

class JobListRequestPostSuccess extends JobListState {
  const JobListRequestPostSuccess(JobListData data) : super(data);
}

class JobListRequestSaveSuccess extends JobListState {
  const JobListRequestSaveSuccess(JobListData data) : super(data);
}

class JobListSortSuccess extends JobListState {
  const JobListSortSuccess(JobListData data) : super(data);
}
