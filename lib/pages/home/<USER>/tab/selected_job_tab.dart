import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/header_bar/header_bar_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/share/fade_indexed_stack.dart';
import 'package:ticking_app/pages/home/<USER>/share/google_map_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_header_bar_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/selected_job_widget.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

import '../../../../widgets/button/common_primary_button.dart';

class SelectedJobTab extends StatefulWidget {
  const SelectedJobTab({Key? key}) : super(key: key);

  @override
  _SelectedJobTabState createState() => _SelectedJobTabState();
}

class _SelectedJobTabState extends State<SelectedJobTab> {
  late final JobListBloc _jobListBloc;
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _jobListBloc = BlocProvider.of<JobListBloc>(context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grey4,
      body: BlocProvider(
        create: (BuildContext context) => HeaderBarBloc(),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            JobHeaderBarWidget(
              title: 'Selected Jobs',
              showFilter: false,
              leadingWidget: _buildOptimizeRouteBtn(),
            ).pb16(),
            BlocBuilder<HeaderBarBloc, HeaderBarState>(
              builder: (context, state) {
                return _buildPages(state.isListType);
              },
            ).expand(),
          ],
        ),
      ),
    );
  }

  Widget _buildPages(bool isListType) {
    return BlocBuilder<JobListBloc, JobListState>(
        buildWhen: (_, current) => ![
              JobListBSUpdateSuccess,
              // JobListMapUpdateSuccess,
            ].contains(current.runtimeType),
        builder: (context, state) {
          switch (state.runtimeType) {
            case JobListLoading:
              return const Center(
                child: LoadingIndicator(),
              );
          }
          return FadeIndexedStack(
            index: isListType ? 0 : 1,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildOptimizeRouteRow(),
                  Expanded(child: _buildListView(state)),
                ],
              ),
              if (state is JobListLoading) const SizedBox(),
              if (state is! JobListLoading)
                Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOptimizeRouteRow(),
                    Expanded(
                      child: GoogleMapWidget(
                        isSelected: true,
                        isOptimizeRoute: state.data.isOptimizeRoute,
                        startPosition: state.data.startDestination,
                        gpsList: state.data.gpsList,
                        myDestination: state.data.myDestination,
                        curLocation: state.data.curLocation,
                        jobs: state.data.selectedJobList,
                        selectedJobs: state.data.selectedJobList,
                        smsTemplate: state.data.smsTemplate,
                        isUpdated: state.data.isMapUpdated,
                        isPositionUpdated: state.data.isPositionUpdated,
                      ),
                    ),
                  ],
                ),
            ],
          );
        });
  }

  Widget _buildOptimizeRouteBtn() {
    return CommonPrimaryButton(
      padding: const EdgeInsets.symmetric(vertical: 11, horizontal: 8),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Assets.icon.sort.svg(width: 25, height: 25),
          'Optimize Route'
              .text
              .textStyle(Theme.of(context).textTheme.bodyText1!)
              .size(Dimens.text_XL)
              .color(AppColors.materialWhite)
              .fontWeight(FontWeight.w600)
              .make(),
        ],
      ),
      onPressed: () {
        _jobListBloc.add(JobSelectSorted());
      },
    );
  }

  Widget _buildOptimizeRouteRow() {
    return BlocBuilder<JobListBloc, JobListState>(builder: (context, state) {
      if (state.data.isOptimizeRouteWarning == true &&
          state.data.isOptimizeRoute == true) {
        return Text(
          'Recommend Re-Optimizing',
          overflow: TextOverflow.ellipsis,
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey,
                fontWeight: FontWeight.w600,
                // height:
              ),
          maxLines: 3,
        ).pLTRB(16, 0, 16, 16);
      }
      return const SizedBox();
    });
  }

  Widget _buildListView(JobListState state) {
    final jobList = state.data.selectedJobList;

    if (jobList.isEmpty) {
      return _buildRefreshWidget(
        child: ListView(
          children: [
            _buildEmpty(),
          ],
        ),
      );
    }

    return _buildList(
        curLocation: state.data.curLocation,
        selectedJobList: state.data.selectedJobList,
        // groupDate: state.data.selectedJobListGroup,
        smsTemplate: state.data.smsTemplate);
  }

  Widget _buildRefreshWidget({required Widget child}) {
    return RefreshIndicator(
      onRefresh: () async {
        _jobListBloc.add(JobListRefreshed());
      },
      child: child,
    );
  }

  ///Build job list
  Widget _buildList({
    Gps? curLocation,
    required List<Job> selectedJobList,
    // required Map<String, List<Job>> groupDate,
    SmsTemplate? smsTemplate,
  }) {
    return _buildRefreshWidget(
      child: SizedBox(
        height: double.infinity,
        child: Stack(
          children: [

            Scrollbar(
              controller: _scrollController,
              child: ListView.builder(
                controller: _scrollController,
                itemBuilder: (context, index) {
                  Job job = selectedJobList[index];
                  return SelectedJobWidget(
                    group: (job.scheduleDate ?? DateTime.now()).dateGroupName,
                    smsTemplate: smsTemplate,
                    // job: groupDate[groupItemKey]![index],
                    curLocation: curLocation,
                    isSelected: true,
                    onSelected: (Job job) {
                      _jobListBloc.add(JobSelected(job));
                    },
                    onUnSelected: (Job job) {
                      _jobListBloc.add(JobUnSelected(job));
                    },
                    job: job,
                  ).pb(value: 20);
                },
                itemCount: selectedJobList.length,
              ),
            ),
            Container(
              // color: AppColors.grey4,
              // padding: const EdgeInsets.symmetric(horizontal: Dimens.pad),
              child: Text(
                'Distance',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      color: AppColors.grey,
                      fontWeight: FontWeight.w600,
                      // height:
                    ),
              ).px16().objectTopLeft().pb8(),
            ),
          ],
        ),
      ),
    );
  }

  ///Build empty job list
  Widget _buildEmpty() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container().pt(value: 50),
        Gaps.vGap50,
        Text(
          'No selected jobs.\n'
          'Please go to "Assigned Jobs" to select jobs.',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey,
                fontWeight: FontWeight.w600,
              ),
          textAlign: TextAlign.center,
        ).centered(),
      ],
    );
  }
}
