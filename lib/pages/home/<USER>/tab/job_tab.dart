import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sticky_header/flutter_sticky_header.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/header_bar/header_bar_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/share/fade_indexed_stack.dart';
import 'package:ticking_app/pages/home/<USER>/share/google_map_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_header_bar_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_widget.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
// import 'package:pull_to_refresh/pull_to_refresh.dart';

class JobTab extends StatefulWidget {
  const JobTab({Key? key}) : super(key: key);

  @override
  _JobTabState createState() => _JobTabState();
}

class _JobTabState extends State<JobTab> {
  late final JobListBloc _jobListBloc;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    _jobListBloc = BlocProvider.of<JobListBloc>(context);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grey4,
      body: BlocProvider(
        create: (BuildContext context) => HeaderBarBloc(),
        child: Column(
          children: [
            const JobHeaderBarWidget(
              title: 'Assigned Jobs',
            ).pb16(),
            BlocBuilder<HeaderBarBloc, HeaderBarState>(
              builder: (context, state) {
                return _buildPages(state.isListType);
              },
            ).expand(),
          ],
        ),
      ),
    );
  }

  Widget _buildPages(bool isListType) {
    return BlocBuilder<JobListBloc, JobListState>(
        buildWhen: (_, current) => ![
              JobListBSUpdateSuccess,
              // JobListMapUpdateSuccess,
            ].contains(current.runtimeType),
        builder: (context, state) {
          switch (state.runtimeType) {
            case JobListLoading:
              return const Center(
                child: LoadingIndicator(),
              );
            case JobListLatePayrollFailure:
              final data = state as JobListLatePayrollFailure;
              return Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Assets.icon.illustrations.svg(),
                    Text(
                      data.title,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: const Color(0xFFF2002C),
                            fontWeight: FontWeight.w900,
                            fontSize: 18,
                          ),
                    ).pb(value: 8).pt(value: 16),
                    Text(
                      data.message,
                      textAlign: TextAlign.center,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: const Color(0xFF798287),
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                          ),
                    ).pr(value: 16).pl(value: 16),
                  ],
                ),
              );

            // case JobListLoadFailure:
            //   return ToastUtils.showShortTimeToast(
            //       msg: 'Failure Load Data! Please try again!');
          }
          return FadeIndexedStack(
            index: isListType ? 0 : 1,
            children: [
              _buildListView(state),
              if (state is JobListLoading) const SizedBox(),
              if (state is! JobListLoading)
                GoogleMapWidget(
                  previousGps: state.data.lastLatLng,
                  isUpdated: state.data.isMapUpdated,
                  jobs: state.data.jobList,
                  selectedJobs: state.data.selectedJobList,
                  curLocation: state.data.curLocation,
                  smsTemplate: state.data.smsTemplate,
                  isPositionUpdated: state.data.isPositionUpdated,
                ),
            ],
          );
        });
  }

  Widget _buildListView(JobListState state) {
    final jobList = state.data.jobList;

    if (jobList.isEmpty) {
      return _buildRefreshWidget(
        child: ListView(
          children: [
            _buildEmpty(),
          ],
        ),
      );
    }

    return _buildList(
        curLocation: state.data.curLocation,
        selectedJobList: state.data.selectedJobList,
        groupDate: state.data.jobListGroup,
        smsTemplate: state.data.smsTemplate);
  }

  Widget _buildRefreshWidget({required Widget child}) {
    return RefreshIndicator(
      onRefresh: () async {
        _jobListBloc.add(JobListRefreshed());
      },
      child: child,
    );
  }

  ///Build job list
  Widget _buildList({
    Gps? curLocation,
    required List<Job> selectedJobList,
    required Map<String, List<Job>> groupDate,
    SmsTemplate? smsTemplate,
  }) {
    return _buildRefreshWidget(
      child: Stack(
        children: [
          PrimaryScrollController(
            controller: _scrollController,
            child: Scrollbar(
                child: CustomScrollView(
              slivers: groupDate.keys.map((key) {
                final groupItemKey = key;
                return SliverStickyHeader.builder(
                  // key: GlobalKey(),
                  // overlapHeaders: true,
                  builder: (context, state) {
                    return Container(
                      color: AppColors.grey4,
                      // padding: const EdgeInsets.symmetric(horizontal: Dimens.pad),
                      child: Text(
                        groupItemKey,
                        style: Theme.of(context).textTheme.bodyText1?.copyWith(
                              color: AppColors.grey,
                              fontWeight: FontWeight.w600,
                              // height:
                            ),
                      ).centered().px16().pOnly(left: 65).pb8(),
                    );
                  },
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) => JobWidget(
                        group: groupItemKey,
                        smsTemplate: smsTemplate,
                        job: groupDate[groupItemKey]![index],
                        curLocation: curLocation,
                        isSelected: selectedJobList.any(
                          (element) =>
                              element.id == groupDate[groupItemKey]![index].id,
                        ),
                        onSelected: (Job job) {
                          _jobListBloc.add(JobSelected(job));
                        },
                        onUnSelected: (Job job) {
                          _jobListBloc.add(JobUnSelected(job));
                        },
                      ).pb(value: 20),
                      childCount: groupDate[groupItemKey]!.length,
                    ),
                  ),
                );
              }).toList(),
            ).backgroundColor(AppColors.grey4)),
          ),
          Container(
            // color: AppColors.grey4,
            // padding: const EdgeInsets.symmetric(horizontal: Dimens.pad),
            child: Text(
              'Distance',
              style: Theme.of(context).textTheme.bodyText1?.copyWith(
                    color: AppColors.grey,
                    fontWeight: FontWeight.w600,
                    // height:
                  ),
            ).px16().objectTopLeft().pb8(),
          ),
        ],
      ),
    );
  }

  ///Build empty job list
  Widget _buildEmpty() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Image.asset(Assets.image.assignedJobsEmpty.path).px(90).pt(value: 50),
        Gaps.vGap50,
        Text(
          'Congratulations! \n'
          'You have no assigned jobs for today!',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey,
                fontWeight: FontWeight.w600,
              ),
          textAlign: TextAlign.center,
        ).centered(),
      ],
    );
  }
}
