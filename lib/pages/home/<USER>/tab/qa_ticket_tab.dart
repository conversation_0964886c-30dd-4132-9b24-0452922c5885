import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/calendar/app_calendar.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_issue_widget.dart';

class QATicketTab extends StatefulWidget {
  const QATicketTab({Key? key}) : super(key: key);

  @override
  _QATicketTabState createState() => _QATicketTabState();
}

class _QATicketTabState extends State<QATicketTab> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('QA')
          // Btn.main(
          //   text: 'Logout',
          //   onPressed: () {
          //     BlocProvider.of<AuthBloc>(context).add(AuthLogout());
          //     // App.pushNamedAndPopUntil(AppRoutes.login, null, '/');
          //   },
          // ),
          // 40.toVSizeBox(),
          // Btn.main(
          //   text: 'Calender',
          //   onPressed: () {
          //     showCalendar(context, onSelectedDate: (value) {
          //       print(value);
          //     });
          //   },
          // ),
          // 40.toVSizeBox(),
          // Btn.main(
          //   text: 'Show Issue Dialog',
          //   onPressed: () {
          //     showIssueDialog(context,
          //         email: '<EMAIL>', phoneNumber: '**********');
          //   },
          // ),
        ],
      ),
    );
  }
}
