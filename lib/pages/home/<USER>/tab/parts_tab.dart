import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/pages/details/ui/job_auto_calculate.dart';
import 'package:ticking_app/pages/details/ui/job_details_page.dart';
import 'package:ticking_app/pages/details/ui/job_task_check.dart';
import 'package:ticking_app/pages/details/ui/job_task_input.dart';
import 'package:ticking_app/pages/details/ui/job_task_selection.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/share/user_avatar_widget.dart';
import 'package:ticking_app/pages/qr_code_view/product_input_page.dart';
import 'package:ticking_app/pages/repair_ticket/bloc/repair_ticket_bloc.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/input_format.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';

///Usage for PartsList Tab at home
class PartsListTab extends StatefulWidget {
  const PartsListTab({Key? key}) : super(key: key);

  @override
  _PartsListTabState createState() => _PartsListTabState();
}

class _PartsListTabState extends State<PartsListTab> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.grey4,
      appBar: AppBar(
        title: Text(
          'Parts list',
          style: Theme.of(context)
              .textTheme
              .headline6
              ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        backgroundColor: AppColors.grey4,
        actions: [
          const UserAvatarWidget().pr(value: 10),
        ],
        leading: CircleButton(
          child: Assets.icon.refreshEnable.svg(
            color: Theme.of(context).primaryColor,
          ),
          onPressed: () {
            // throw Exception("test sentry 1");
            BlocProvider.of<JobListBloc>(context).add(JobListRefreshed());
          },
          size: Dimens.ic_XL,
        ),
      ),
      body: BlocBuilder<JobListBloc, JobListState>(
        builder: (context, state) {
          List<RepairParts> repairParts = state.data.repairParts;
          repairParts.sortBy((element) => element.details?.name ?? '');
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('Estimated parts for the selected repair jobs.')
                  .pOnly(left: 16, top: 20, bottom: 20),
              Expanded(child: _buildPartList(repairParts)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPartList(List<RepairParts> repairParts) {
    if (repairParts.isEmpty) {
      return Center(
        child: Text(
          'No parts',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey,
                fontWeight: FontWeight.w600,
              ),
        ),
      );
    }
    return Scrollbar(
      child: ListView.builder(
        // shrinkWrap: true,
        padding: const EdgeInsets.only(top: 12, left: 16, right: 16),
        itemBuilder: (context, index) {
          return _OnlyReadPartItem(
            part: repairParts[index],
          );
        },
        itemCount: repairParts.length,
      ),
    );
  }
}

class _ReviewingEditPartItem extends StatelessWidget {
  const _ReviewingEditPartItem({
    Key? key,
    required this.part,
    this.initialValue = '',
  }) : super(key: key);

  final RepairParts part;
  final String initialValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: AppColors.grey4,
      ),
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            part.details?.name ?? '',
            style: Theme.of(context).textTheme.bodyText2?.copyWith(
                  fontWeight: FontWeight.w700,
                  fontSize: Dimens.text,
                ),
          ).pb(value: 2),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Text(
              'Estimated Qty: ${part.estimatedQuantity.convertIfInt2()}',
              style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    overflow: TextOverflow.ellipsis,
                    fontSize: Dimens.text_S,
                    fontWeight: FontWeight.w400,
                    height: 1.5,
                  ),
            ),
          ),
          Row(
            children: [
              Text(
                'Used : ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ).pr(value: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.grey11,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: AppColors.grey5),
                ),
                padding: const EdgeInsets.symmetric(
                  vertical: 4,
                  horizontal: 5,
                ),
                child: ConstrainedBox(
                  constraints: const BoxConstraints(
                    minWidth: 80,
                  ),
                  child: Align(
                    alignment: Alignment.bottomRight,
                    child: Text(
                      double.parse(initialValue ?? "0").toStringAsFixed(2),
                      style: Theme.of(context).textTheme.bodyText2?.copyWith(
                            color: AppColors.grey14,
                            fontSize: Dimens.text,
                            fontWeight: FontWeight.w700,
                            height: 1.5,
                          ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

///Item for parts list at [EditPartsTab]
class _EditPartItem extends StatelessWidget {
  const _EditPartItem({
    Key? key,
    required this.part,
    this.initialValue = '',
  }) : super(key: key);

  final RepairParts part;
  final String initialValue;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.grey4,
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      // margin: const EdgeInsets.all(4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: part.details?.name ?? '',
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: Dimens.text,
                      ),
                ),
                TextSpan(
                  text: " (Required)",
                  style: Theme.of(context)
                      .normalStyle
                      .copyWith(color: AppColors.redAccentColor, fontSize: 15),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              children: [
                Text(
                  'Estimated Qty: ',
                  style: Theme.of(context).textTheme.bodyText2?.copyWith(
                      color: AppColors.grey,
                      fontSize: Dimens.text,
                      fontWeight: FontWeight.w400,
                      height: 1.5),
                ),
                Expanded(
                  child: Text(
                    '${part.estimatedQuantity.convertIfInt2()}',
                    style: Theme.of(context).textTheme.bodyText2?.copyWith(
                        color: AppColors.grey,
                        fontSize: Dimens.text,
                        fontWeight: FontWeight.w400,
                        height: 1.5),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Text(
                'Used : ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
              13.toHSizeBox(),
              SizedBox(
                width: 80,
                child: TextFormField(
                  initialValue: initialValue,
                  style: const TextStyle(fontSize: 14),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    if (part.id != null) {
                      context.read<RepairTicketBloc>().add(
                            RepairTicketTextChanged(
                              text: value,
                              repairId: part.id!,
                            ),
                          );
                    }
                  },
                  onEditingComplete: () {
                    FocusScope.of(context).unfocus();
                  },
                  inputFormatters: <TextInputFormatter>[
                    RegExInputFormatter.withRegex(
                        r'^([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+[.,]?\d{0,2}')),
                    LengthLimitingTextInputFormatter(15),
                  ],
                  decoration: const InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.grey5)),
                    isDense: true,
                    isCollapsed: true,
                    contentPadding: EdgeInsets.all(8), // Added this
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

class _EditMaintenanceItem extends StatelessWidget {
  const _EditMaintenanceItem({
    Key? key,
    required this.maintenanceTask,
    required this.jobTaskTemplate,
  }) : super(key: key);

  final MaintenanceTask maintenanceTask;
  final JobTask? jobTaskTemplate;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.grey4,
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: maintenanceTask.name ?? '',
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: Dimens.text,
                      ),
                ),
                if (maintenanceTask.isRequired == true)
                  TextSpan(
                    text: " (Required)",
                    style: Theme.of(context).normalStyle.copyWith(
                        color: AppColors.redAccentColor, fontSize: 15),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Min: ${jobTaskTemplate?.domain[0] ?? ""}, Max: ${jobTaskTemplate?.domain[2] ?? ""}, Estimated: ${jobTaskTemplate?.defaultValue} ${jobTaskTemplate?.unit ?? ""}',
                    style: Theme.of(context).textTheme.bodyText2?.copyWith(
                        color: AppColors.grey,
                        fontSize: Dimens.text,
                        fontWeight: FontWeight.w400,
                        height: 1.5),
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Text(
                'Used : ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
              13.toHSizeBox(),
              SizedBox(
                width: 80,
                child: TextFormField(
                  initialValue: maintenanceTask.value ?? "",
                  style: const TextStyle(fontSize: 14),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {},
                  onEditingComplete: () {
                    FocusScope.of(context).unfocus();
                  },
                  inputFormatters: <TextInputFormatter>[
                    RegExInputFormatter.withRegex(
                        r'^([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
                    FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+[.,]?\d{0,2}')),
                    LengthLimitingTextInputFormatter(15),
                  ],
                  decoration: const InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                        borderSide: BorderSide(color: AppColors.grey5)),
                    isDense: true,
                    isCollapsed: true,
                    contentPadding: EdgeInsets.all(8), // Added this
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}

///Parts List has input textField
class EditPartsTab extends StatefulWidget {
  const EditPartsTab({
    Key? key,
    required this.job,
    required this.isReviewing,
  }) : super(key: key);
  final Job job;
  final bool isReviewing;

  @override
  State<EditPartsTab> createState() => _EditPartsTabState();
}

class _EditPartsTabState extends State<EditPartsTab> {
  List<JobTask> listJobTemplates = [];
  @override
  void initState() {
    super.initState();
    clearData();
  }

  void _handleDataChanged({
    required JobTaskValue value,
    required JobMaintenanceTask maintenanceTask,
    required String jobTaskInputType,
  }) {
    context.read<RepairTicketBloc>().add(
          RepairTicketTextChanged(
            text: (value.value ?? "").toString(),
            change: value.isChangedValue,
            maintenanceId: maintenanceTask.maintenanceTask.id,
            jobTaskInputType: jobTaskInputType,
          ),
        );
  }

  void clearData() {
    CacheService cacheService = GetIt.I<CacheService>();
    var data = cacheService.getJobTaskTemplates;
    setState(() {
      listJobTemplates = data;
    });
    List<RepairParts> repairParts = widget.job.details.repairParts;
    for (var item in repairParts) {
      if (item.consumedQuantity == 0) {
        item.consumedQuantity = null;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<RepairParts> repairParts = widget.job.details.repairParts;
    List<MaintenanceTask> maintenanceTasks =
        widget.job.details.maintenanceTasks;
    bool isReviewing = widget.isReviewing;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 12),
          itemBuilder: (context, index) {
            RepairParts part = repairParts[index];

            if (part.details?.tracking != 'none' &&
                part.details?.tracking != null) {
              return _buildWrapReviewing(
                child: _ScannerSerialNumberPartItem(
                  part: part,
                  initialValue: part.consumedQuantity == null
                      ? ""
                      : part.consumedQuantity!.toStringAsFixed(0),
                ),
              );
            }
            if (isReviewing) {
              return _buildWrapReviewing(
                child: AbsorbPointer(
                  absorbing: widget.isReviewing,
                  child: _ReviewingEditPartItem(
                    part: part,
                    initialValue: part.consumedQuantity == null
                        ? "0.0"
                        : part.consumedQuantity.toString(),
                  ),
                ),
              );
            }
            return _buildWrap(
              isReviewing: isReviewing,
              child: _EditPartItem(
                part: part,
                initialValue: part.consumedQuantity == null
                    ? ""
                    : part.consumedQuantity.toString(),
              ),
            );
          },
          itemCount: repairParts.length,
        ),
        if (maintenanceTasks.isNotEmpty)
          ListView.builder(
            shrinkWrap: true,
            itemCount: maintenanceTasks.length,
            padding:const EdgeInsets.only(bottom: 16),
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final jobTaskTemplate = listJobTemplates.firstWhere((element) =>
                  element.id == maintenanceTasks[index].templateId);
              JobMaintenanceTask maintenanceTask = JobMaintenanceTask(
                maintenanceTask: maintenanceTasks[index],
                jobTaskTemplate: jobTaskTemplate,
              );
              switch (jobTaskTemplate.inputType) {
                case JobTaskInputType.number:
                  return _buildWrap(
                    isReviewing: isReviewing,
                    child: JobTaskInput(
                      maintenanceTask: maintenanceTask,
                      isChange: maintenanceTasks[index].isChange,
                      defaultValue: isReviewing == true
                          ? maintenanceTask.maintenanceTask.value
                          : null,
                      onChanged: (value) {
                        _handleDataChanged(
                          value: value,
                          maintenanceTask: maintenanceTask,
                          jobTaskInputType: JobTaskInputType.number,
                        );
                      },
                      isReviewing: isReviewing,
                    ),
                  );
                case JobTaskInputType.calculation:
                  return _buildWrap(
                    isReviewing: isReviewing,
                    child: AutoCalculate(
                      maintenanceTask: maintenanceTask,
                      isReviewing: isReviewing,
                    ),
                  );
                case JobTaskInputType.checkbox:
                  return _buildWrap(
                    isReviewing: isReviewing,
                    child: JobTaskCheckbox(
                      maintenanceTask: maintenanceTask,
                      isChange: maintenanceTasks[index].isChange,
                      onChanged: (value) {
                        _handleDataChanged(
                          value: value,
                          maintenanceTask: maintenanceTask,
                          jobTaskInputType: JobTaskInputType.checkbox,
                        );
                      },
                      defaultValue: isReviewing == true
                          ? maintenanceTask.maintenanceTask.value.toString()
                          : null,
                      isReviewing: isReviewing,
                    ),
                  );
                case JobTaskInputType.selection:
                  return _buildWrap(
                    isReviewing: isReviewing,
                    child: JobTaskSelection(
                      maintenanceTask: maintenanceTask,
                      onChanged: (value) {
                        _handleDataChanged(
                          value: value,
                          maintenanceTask: maintenanceTask,
                          jobTaskInputType: JobTaskInputType.selection,
                        );
                      },
                      defaultValue: isReviewing == true
                          ? maintenanceTask.maintenanceTask.value
                          : null,
                      isReviewing: isReviewing,
                    ),
                  );
              }
              return const SizedBox();
            },
          ),
      ],
    );
  }
}

Widget _buildWrap({required Widget child, required bool? isReviewing}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      color: Colors.white,
    ),
    margin: EdgeInsets.only(
      top: 16,
      left: isReviewing == true ? 16 : 8,
      right: isReviewing == true ? 16 : 8,
    ),
    padding: const EdgeInsets.all(16),
    // margin: const EdgeInsets.symmetric(horizontal: 16),
    child: child,
  );
}

Widget _buildWrapReviewing({required Widget child}) {
  return Container(
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(8),
      color: Colors.white,
    ),
    margin: const EdgeInsets.only(
      top: 16,
      left: 16,
      right: 16,
    ),
    padding: const EdgeInsets.only(
      top: 16,
    ),
    // margin: const EdgeInsets.symmetric(horizontal: 16),
    child: child,
  );
}

///Serial Number Item
class _ScannerSerialNumberPartItem extends StatelessWidget {
  const _ScannerSerialNumberPartItem(
      {Key? key, required this.part, this.initialValue = ''})
      : super(key: key);

  final RepairParts part;
  final String initialValue;

  @override
  Widget build(BuildContext context) {
    List<String> trackingReferences = part.trackingReferences ?? [];

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColors.grey4,
      ),
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                part.details?.name ?? '',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w700,
                      overflow: TextOverflow.ellipsis,
                      fontSize: Dimens.text,
                    ),
              ),
              TextButton(
                onPressed: () async {
                  String? barcode = await Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => const ProductInputPage()));

                  if (barcode != '' && barcode != null && part.id != null) {
                    context.read<RepairTicketBloc>().add(
                          RepairTicketSerialNumberTagAssigned(
                            tagValue: barcode,
                            repairId: part.id!,
                          ),
                        );
                  }
                },
                child: Text(
                  'Enter S/N',
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        fontWeight: FontWeight.w700,
                        overflow: TextOverflow.ellipsis,
                        fontSize: Dimens.text,
                        color: Theme.of(context).primaryColor,
                      ),
                ),
              ),
            ],
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                'Estimated Qty: ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    overflow: TextOverflow.ellipsis,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
              Expanded(
                child: Text(
                  '${part.estimatedQuantity.convertIfInt2()}',
                  style: Theme.of(context).textTheme.bodyText2?.copyWith(
                      color: AppColors.grey,
                      overflow: TextOverflow.ellipsis,
                      fontSize: Dimens.text,
                      fontWeight: FontWeight.w400,
                      height: 1.5),
                ),
              ),
              Text(
                'Used : ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    overflow: TextOverflow.ellipsis,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
              13.toHSizeBox(),
              Opacity(
                opacity: 0.5,
                child: SizedBox(
                  width: 60,
                  child: TextFormField(
                    initialValue: initialValue,
                    key: Key(initialValue.toString()),
                    enabled: false,
                    style: const TextStyle(fontSize: 14),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      if (part.id != null) {
                        context.read<RepairTicketBloc>().add(
                              RepairTicketTextChanged(
                                text: value,
                                repairId: part.id!,
                              ),
                            );
                      }
                    },
                    onEditingComplete: () {
                      FocusScope.of(context).unfocus();
                    },
                    inputFormatters: <TextInputFormatter>[
                      RegExInputFormatter.withRegex(
                          r'^([0-9]+([.][0-9]*)?|[.][0-9]+)$'),
                      // //To remove first '0'
                      FilteringTextInputFormatter.deny(RegExp(r'^0+')),
                      LengthLimitingTextInputFormatter(15),
                    ],
                    decoration: const InputDecoration(
                      filled: true,
                      fillColor: Colors.white,
                      border: OutlineInputBorder(
                          borderSide: BorderSide(color: AppColors.grey5)),
                      isDense: true,
                      isCollapsed: true,
                      contentPadding: EdgeInsets.all(8), // Added this
                    ),
                  ),
                ),
              ),
            ],
          ),
          8.toVSizeBox(),
          if (trackingReferences.isNotEmpty)
            Wrap(
              runSpacing: 5.0,
              spacing: 5.0,
              children: trackingReferences
                  .map((item) => Container(
                      decoration: BoxDecoration(
                          color: AppColors.neutral1Color,
                          borderRadius:
                              BorderRadius.circular(Dimens.rad_XXXXL)),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ConstrainedBox(
                            constraints: BoxConstraints(
                                maxWidth:
                                    MediaQuery.of(context).size.width - 150),
                            child: item.text
                                .color(Theme.of(context).primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),
                          InkWell(
                            onTap: () {
                              context.read<RepairTicketBloc>().add(
                                    RepairTicketSerialNumberTagAssigned(
                                        tagValue: item,
                                        repairId: part.id!,
                                        isDelete: true),
                                  );
                            },
                            child: Icon(
                              Icons.close,
                              size: 15,
                              color: Theme.of(context).primaryColor,
                            ).pr(value: 16),
                          )
                        ],
                      )))
                  .toList(),
            ),
        ],
      ),
    );
  }
}

///Usage for bottom sheet
class PartsTab extends StatelessWidget {
  const PartsTab({Key? key, required this.job}) : super(key: key);
  final Job job;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<JobListBloc, JobListState>(
      builder: (context, state) {
        List<RepairParts> repairParts = job.details.repairParts;
        repairParts.sortBy((element) => element.details?.name ?? '');
        //Todo: Test many repairParts
        // List<RepairParts> repairParts = List<RepairParts>.generate(
        //     40,
        //         (counter) => RepairParts(
        //         consumedQuantity: 0,
        //         estimatedQuantity: 0,
        //         details: RepairPartsDetails(name: 'Item $counter')));

        return ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          itemBuilder: (context, index) {
            RepairParts part = repairParts[index];
            return _OnlyReadPartItem(
              part: part,
            );
          },
          itemCount: repairParts.length,
          separatorBuilder: (BuildContext context, int index) {
            return const Divider(
              color: AppColors.grey4,
            );
          },
        );
      },
    );
  }
}

///Item for parts list at [PartsTab][PartsListTab].
class _OnlyReadPartItem extends StatelessWidget {
  const _OnlyReadPartItem({Key? key, required this.part}) : super(key: key);
  final RepairParts part;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 16),
      margin: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Text(
              part.details?.name ?? '',
              style: Theme.of(context).textTheme.bodyText1?.copyWith(
                    fontWeight: FontWeight.w700,
                    overflow: TextOverflow.ellipsis,
                    fontSize: Dimens.text,
                  ),
            ).pr(value: 16),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.baseline,
            textBaseline: TextBaseline.alphabetic,
            children: [
              Text(
                'Estimated Quantity: ',
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                    color: AppColors.grey,
                    overflow: TextOverflow.ellipsis,
                    fontSize: Dimens.text,
                    fontWeight: FontWeight.w400,
                    height: 1.5),
              ),
              Text(
                '${part.estimatedQuantity.convertIfInt2()}',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w700,
                      overflow: TextOverflow.ellipsis,
                      fontSize: Dimens.text,
                    ),
              ),
            ],
          )
        ],
      ),
    );
  }
}
