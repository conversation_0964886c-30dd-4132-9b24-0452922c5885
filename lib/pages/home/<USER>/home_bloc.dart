import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:meta/meta.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';


part 'home_event.dart';

part 'home_state.dart';

class HomeBloc extends Bloc<HomeEvent, HomeState> {
  HomeBloc({   SecureConfigService? secureConfigService}) : super(const HomeState(0,   [])) {
    on<HomeTabChanged>(_onTabChanged);
    
     on<HomeLoaded>(_onLoaded);
    _secureConfigService = secureConfigService?? GetIt.I<SecureConfigService>();
  }
  late SecureConfigService _secureConfigService;

    Future<void> _onLoaded(
    HomeLoaded event,
    Emitter<HomeState> emit,
  ) async {
   emit(HomeState(0, _secureConfigService.userInfo?.roles ??  state.roles));
  }
  Future<void> _onTabChanged(
    HomeTabChanged event,
    Emitter<HomeState> emit,
  ) async {
    emit(HomeState(event.page, state.roles));
  }
}
