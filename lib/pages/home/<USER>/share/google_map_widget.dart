import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs.dart';
import 'package:ticking_app/pages/home/<USER>/share/jobs_map_bs.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:synchronized/synchronized.dart' as synchronized;

class GoogleMapWidget extends StatefulWidget {
  const GoogleMapWidget(
      {Key? key,
      required this.jobs,
      required this.selectedJobs,
      this.curLocation,
      this.previousGps,
      this.isSelected = false,
      this.isPositionUpdated = false,
      required this.isUpdated,
      this.smsTemplate,
      this.gpsList,
      this.myDestination,
      this.startPosition,
      this.isOptimizeRoute = false})
      : super(key: key);

  final List<Job> jobs;
  final List<Job> selectedJobs;
  final bool isSelected;
  final Gps? curLocation;
  final bool isUpdated;
  final bool isPositionUpdated;
  final Gps? previousGps;
  final SmsTemplate? smsTemplate;
  final List<Gps>? gpsList;
  final Gps? myDestination;
  final Gps? startPosition;
  final bool isOptimizeRoute;

  @override
  _GoogleMapWidgetState createState() => _GoogleMapWidgetState();
}

class _GoogleMapWidgetState extends State<GoogleMapWidget> {
  GoogleMapController? _mapController;
  LatLng _center = const LatLng(28.535517, 77.391029);
  final List<Polyline> _polylines = [];
  List<Marker> customMarkers = [];
  BitmapDescriptor? pinLocationIcon;

  @override
  void initState() {
    final curLocation = widget.curLocation;
    if (curLocation != null) {
      _center = LatLng(
        curLocation.latitude,
        curLocation.longitude,
      );
    }

    Future.delayed(const Duration(milliseconds: 3000), () {
      _lock.synchronized(() => _init());
    });

    super.initState();
  }

  @override
  void didUpdateWidget(covariant GoogleMapWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isUpdated) {
      BlocProvider.of<JobListBloc>(context).add(JobListMapUpdated(false));
      _lock.synchronized(() => _init(true));
    } else if (widget.isPositionUpdated) {
      BlocProvider.of<JobListBloc>(context).add(JobListPositionUpdated(false));
      _lock.synchronized(() => _init(false));
    }
  }

  List<Marker> mapBitmapsToMarkers(
      BitmapDescriptor bitmap, List<LatLng> positions) {
    return positions
        .map(
          (position) => Marker(
            markerId: MarkerId("${position.longitude}_${position.longitude}"),
            position: position,
            icon: bitmap,
            onTap: () {},
          ),
        )
        .toList();
  }

  final List<Marker> _markers = [];
  final List<LatLng> _listPositions = [];
  final List<LatLng> _listDrawLinePositions = [];
  final _lock = synchronized.Lock();

  Future<void> _init([bool isUpdated = true]) async {
    _listDrawLinePositions.clear();
    _listPositions.clear();
    _polylines.clear();
    _markers.clear();

    final Map<Gps, List<Job>> mapJobs = {};
    final Map<Gps, bool> mapSelectedGps = {};
    final jobs = List<Job>.from(widget.jobs);

    for (final job in jobs) {
      final jobGps = job.contact.gps;


      final isSelected = widget.isSelected
          ? widget.isSelected
          : widget.selectedJobs.any((element) => element.id == job.id);

      if (jobGps == null) {
        continue;
      }

      if (!mapJobs.containsKey(jobGps)) {
        mapJobs[jobGps] = [];
      }

      mapJobs[jobGps]?.add(job);

      if (isSelected) {
        mapSelectedGps[jobGps] = true;
      }
    }

    for (final jobGps in mapJobs.keys) {
      bool isSelected = mapSelectedGps[jobGps] ?? false;
      BitmapDescriptor? bitmap = await getMarkerIcon(
        imagePath: isSelected
            ? 'assets/image/selected_marker.png'
            : 'assets/image/marker.png',
        size: Platform.isIOS ? const Size(150, 150) : const Size(100, 100),
        gps: jobGps,
        isSelected: mapSelectedGps[jobGps] ?? false,
        number: mapJobs[jobGps]?.length ?? 1,
        // number: 999,
        // number: 99,
        // number: 9,
      );

      if (bitmap == null) {
        continue;
      }

      final marker = Marker(
        markerId: MarkerId("${jobGps.longitude}_${jobGps.longitude}"),
        position: LatLng(jobGps.latitude, jobGps.longitude),
        icon: bitmap,
        onTap: () {
          final jobs = mapJobs[jobGps];
          if (jobs == null) {
            return;
          }

          if (jobs.length == 1) {
            final job = jobs.first;
            String distance = '0 mi';
            if (widget.curLocation != null) {
              distance =
                  '${NumberUtils.formatToPattern(GeoLocatorUtils.distanceBetweenGps(widget.curLocation!, jobGps))}mi';
            }
            final String? groupName = job.scheduleDate?.dateGroupName;
            showJobDetailBS(
              job: job,
              distance: distance,
              group: groupName ?? '',
              smsTemplate: widget.smsTemplate,
              // onRepairPosted: (Job job, String description) {
              //   BlocProvider.of<JobListBloc>(context).add(
              //     JobRepairPosted(
              //       job: job,
              //       description: description,
              //     ),
              //   );
              // },
              onUnableToAccess: () {},
              onCommentDeleted: (JobComment comment, Job job) {
                BlocProvider.of<JobListBloc>(App.overlayContext!).add(
                  JobCommentDeleted(
                    job: job,
                    comment: comment,
                  ),
                );
              },
              onCommentEdit: (JobComment comment, Job job, String body) {
                BlocProvider.of<JobListBloc>(App.overlayContext!).add(
                  JobCommentUpdated(
                    job: job,
                    comment: comment,
                    body: body,
                  ),
                );
              },
            );
            return;
          }

          showJobsMapBS(jobs);
        },
      );

      _listPositions.add(LatLng(jobGps.latitude, jobGps.longitude));

      _markers.add(marker);
    }

    final curLocation = widget.curLocation;
    if (curLocation != null) {
      _listPositions.add(
        LatLng(
          curLocation.latitude,
          curLocation.longitude,
        ),
      );
    }
    final startPosition = widget.startPosition;
    if (startPosition != null && widget.isOptimizeRoute) {
      _listDrawLinePositions.insert(
        0,
        LatLng(
          startPosition.latitude,
          startPosition.longitude,
        ),
      );
    }

    ///doing
    List<Gps> gpsList = widget.gpsList ?? [];

    if (widget.isOptimizeRoute) {
      for (final gps in gpsList) {
        _listDrawLinePositions.add(LatLng(gps.latitude, gps.longitude));
      }
    }

    Gps? myDestination = widget.myDestination;
    if (myDestination != null && widget.isOptimizeRoute && gpsList.isNotEmpty) {
      final latitude = LatLng(
        myDestination.latitude,
        myDestination.longitude,
      );

      _listDrawLinePositions.add(latitude);

      BitmapDescriptor? bitmap = await getMarkerIcon(
        imagePath: 'assets/image/destination.png',
        isHomeIcon: true,
        size: Platform.isIOS ? const Size(150, 150) : const Size(100, 100),
        gps: myDestination,
        isSelected: false,
        number: 1,
      );

      if (bitmap != null) {
        final marker = Marker(
          markerId: MarkerId("${latitude.longitude}_${latitude.longitude}"),
          position: latitude,
          icon: bitmap,
          onTap: () {},
        );

        _markers.add(marker);
      }
    }

    if (widget.isSelected && widget.isOptimizeRoute && gpsList.isNotEmpty) {
      _polylines.add(
        Polyline(
          polylineId: const PolylineId('1'),
          points: _listDrawLinePositions,
          width: 1,
          color: AppColors.primaryColor,
        ),
      );
    }

    if (_mapController != null && _listPositions.isNotEmpty && isUpdated) {
      try {
        if (_listPositions.length == 1) {
          _mapController?.animateCamera(
            CameraUpdate.newLatLngZoom(_listPositions.first, 10),
          );
        } else {
          _mapController?.animateCamera(
            CameraUpdate.newLatLngBounds(_createBounds(_listPositions), 50),
          );
        }
      } catch (_) {}
    }

    if (mounted) {
      setState(() {});
    }
  }

  Future<BitmapDescriptor?> getMarkerIcon(
      {required String imagePath,
      required Size size,
      required Gps gps,
      required int number,
      required bool isSelected,
      bool isHomeIcon = false}) async {
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    // Add path for oval image
    // canvas.clipPath(Path()..addOval(oval));
    // final ByteData assetImageByteData = await rootBundle.load(imagePath);
    final textStyle = TextStyle(
      color: isSelected ? AppColors.primaryColor : AppColors.grey,
      fontSize: 30,
      fontWeight: FontWeight.w600,
    );

    String distance = '0 mi';

    if (widget.curLocation != null) {
      distance =
          '${NumberUtils.formatToPattern(GeoLocatorUtils.distanceBetweenGps(widget.curLocation!, gps))}mi';
    }

    final textSpan = TextSpan(
      text: distance,
      style: textStyle,
    );

    final numberTextSpan = TextSpan(
      text: number.toString(),
      style: textStyle.copyWith(
        color: number != 0 ? Colors.white : Colors.transparent,
        fontWeight: FontWeight.w400,
      ),
    );
    final numberJobPainter = TextPainter(
      text: numberTextSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(minWidth: 0, maxWidth: 200);
    // }

    double maxWidth =
        size.width > textPainter.width ? size.width : textPainter.width;
    numberJobPainter.layout(minWidth: 0, maxWidth: 100);
    // maxWidth =
    //     size.width > textPainter.width ? size.width : textPainter.width;
    maxWidth += numberJobPainter.width;
    double heightOffset = 10;
    double homeIconHeight = isHomeIcon ? 0 : 0;
    if (number >= 100 && Platform.isIOS) {
      heightOffset = 14;
    }
    // Oval for the image
    Rect oval = Rect.fromLTWH((maxWidth - size.width) / 2, 10, size.width,
        size.height + heightOffset + homeIconHeight);

    // Add image
    ui.Image image = await getImageFromPath(
        imagePath); // Alternatively use your own method to get the image

    paintImage(canvas: canvas, image: image, rect: oval, fit: BoxFit.fitWidth);

    // final boundPainter = Paint()..color = Colors.red.withOpacity(0.1);
    // Rect fullRect = Rect.fromLTWH(0, 0, maxWidth, size.height);
    // canvas.drawRect(fullRect, boundPainter);

    final xCenter = (maxWidth - textPainter.width) / 2;

    final yCenter = size.height + 10;

    final offset = Offset(xCenter, yCenter);

    textPainter.paint(canvas, offset);

    if (number < 10 && number > 1) {
      Offset numberOffset =
          Offset((maxWidth - numberJobPainter.width * 2 - 12), 12);

      Offset circleRadius = Offset(
          maxWidth -
              numberJobPainter.width * 2 +
              numberJobPainter.width / 2 -
              12,
          numberJobPainter.height / 2 + 12);
      double size = numberJobPainter.width + 5;
      if (Platform.isIOS) {
        numberOffset = Offset((maxWidth - numberJobPainter.width * 2 - 12), 14);

        circleRadius = Offset(
            maxWidth -
                numberJobPainter.width * 2 +
                numberJobPainter.width / 2 -
                12,
            numberJobPainter.height / 2 + 14);

        size = numberJobPainter.width / 2 + 20;
      }

      canvas.drawCircle(
        circleRadius,
        size,
        Paint()..color = Colors.red,
      );

      numberJobPainter.paint(canvas, numberOffset);
    }

    if (number >= 10 && number < 100) {
      Offset numberOffset = Offset(
          (maxWidth - numberJobPainter.width - numberJobPainter.width / 2), 12);

      Offset circleRadius = Offset(
          maxWidth - numberJobPainter.width, numberJobPainter.height / 2 + 12);

      double size = numberJobPainter.width / 2 + 10;
      if (Platform.isIOS) {
        numberOffset = Offset(
            (maxWidth -
                numberJobPainter.width -
                numberJobPainter.width / 2 -
                10),
            16);

        circleRadius = Offset(maxWidth - numberJobPainter.width - 12,
            numberJobPainter.height / 2 + 16);
        size = numberJobPainter.width / 2 + 14;
      }

      canvas.drawCircle(
        circleRadius,
        size,
        Paint()..color = Colors.red,
      );

      numberJobPainter.paint(canvas, numberOffset);
    }

    if (number >= 100) {
      // final numberOffset = Offset((maxWidth - numberJobPainter.width - 10), 20);

      Offset numberOffset =
          Offset((maxWidth - numberJobPainter.width - 10), 20);

      Offset circleRadius = Offset(maxWidth - numberJobPainter.width / 2 - 10,
          numberJobPainter.height / 2 + 20);
      double size = numberJobPainter.width / 2 + 10;
      if (Platform.isIOS) {
        numberOffset = Offset((maxWidth - numberJobPainter.width - 20), 22);

        circleRadius = Offset(maxWidth - numberJobPainter.width / 2 - 20,
            numberJobPainter.height / 2 + 22);
        size = numberJobPainter.width / 2 + 12;
      }

      canvas.drawCircle(
        circleRadius,
        size,
        Paint()..color = Colors.red,
      );

      numberJobPainter.paint(canvas, numberOffset);
    }

    // Convert canvas to image
    final maxHeight = size.height + textPainter.height + 20 + 10;
    final ui.Image markerAsImage = await pictureRecorder
        .endRecording()
        .toImage(maxWidth.toInt(), maxHeight.toInt());
    BitmapDescriptor? markerIcon;
    // Convert image to bytes
    final ByteData? byteData =
        await markerAsImage.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final Uint8List uint8List = byteData.buffer.asUint8List();
      markerIcon = BitmapDescriptor.fromBytes(uint8List);
    }

    return markerIcon;
  }

  Future<ui.Image> getImageFromPath(String path) async {
    Completer<ImageInfo> completer = Completer();
    var img = AssetImage(path);
    img
        .resolve(const ImageConfiguration())
        .addListener(ImageStreamListener((ImageInfo info, bool _) {
      completer.complete(info);
    }));
    ImageInfo imageInfo = await completer.future;
    return imageInfo.image;
  }

  LatLngBounds _createBounds(List<LatLng> positions) {
    final southwestLat = positions.map((p) => p.latitude).reduce(
        (value, element) => value < element ? value : element); // smallest
    final southwestLon = positions
        .map((p) => p.longitude)
        .reduce((value, element) => value < element ? value : element);
    final northeastLat = positions.map((p) => p.latitude).reduce(
        (value, element) => value > element ? value : element); // biggest
    final northeastLon = positions
        .map((p) => p.longitude)
        .reduce((value, element) => value > element ? value : element);
    return LatLngBounds(
        southwest: LatLng(southwestLat, southwestLon),
        northeast: LatLng(northeastLat, northeastLon));
  }

  void _onMapCreated(GoogleMapController _controller) {
    _mapController = _controller;
    _mapController?.setMapStyle(
        '[{"featureType": "poi","stylers": [{"visibility": "off"}]}]');

    // if (_listPositions.isNotEmpty) {
    //   try{
    //     if (_listPositions.length == 1) {
    //       _mapController?.animateCamera(
    //         CameraUpdate.newLatLngZoom(_listPositions.first, 10),
    //       );
    //     } else {
    //       _mapController?.animateCamera(
    //         CameraUpdate.newLatLngBounds(_createBounds(_listPositions), 50),
    //       );
    //     }
    //
    //     if (mounted) {
    //       setState(() {});
    //     }
    //   }catch(_) {}
    //
    // }
  }

  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      myLocationEnabled: true,
      onMapCreated: _onMapCreated,
      markers: _markers.toSet(),
      polylines: _polylines.toSet(),
      initialCameraPosition: CameraPosition(
        target: _center,
        zoom: 10.0,
      ),
    );
  }
}
