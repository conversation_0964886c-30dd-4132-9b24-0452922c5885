import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';

void showJobsMapBS(List<Job> jobs) {
  showModalBottomSheet(
    context: App.overlayContext!,
    builder: (context) => JobsMapBS(jobs: jobs),
    backgroundColor: AppColors.grey4,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(Dimens.rad_XXXSL),
        topRight: Radius.circular(Dimens.rad_XXXSL),
      ),
    ),
  );
}

class JobsMapBS extends StatelessWidget {
  const JobsMapBS({Key? key, required this.jobs}) : super(key: key);

  final List<Job> jobs;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: ViewUtils.getPercentHeight(percent: 0.8),
      child: Column(
        children: [
          const SizedBox(height: 40),
          _buildList().expand(),
        ],
      ),
    );
  }

  Widget _buildList() {
    return BlocBuilder<JobListBloc, JobListState>(
      builder: (context, state) {
        return Scrollbar(
          child: ListView.separated(
            padding: const EdgeInsets.only(bottom: 30),
            itemCount: jobs.length,
            separatorBuilder: (BuildContext context, int index) {
              return const SizedBox(height: 12);
            },
            itemBuilder: (BuildContext context, int index) {
              final job = jobs[index];
              return JobSelectionMap(
                job: job,
                isSelected: state.data.selectedJobList
                    .any((element) => element.id == job.id),
                currentLocation: state.data.curLocation,
                smsTemplate: state.data.smsTemplate,
                onUnableToAccess: () {
                  App.pop();
                },
              );
            },
          ),
        );
      },
    );
  }
}

class JobSelectionMap extends StatelessWidget {
  const JobSelectionMap({
    Key? key,
    required this.job,
    required this.currentLocation,
    required this.smsTemplate,
    required this.isSelected,
    required this.onUnableToAccess,
  }) : super(key: key);
  final Job job;
  final Gps? currentLocation;
  final SmsTemplate? smsTemplate;
  final bool isSelected;
  final Function onUnableToAccess;

  void _jobItemClickEvent(
    BuildContext context, {
    required Job job,
    required String? distance,
    required String group,
    SmsTemplate? smsTemplate,
  }) {
    showJobDetailBS(
      job: job,
      distance: distance,
      group: group,
      smsTemplate: smsTemplate,
      onUnableToAccess: onUnableToAccess,
      // onRepairPosted: ( Job job, String description){
      //   BlocProvider.of<JobListBloc>(context).add(
      //     JobRepairPosted(
      //       job: job,
      //       description: description,
      //     ),
      //   );
      // },
      onCommentDeleted: (JobComment comment, Job job) {
        BlocProvider.of<JobListBloc>(App.overlayContext!).add(
          JobCommentDeleted(
            job: job,
            comment: comment,
          ),
        );
      },
      onCommentEdit: (JobComment comment, Job job, String body) {
        BlocProvider.of<JobListBloc>(App.overlayContext!).add(
          JobCommentUpdated(
            job: job,
            comment: comment,
            body: body,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    String distance = '0 mi';
    if (job.contact.gps != null && currentLocation != null) {
      distance =
          '${NumberUtils.formatToPattern(GeoLocatorUtils.distanceBetweenGps(currentLocation!, job.contact.gps!))}mi';
    }

    final String groupName = job.scheduleDate?.dateGroupName ?? '';

    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Gaps.hGap16,
        InkWell(
          onTap: () => _jobItemClickEvent(
            context,
            job: job,
            distance: distance,
            group: groupName,
            smsTemplate: smsTemplate,
          ),
          child: Container(
            color: Theme.of(context).canvasColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: Theme.of(context).primaryColor,
                  height: 20,
                  width: 3,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _wQuickInfo(context).expand(),
                        CircleButton(
                          child: Assets.icon.pin.svg(),
                          backgroundColor: isSelected
                              ? Theme.of(context).primaryColor
                              : AppColors.grey2,
                          onPressed: () {
                            if (isSelected) {
                              BlocProvider.of<JobListBloc>(context)
                                  .add(JobUnSelected(job));
                            } else {
                              BlocProvider.of<JobListBloc>(context)
                                  .add(JobSelected(job));
                            }
                          },
                          size: Dimens.ic_XL,
                        ),
                      ],
                    ).px8().pt8().pb(value: 6),
                    _wGroupButton(context).pl(value: 16)
                  ],
                ).expand()
              ],
            ),
          ).cornerRadius(Dimens.rad_XXL),
        ).expand()
      ],
    ).px16();
  }

  Widget _wGroupButton(BuildContext context) {
    final String groupName = job.scheduleDate?.dateGroupName ?? '';
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      alignment: WrapAlignment.start,
      runAlignment: WrapAlignment.start,
      spacing: Dimens.gap_dp8,
      runSpacing: Dimens.gap_dp8,
      children: [
        Container(
          decoration: BoxDecoration(
              color: AppColors.neutral1Color,
              borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
          child: job.details.serviceType.text
              .color(Theme.of(context).primaryColor)
              .make()
              .py8()
              .px16(),
        ),
        if (job.contact.filterType != null && job.contact.filterType != "")
          Container(
            decoration: BoxDecoration(
                color: AppColors.neutral1Color,
                borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
            child: (job.contact.filterType ?? "")
                .text
                .color(Theme.of(context).primaryColor)
                .make()
                .py8()
                .px16(),
          ),
        groupName == 'Overdue'
            ? Container(
                decoration: BoxDecoration(
                    color: AppColors.redBackgroundColor,
                    borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                child: 'Due ${TimeUtils.dateToStr(job.scheduleDate)}'
                    .text
                    .color(AppColors.redAccentColor)
                    .make()
                    .py8()
                    .px16(),
              )
            : Container(
                decoration: BoxDecoration(
                    color: AppColors.neutral1Color,
                    borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                child: 'Due ${TimeUtils.dateToStr(job.scheduleDate)}'
                    .text
                    .color(AppColors.primaryColor2)
                    .make()
                    .py8()
                    .px16(),
              ),
      ],
    ).pb(value: Dimens.rad);
  }

  Widget _wQuickInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          job.contact.name ?? '',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                fontWeight: FontWeight.w600,
                overflow: TextOverflow.ellipsis,
                fontSize: Dimens.text,
              ),
          maxLines: 1,
        ).pt(value: 4),
        Gaps.vGap6,
        Text(
          job.contact.getFullLocation(),
          style: Theme.of(context).textTheme.bodyText2?.copyWith(
              color: AppColors.grey,
              overflow: TextOverflow.ellipsis,
              fontSize: Dimens.text,
              fontWeight: FontWeight.w400,
              height: 1.5),
          maxLines: 2,
        ),
      ],
    ).pl(value: 8);
  }
}
