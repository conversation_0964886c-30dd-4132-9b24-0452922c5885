import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_until.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';

import 'contact_customer_widget.dart';

class JobWidget extends StatefulWidget {
  const JobWidget({
    Key? key,
    required this.job,
    required this.isSelected,
    required this.group,
    this.onSelected,
    this.curLocation,
    this.onUnSelected,
    this.smsTemplate,
  }) : super(key: key);

  final Job job;
  final String group;
  final bool isSelected;
  final Gps? curLocation;
  final Function(Job job)? onSelected;
  final Function(Job job)? onUnSelected;
  final SmsTemplate? smsTemplate;

  @override
  State<JobWidget> createState() => _JobWidgetState();
}

class _JobWidgetState extends State<JobWidget> {
  void _jobItemClickEvent({
    required Job job,
    required String? distance,
    required String group,
    SmsTemplate? smsTemplate,
  }) {
    showJobDetailBS(
      job: job,
      distance: distance,
      group: group,
      smsTemplate: smsTemplate,
      onUnableToAccess: () {},
      // onRepairPosted: (Job job, String comment) {
      //   BlocProvider.of<JobListBloc>(context).add(
      //     JobRepairPosted(
      //       job: widget.job,
      //       description: comment,
      //     ),
      //   );
      // },
      onCommentDeleted: (JobComment comment, Job job) {
        BlocProvider.of<JobListBloc>(App.overlayContext!).add(
          JobCommentDeleted(
            job: job,
            comment: comment,
          ),
        );
      },
      onCommentEdit: (JobComment comment, Job job, String body) {
        BlocProvider.of<JobListBloc>(App.overlayContext!).add(
          JobCommentUpdated(
            job: job,
            comment: comment,
            body: body,
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    String distance = '0 mi';
    if (widget.curLocation != null && widget.job.contact.gps != null) {
      distance =
          '${NumberUtils.formatToPattern(GeoLocatorUtils.distanceBetweenGps(widget.curLocation!, widget.job.contact.gps!))}mi';
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 70,
          child: Text(
            distance,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyText2?.copyWith(
                  color: AppColors.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
          ),
        ),
        Gaps.hGap10,
        InkWell(
          onTap: () => _jobItemClickEvent(
              job: widget.job,
              distance: distance,
              group: widget.group,
              smsTemplate: widget.smsTemplate),
          child: Container(
            color: Theme.of(context).canvasColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: Theme.of(context).primaryColor,
                  height: 20,
                  width: 3,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _wQuickInfo(context).expand(),
                        CircleButton(
                          child: Assets.icon.pin.svg(),
                          backgroundColor: widget.isSelected
                              ? Theme.of(context).primaryColor
                              : AppColors.grey2,
                          onPressed: () {
                            if (widget.isSelected) {
                              widget.onUnSelected?.call(widget.job);
                            } else {
                              widget.onSelected?.call(widget.job);
                            }
                          },
                          size: Dimens.ic_XL,
                        ),
                      ],
                    ).px8().pt8().pb(value: 6),
                    _wGroupButton(context).pl(value: 16).pr(value: 8)
                  ],
                ).expand()
              ],
            ),
          ).cornerRadius(Dimens.rad_XXL),
        ).expand()
      ],
    ).px16();
  }

  Widget _wGroupButton(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
          flex: 6,
          child: Wrap(
            crossAxisAlignment: WrapCrossAlignment.start,
            alignment: WrapAlignment.start,
            runAlignment: WrapAlignment.start,
            spacing: Dimens.gap_dp8,
            runSpacing: Dimens.gap_dp8,
            children: [
              Container(
                decoration: BoxDecoration(
                    color: AppColors.neutral1Color,
                    borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                child: widget.job.details.serviceType.text
                    .color(Theme.of(context).primaryColor)
                    .make()
                    .py8()
                    .px16(),
              ),
              if (widget.job.contact.isFiberglassPool == true)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.neutral1Color,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: "Fiber Glass"
                      .text
                      .color(Theme.of(context).primaryColor)
                      .make()
                      .py8()
                      .px16(),
                ),
              if (widget.job.details.serviceLevel != null)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.neutral1Color,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: widget.job.details.serviceLevel?.text
                      .color(Theme.of(context).primaryColor)
                      .make()
                      .py8()
                      .px16(),
                ),

              if (widget.job.contact.filterType != null && widget.job.contact.filterType != "")
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.neutral1Color,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: (widget.job.contact.filterType ?? "").text
                      .color(Theme.of(context).primaryColor)
                      .make()
                      .py8()
                      .px16(),
                ),

              if (widget.job.details.isLightningService == true)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.neutral1Color,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: 'Lightning Service'.text
                      .color(Theme.of(context).primaryColor)
                      .make()
                      .py8()
                      .px16(),
                ),




              widget.group == 'Overdue'
                  ? Container(
                      decoration: BoxDecoration(
                          color: AppColors.redBackgroundColor,
                          borderRadius:
                              BorderRadius.circular(Dimens.rad_XXXXL)),
                      child:
                          'Due ${TimeUtils.dateToStr(widget.job.scheduleDate)}'
                              .text
                              .color(AppColors.redAccentColor)
                              .make()
                              .py8()
                              .px16(),
                    )
                  : Container(
                      decoration: BoxDecoration(
                          color: AppColors.neutral1Color,
                          borderRadius:
                              BorderRadius.circular(Dimens.rad_XXXXL)),
                      child:
                          'Due ${TimeUtils.dateToStr(widget.job.scheduleDate)}'
                              .text
                              .color(AppColors.primaryColor2)
                              .make()
                              .py8()
                              .px16(),
                    ),

              if (widget.job.details.estimatedTime != null)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.grey13,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: 'Estimate: ${printDuration(
                    Duration(
                      seconds: widget.job.details.estimatedTime!,
                    ),
                  )}'
                      .text
                      .color(AppColors.grey12)
                      .make()
                      .py8()
                      .px16(),
                ),

              if (widget.job.contact.isSaltWater == true)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.neutral1Color,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: "Salt Water"
                      .text
                      .color(Theme.of(context).primaryColor)
                      .make()
                      .py8()
                      .px16(),
                ),
              if (widget.job.details.specialRequests != null)
                Container(
                  decoration: BoxDecoration(
                      color: AppColors.specialBox,
                      borderRadius: BorderRadius.circular(Dimens.rad_XXXXL)),
                  child: 'Special Requests'
                      .text
                      .color(AppColors.specialColor)
                      .make()
                      .py8()
                      .px16(),
                ),
              // Btn(
              //   onPressed: null,
              //   style: AppButtonStyle.filterCleanStyle(context),
              //   text: widget.job.details.serviceType,
              // ),
              // Btn(
              //   onPressed: null,
              //   style: widget.group == 'Overdue'
              //       ? AppButtonStyle.overDueStyle(context)
              //       : AppButtonStyle.filterCleanStyle(context).copyWith(
              //           foregroundColor: MaterialStateProperty.resolveWith<Color>(
              //               (Set<MaterialState> states) => AppColors.primaryColor2)),
              //   text: 'Due ${TimeUtils.dateToStr(widget.job.scheduleDate)}',
              // )
            ],
          ).pb(value: Dimens.rad),
        ),
        CircleButton(
          child: Assets.icon.call.svg(),
          backgroundColor: const Color(0xff3f3fff),
          onPressed: () {
            showContactCustomerDialog(
              email: widget.job.contact.email,
              phoneNumber: widget.job.contact.phone,
              smsTemplate: null,
              job: widget.job,
            );
          },
          size: Dimens.ic_2XL,
        ).pb(value: 8)
      ],
    );
  }

  Widget _wQuickInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.job.contact.name ?? '',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                fontWeight: FontWeight.w600,
                overflow: TextOverflow.ellipsis,
                fontSize: Dimens.text,
              ),
          maxLines: 1,
        ).pt(value: 4),
        Gaps.vGap6,
        Text(
          widget.job.contact.getFullLocation(),
          style: Theme.of(context).textTheme.bodyText2?.copyWith(
              color: AppColors.grey,
              overflow: TextOverflow.ellipsis,
              fontSize: Dimens.text,
              fontWeight: FontWeight.w400,
              height: 1.5),
          maxLines: 2,
        ),
        if (widget.job.details.specialRequests != null)
          Text(
            widget.job.details.specialRequests ?? "",
            style: Theme.of(context).textTheme.bodyText2?.copyWith(
                color: AppColors.specialColor,
                overflow: TextOverflow.ellipsis,
                fontSize: Dimens.text,
                fontWeight: FontWeight.w400,
                height: 1.5),
            maxLines: 10,
          ).pt(value: 5),
      ],
    ).pl(value: 8);
  }
}
