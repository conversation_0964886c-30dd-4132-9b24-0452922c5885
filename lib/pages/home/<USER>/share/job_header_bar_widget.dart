import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/header_bar/header_bar_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/share/user_avatar_widget.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';
import 'package:ticking_app/widgets/calendar/app_calendar.dart';

class JobHeaderBarWidget extends StatelessWidget {
  const JobHeaderBarWidget(
      {Key? key,
      required this.title,
      this.showFilter = true,
      this.leadingWidget})
      : super(key: key);

  final String title;
  final bool showFilter;
  final Widget? leadingWidget;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        AppBar(
          title: Text(
            title,
            style: Theme.of(context)
                .textTheme
                .headline6
                ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          backgroundColor: AppColors.grey4,
          actions: [
            const UserAvatarWidget().pr(value: 10),
          ],
          leading: CircleButton(
            child: Assets.icon.refreshEnable.svg(
              color: Theme.of(context).primaryColor,
            ),
            onPressed: () {
              BlocProvider.of<JobListBloc>(context).add(JobListRefreshed());
            },
            size: Dimens.ic_XL,
          ),
        ),
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (showFilter)
              ..._buildFilter(context)
            else
              leadingWidget ?? const SizedBox(),
            // const Spacer(),
            _wGroupButton(context)
          ],
        ).px16(),
      ],
    );
  }

  List<Widget> _buildFilter(BuildContext context) {
    return [
      Text(
        'Until',
        style: Theme.of(context)
            .textTheme
            .bodyText1
            ?.copyWith(color: AppColors.grey2, fontWeight: FontWeight.w600),
      ).centered(),
      Gaps.hGap8,
      _wBtnCalendar(context),
      const Spacer(),
    ];
  }

  Widget _wBtnCalendar(BuildContext context) {
    return BlocBuilder<JobListBloc, JobListState>(
      builder: (context, state) {
        return GestureDetector(
          onTap: () async {
            BlocProvider.of<JobListBloc>(App.overlayContext!)
                .add(JobListBSUpdated(true));

            await showCalendar(
              onSelectedDate: (DateTime dateTime) {
                BlocProvider.of<JobListBloc>(context)
                    .add(JobListFiltered(dateTime));
              },
              selectedDate: state.data.filterDate,
            );

            BlocProvider.of<JobListBloc>(App.overlayContext!)
                .add(JobListBSUpdated(false));
          },
          child: Container(
            color: AppColors.materialWhite,
            child: Row(
              children: [
                Text(
                  TimeUtils.dateToStr(state.data.filterDate),
                  style: Theme.of(context).textTheme.bodyText1,
                ),
                Gaps.hGap8,
                const Icon(
                  Icons.date_range,
                  color: AppColors.grey2,
                )
              ],
            ).p(10),
          ).cornerRadius(Dimens.rad),
        );
      },
    );
  }

  Widget _wGroupButton(BuildContext context) {
    return Container(
      color: AppColors.grey5,
      child: BlocBuilder<HeaderBarBloc, HeaderBarState>(
        builder: (context, state) {
          return Row(children: [
            GestureDetector(
                onTap: () {
                  BlocProvider.of<HeaderBarBloc>(context)
                      .add(HeaderBarTypeChanged(false));
                },
                child: Container(
                  padding: const EdgeInsets.only(
                    left: Dimens.gap_dp4,
                    top: Dimens.gap_dp4,
                    bottom: Dimens.gap_dp4,
                  ),
                  child: Container(
                          color:
                              state.isListType ? null : AppColors.materialWhite,
                          child: state.isListType
                              ? Icon(
                                  Icons.location_on,
                                  color: state.isListType
                                      ? AppColors.grey
                                      : AppColors.primaryColor,
                                  size: 20,
                                ).p8()
                              : Assets.image.googleMap
                                  .image(
                                    width: 20,
                                    height: 20,
                                    fit: BoxFit.scaleDown,
                                  )
                                  .p8())
                      .cornerRadius(Dimens.rad_S),
                ).cornerRadius(Dimens.rad_S)),
            GestureDetector(
              onTap: () {
                BlocProvider.of<HeaderBarBloc>(context)
                    .add(HeaderBarTypeChanged(true));
              },
              child: Container(
                padding: const EdgeInsets.only(
                  top: Dimens.gap_dp4,
                  bottom: Dimens.gap_dp4,
                  right: Dimens.gap_dp4,
                ),
                child: Container(
                  color: state.isListType ? AppColors.materialWhite : null,
                  child: Icon(
                    Icons.list_alt,
                    color: state.isListType
                        ? AppColors.primaryColor
                        : AppColors.grey,
                    size: 20,
                  ).p8(),
                ).cornerRadius(Dimens.rad_S),
              ).cornerRadius(Dimens.rad_S),
            )
          ]);
        },
      ),
    ).cornerRadius(Dimens.rad_S);
  }
}
