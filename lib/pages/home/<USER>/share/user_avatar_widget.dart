import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/pages/auth/ui/change_password_page.dart';
import 'package:ticking_app/pages/barcode/barcode_page.dart';
import 'package:ticking_app/pages/job_count/history_tab/job_count_page.dart';
import 'package:ticking_app/pages/my_destination/my_destination_page.dart';
import 'package:ticking_app/pages/repair_request/list/repair_request_page.dart';
import 'package:ticking_app/pages/submit_bill/submit_bill_page.dart';

class UserAvatarWidget extends StatelessWidget {
  const UserAvatarWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PopupMenuButton<String>(
      icon: Assets.icon.noAvatar.svg(width: 25, height: 25),
      onSelected: (String result) {
        if (result == 'Log time') {
          App.pushNamed(AppRoutes.logTime, null);
        } else if (result == 'Change password') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return const ChangePasswordPage();
              },
            ),
          );
        } else if (result == 'Logout') {
          BlocProvider.of<AuthBloc>(context).add(AuthLogout());
        } else if (result == 'myDestination') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return const MyDestinationPage();
              },
            ),
          );
        } else if (result == 'submitBill') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return const SubmitBillPage();
              },
            ),
          );
        } else if (result == 'barcode') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return const BarcodePage();
              },
            ),
          );
        } else if (result == 'repairRequest') {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return const RepairRequestPage();
              },
            ),
          );
        } else if (result == "jobCount") {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) {
                return JobCountPage();
              },
            ),
          );
        }

        // else if (result == 'Session checkout') {
        //   Navigator.push(
        //     context,
        //     MaterialPageRoute(
        //       builder: (context) {
        //         return const SessionCheckoutPage();
        //       },
        //     ),
        //   );
        // }

        // BlocProvider.of<JobListBloc>(context).add(JobListUnInit());
      },
      padding: EdgeInsets.zero,
      iconSize: 30,
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          height: 30,
          value: 'Log time',
          child: Row(
            children: [
              Assets.icon.logTime.svg(width: 25, height: 25),
              Gaps.hGap6,
              Text(
                'Log time',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        PopupMenuItem<String>(
          height: 30,
          value: 'Logout',
          child: Row(
            children: [
              Assets.icon.logout.svg(width: 25, height: 25),
              Gaps.hGap6,
              Text(
                'Log out',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        PopupMenuItem<String>(
          height: 30,
          value: 'Change password',
          child: Row(
            children: [
              Assets.icon.changePassword.svg(width: 20, height: 20),
              Gaps.hGap6,
              Text(
                'Change password',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        // PopupMenuItem<String>(
        //   height: 30,
        //   value: 'Session checkout',
        //   child: Row(
        //     children: [
        //       Assets.icon.sharpMoreTime.svg(width: 25, height: 25),
        //       Gaps.hGap6,
        //       Text(
        //         'Session checkout',
        //         style: Theme.of(context).textTheme.bodyText1?.copyWith(
        //               fontWeight: FontWeight.w600,
        //             ),
        //       )
        //     ],
        //   ),
        // ),
        PopupMenuItem<String>(
          height: 30,
          value: 'myDestination',
          child: Row(
            children: [
              Assets.icon.location.svg(width: 25, height: 25),
              Gaps.hGap6,
              Text(
                'My Destination',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        PopupMenuItem<String>(
          height: 30,
          value: 'submitBill',
          child: Row(
            children: [
              Assets.icon.submitBill.svg(width: 25, height: 25),
              Gaps.hGap6,
              Text(
                'Submit bill',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),

        PopupMenuItem<String>(
          height: 30,
          value: 'barcode',
          child: Row(
            children: [
              Assets.icon.scan.svg(width: 25, height: 25),
              Gaps.hGap6,
              Text(
                'Barcode',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        PopupMenuItem<String>(
          height: 30,
          value: 'repairRequest',
          child: Row(
            children: [
              Assets.icon.checklist.svg(
                width: 25,
                height: 25,
                color: AppColors.black1,
              ),
              Gaps.hGap6,
              Text(
                'Submitted Repair Request',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
        PopupMenuItem<String>(
          height: 30,
          value: 'jobCount',
          child: Row(
            children: [
              Assets.icon.briefcase.svg(
                width: 25,
                height: 25,
                color: AppColors.black1,
              ),
              Gaps.hGap6,
              Text(
                'Job Count',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              )
            ],
          ),
        ),
      ],
    );
  }
}
