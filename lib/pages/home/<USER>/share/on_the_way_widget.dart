import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:simple_connection_checker/simple_connection_checker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

void showOnTheWayDialog({
  BuildContext? context,
  required Job job,
}) {
  showDialog(
      context: context ?? App.overlayContext!,
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            100.toVSizeBox(),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: GestureDetector(
                  onTap: () {
                    App.pop();
                  },
                  child: const Icon(
                    Icons.close,
                    size: 40,
                  )),
            ),
            AlertDialog(
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(24.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              insetPadding: const EdgeInsets.only(left: 10, right: 10),
              content: ContactCustomerWidget(
                job: job,
              ),
            ),
          ],
        );
      });
}

class ContactCustomerWidget extends StatefulWidget {
  const ContactCustomerWidget({
    Key? key,
    required this.job,
  }) : super(key: key);
  final Job job;

  @override
  _ContactCustomerWidgetState createState() => _ContactCustomerWidgetState();
}

class _ContactCustomerWidgetState extends State<ContactCustomerWidget> {
  List<String> list = ["5", "10", "15", "30", "45", "60"];
  String value = "5";
  bool isConnected = true;
  bool isLoading = false;
  @override
  void initState() {
    super.initState();
    checkConnection();
  }

  checkConnection() async {
    isConnected = await SimpleConnectionChecker.isConnectedToInternet();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: BlocBuilder<JobListBloc, JobListState>(
        builder: (context, state) {
          return Stack(
            children: [
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: <Widget>[
                    Text(
                      'Let ${widget.job.contact.name ?? ""} know you’re on the way',
                      style: Theme.of(context).normalStyle.copyWith(
                          color: AppColors.black,
                          fontSize: Dimens.text_L,
                          fontWeight: FontWeight.bold),
                    ).pb(value: 5).plr(value: 30),
                    Text(
                      'Be there in (minutes)',
                      style: Theme.of(context).normalStyle.copyWith(
                          color: AppColors.darkTextHeading,
                          fontWeight: FontWeight.bold),
                    ).pb(value: 30).plr(value: 30),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        for (int index = 0; index < list.length; index++)
                          InkWell(
                            onTap: () {
                              setState(() {
                                value = list[index];
                              });
                            },
                            child: CircleAvatar(
                              backgroundColor: value != list[index]
                                  ? AppColors.draftColor.withOpacity(0.3)
                                  : AppColors.darkBgGray.withOpacity(0.8),
                              child: Center(
                                child: Text(
                                  list[index],
                                  style: Theme.of(context).normalStyle.copyWith(
                                      color: value != list[index]
                                          ? AppColors.black
                                          : AppColors.materialWhite,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                            ),
                          )
                      ],
                    ).plr(value: 30).pb(value: 35),
                    InkWell(
                      onTap: () {
                        if (isConnected == true) {
                          _handleTap();
                        }
                      },
                      child: SizedBox(
                        height: 50,
                        child: Center(
                          child: Text(
                            "SEND TEXT",
                            style: Theme.of(context).normalStyle.copyWith(
                                color: isConnected == true
                                    ? AppColors.systemBlueColor
                                    : AppColors.darkBgGray.withOpacity(0.4),
                                fontSize: 18,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      ),
                    ),
                  ]).pt(value: 30).pb(value: 10),
              if (isLoading == true)
                const Center(
                  child: LoadingIndicator(),
                )
            ],
          );
        },
      ),
    );
  }

  void _handleTap() async {
    JobApi _jobApi = GetIt.I<JobApi>();

    try {
      setState(() {
        isLoading = true;
      });
      await _jobApi.postOnTheWay(
        id: widget.job.id,
        value: value,
      );
      setState(() {
        isLoading = false;
      });
      DialogHelper.showInfo(
        title: '',
        icon: const SizedBox(),
        content: 'SMS sent successfully',
      );
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      DialogHelper.showError(content: 'Sent SMS failed!');
    }
  }
}
