import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_event.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/repair_requests/repair_requests_state.dart';
import 'package:ticking_app/widgets/app_button.dart';

class RequestSelectedImagePage extends StatelessWidget {
  const RequestSelectedImagePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 70,
        // leadingWidth: 150,
        leading: const BackButton(
          color: Colors.black,
        ),
        title: Text(
          'Pictures',
          style: Theme.of(context)
              .textTheme
              .headline6
              ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: BlocBuilder<RepairRequestsBloc, RepairRequestsState>(
        builder: (context, state) {
          List<Uint8List> imageBytesList = state.uInt8Lists ?? [];

          return _buildContent(context, imageBytesList);
        },
      ),
    );
  }

  Widget _buildContent(BuildContext context, List<Uint8List> imageBytesList) {
    if (imageBytesList.isEmpty) {
      return const _BuildNoImage();
    }
    return _BuildImageList(
      listWidget: _buildImageList(context, imageBytesList),
    );
  }

  List<Widget> _buildImageList(
      BuildContext context, List<Uint8List> imageBytesList) {
    List<Widget> listWidget = [];

    listWidget.add(
      Container(
        width: 101,
        height: 131,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: AppColors.grey4,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add,
              color: AppColors.grey,
              size: 30,
            ),
            12.toVSizeBox(),
            Text(
              'Add\n Picture',
              style: Theme.of(context).titleTextStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
    for (int i = 0; i < imageBytesList.length; i++) {
      listWidget.add(ClipRRect(
        child: Container(
          width: 101,
          height: 131,
          decoration: BoxDecoration(
            image: DecorationImage(
                fit: BoxFit.cover,
                image: MemoryImage(
                  imageBytesList[i],
                )),
            borderRadius: BorderRadius.circular(4),
            color: AppColors.grey4,
          ),
        ),
      ));
    }
    return listWidget;
  }
}

class _BuildNoImage extends StatelessWidget {
  const _BuildNoImage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        List<XFile> files = [];
        await DialogHelper.showAppDialog(
          context,
          title: 'Add Pictures',
          content: "Please select the method to add pictures!",
          textStyle: Theme.of(context).contentTextStyle,
          actions: [
            AppButton(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
              onPressed: () async {
                XFile? file =
                await ImagePicker().pickImage(source: ImageSource.camera);
                Navigator.pop(context);
                if (file != null) {
                  files.add(file);
                }
              },
              child: Center(
                child: Text(
                  "Camera",
                  style: Theme.of(context)
                      .textTheme
                      .bodyText1!
                      .copyWith(color: AppColors.background, fontSize: 16),
                ),
              ),
            ),
            AppButton(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
              onPressed: () async {
                files = await ImagePicker().pickMultiImage();
                Navigator.pop(context);
              },
              child: Center(
                child: Text(
                  "Gallery",
                  style: Theme.of(context)
                      .textTheme
                      .bodyText1!
                      .copyWith(color: AppColors.background, fontSize: 16),
                ),
              ),
            ),
          ],
        );

        if (files.isNotEmpty) {
          context
              .read<RepairRequestsBloc>()
              .add(RepairTicketImagesInserted(files: files));
        }
      },
      child: Container(
        width: double.maxFinite,
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 17),
        height: 64,
        decoration: BoxDecoration(
          color: AppColors.grey4,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Assets.icon.picture.svg(
                width: 36,
                height: 36,
                color: AppColors.grey,
              ),
            ),
            Expanded(
                flex: 5,
                child: Center(
                    child: Text(
                  '+ Click to add picture',
                  style: Theme.of(context).titleTextStyle,
                ))),
            const Expanded(flex: 2, child: SizedBox()),
          ],
        ),
      ),
    );
  }
}

class _BuildImageList extends StatelessWidget {
  const _BuildImageList({Key? key, required this.listWidget}) : super(key: key);
  final List<Widget> listWidget;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
            childAspectRatio: 111 / 132),
        padding: const EdgeInsets.all(8),
        // shrinkWrap: true,
        itemCount: listWidget.length,
        itemBuilder: (context, index) {
          return InkWell(
              onTap: () async {
                if (index == 0) {
                  List<XFile> files = [];
                  await DialogHelper.showAppDialog(
                    context,
                    title: 'Add Pictures',
                    content: "Please select the method to add pictures!",
                    textStyle: Theme.of(context).contentTextStyle,
                    actions: [
                      AppButton(
                        backgroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
                        onPressed: () async {
                          XFile? file =
                          await ImagePicker().pickImage(source: ImageSource.camera);
                          Navigator.pop(context);
                          if (file != null) {
                            files.add(file);
                          }
                        },
                        child: Center(
                          child: Text(
                            "Camera",
                            style: Theme.of(context)
                                .textTheme
                                .bodyText1!
                                .copyWith(color: AppColors.background, fontSize: 16),
                          ),
                        ),
                      ),
                      AppButton(
                        backgroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
                        onPressed: () async {
                          files = await ImagePicker().pickMultiImage();
                          Navigator.pop(context);
                        },
                        child: Center(
                          child: Text(
                            "Gallery",
                            style: Theme.of(context)
                                .textTheme
                                .bodyText1!
                                .copyWith(color: AppColors.background, fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  );
                  if (files.isNotEmpty) {
                    context
                        .read<RepairRequestsBloc>()
                        .add(RepairTicketImagesInserted(files: files));
                  }
                }
              },
              child: listWidget[index]);
        },
      ),
    );
  }
}
