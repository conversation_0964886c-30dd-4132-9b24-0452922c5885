import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sms/flutter_sms.dart';
import 'package:get_it/get_it.dart';
import 'package:jinja/jinja.dart';
import 'package:simple_connection_checker/simple_connection_checker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/launcher_utils.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

void showIssueDialog({
  BuildContext? context,
  String? email,
  String? phoneNumber,
  SmsTemplate? smsTemplate,
  Widget? childWidget,
  required Job job,
  required Function(String reason) onUnableToAccess,
}) {
  showDialog(
      context: context ?? App.overlayContext!,
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            100.toVSizeBox(),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: GestureDetector(
                  onTap: () {
                    App.pop();
                  },
                  child: const Icon(
                    Icons.close,
                    size: 40,
                  )),
            ),
            AlertDialog(
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(24.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              insetPadding: const EdgeInsets.only(left: 10, right: 10),
              content: childWidget ??
                  IssueWidget(
                    email: email,
                    phoneNumber: phoneNumber,
                    smsTemplate: smsTemplate,
                    job: job,
                    onUnableToAccess: onUnableToAccess,
                  ),
            ),
          ],
        );
      });
}

class IssueWidget extends StatefulWidget {
  const IssueWidget({
    Key? key,
    this.email,
    this.phoneNumber,
    this.smsTemplate,
    required this.job,
    required this.onUnableToAccess,
  }) : super(key: key);
  final String? email;

  final String? phoneNumber;
  final SmsTemplate? smsTemplate;
  final Job job;
  final Function(String reason) onUnableToAccess;

  @override
  _IssueWidgetState createState() => _IssueWidgetState();
}

class _IssueWidgetState extends State<IssueWidget> {
  int _val = -1;
  bool _isCallButtonPressed = false;

  void _onUnableToAccess(BuildContext context) {
    if (_val != -1) {
      final reason = issueReasons[_val].code ?? "";
      widget.onUnableToAccess(reason);
      App.pushNamed(AppRoutes.home);
    }
  }

  void _onSmsLauncher() {
    // final phoneNumber = widget.phoneNumber;
    //
    // if (phoneNumber != null) {
    //   final message =
    //       _onConvertTemplate(widget.job, widget.smsTemplate?.body ?? '');
    //
    //   _sendSMS(message, [phoneNumber]);
    // }

    final reason = _val == -1 ? '' : issueReasons[_val].code;
    BlocProvider.of<JobListBloc>(context).add(
      JobSMSIssuePosted(
        job: widget.job,
        reason: reason ?? "",
      ),
    );
  }

  String _onConvertTemplate(Job job, String message) {
    final fieldManager = job.contact.fieldManager;

    final env = Environment(
      globals: <String, Object?>{
        'now': () {
          var dt = DateTime.now().toLocal();
          var hour = dt.hour.toString().padLeft(2, '0');
          var minute = dt.minute.toString().padLeft(2, '0');
          return '$hour:$minute';
        },
      },
      loader: MapLoader({}),
      leftStripBlocks: true,
      trimBlocks: true,
    );

    final reason = _val == 0 ? 'pet in the yard' : 'lock gate';

    final formatMessage = env
        .fromString(
      // '{% if a and b %}, or you can directly call your {field_manager_name} at {field_manager_phone} {% endif %}. We look forward'
      //         .replaceAll(' field_manager_phone ', ' fieldManagerPhone ')
      //         .replaceAll(' field_manager_name ', ' fieldManagerName ')
      message
          .jinjaFormat(fieldManager?.phone, ' field_manager_phone ')
          .jinjaFormat(fieldManager?.name, ' field_manager_name ')
          .jinjaFormat(job.employee.name, ' employee_name ')
          .jinjaFormat(job.contact.getFullLocation(), ' customer_address ')
          .jinjaFormat(reason, ' reason ')
          .jinjaFormat(
              TimeUtils.dateTimeToStr(DateTime.now(),
                  format: TimeUtils.formatTime),
              ' date_time '),
    )
        .render({
      // 'employee_name': job.employee.name,
      // 'customer_address': job.contact.getFullLocation(),
      // 'reason': reason,
      // 'date_time':
      //     TimeUtils.dateTimeToStr(DateTime.now(), format: TimeUtils.formatTime),

      // 'fieldManagerPhone':
      //     fieldManager?.phone.isNullOrEmpty() ?? false ? true : true,
      // 'fieldManagerName':
      //     fieldManager?.name.isNullOrEmpty() ?? false ? true : true,
    });

    return formatMessage
        .replaceAll('{employee_name}', job.employee.name)
        .replaceAll(
            '{date_time}',
            TimeUtils.dateTimeToStr(DateTime.now(),
                format: TimeUtils.formatTime))
        .replaceAll('{customer_address}', job.contact.getFullLocation())
        .replaceAll('{service_type}', job.details.serviceType)
        .replaceAll('{reason}', reason)
        .replaceAll('{field_manager_name}', fieldManager?.name ?? '')
        .replaceAll('{field_manager_phone}', fieldManager?.phone ?? '')
        .trim();
  }

  void _sendSMS(String message, List<String> receivers) async {
    await sendSMS(message: message, recipients: receivers)
        .catchError((onError) {
      // DialogHelper.showError(content: state.error);
    });
  }

  void _onCallLauncher() {
    if (widget.phoneNumber != null) {
      final template =
          UrlUtils.getCallLauncher(phoneNumber: widget.phoneNumber!);
      launch(template);
    }
  }

  bool _emailEmpty = true;
  bool _phoneEmpty = true;
  bool isConnected = false;
  bool isLoading = true;
  List<IssueReasons> issueReasons = [];

  @override
  void initState() {
    _emailEmpty = widget.email == null || widget.email == '';
    _phoneEmpty = widget.phoneNumber == null || widget.phoneNumber == '';
    _getConfiguration();
    super.initState();
    checkConnection();
  }

  _getConfiguration() async {
    try {
      final _secureConfigService = GetIt.I<SecureConfigService>();
      final configurationResult = _secureConfigService.configuration;

      final listRequirements = configurationResult?.jobRequirements??[];
      issueReasons = listRequirements
              .firstWhere((element) =>
                  element.serviceType == widget.job.details.serviceType)
              .issueReasons ??
          [];
    } catch (e) {
      issueReasons = [];
    }
    setState(() {
      isLoading = false;
    });
  }

  checkConnection() async {
    isConnected = await SimpleConnectionChecker.isConnectedToInternet();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: BlocBuilder<JobListBloc, JobListState>(
        builder: (context, state) {
          return Stack(
            children: [
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: <Widget>[
                    Center(
                      child: Text(
                        'Issue',
                        style: Theme.of(context).normalStyle.copyWith(
                            color: AppColors.black,
                            fontWeight: FontWeight.bold),
                      ).pt(value: 14),
                    ),
                    if (isCheckIssue == false)
                      Center(
                        child: Text(
                          'Please select an issue',
                          style: Theme.of(context)
                              .normalStyle
                              .copyWith(color: AppColors.grey, fontSize: 14),
                        ),
                      ),
                    isLoading == true
                        ? const Center(
                            child: LoadingIndicator(),
                          )
                        : (issueReasons.isEmpty)
                            ? Center(
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 10,
                                  ),
                                  child: Text(
                                    'This service type ${widget.job.details.serviceType} has no issue reasons yet',
                                    style: Theme.of(context)
                                        .normalStyle
                                        .copyWith(
                                            color: AppColors.error,
                                            fontSize: 14),
                                  ),
                                ),
                              )
                            : SizedBox(
                                width: double.infinity,
                                child: Wrap(
                                  direction: Axis.horizontal,
                                  crossAxisAlignment: WrapCrossAlignment.center,
                                  children: [
                                    for (int i = 0;
                                        i < issueReasons.length;
                                        i++)
                                      _buildSelector(
                                          i, issueReasons[i].name ?? ""),
                                  ],
                                ).pl(value: 10).pb(value: 10),
                              ),
                    if (isCheckIssue &&
                        issueReasons[_val].isContactRequired == true)
                      _buildButton(),
                    if (errorValue.isNotEmpty &&
                        isCheckIssue &&
                        issueReasons[_val].isContactRequired == true)
                      _buildErrorMessage(),
                    if (!_isCallButtonPressed &&
                        isCheckIssue &&
                        (!_phoneEmpty || !_phoneEmpty) &&
                        issueReasons[_val].isContactRequired == true)
                      const Text(
                        'Please contact your customer before rescheduling the Job!',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: AppColors.error),
                      ).pLTRB(16, 10, 16, 0),
                    const Divider().pt(value: 10),
                    _buildAccessButton(),
                  ]),
              if (state is JobListBusy)
                const Center(
                  child: LoadingIndicator(),
                )
            ],
          );
        },
      ),
    );
  }

  bool get isCheckIssue => _val != -1;

  String get errorValue {
    String value = '';

    if (_emailEmpty && _phoneEmpty) {
      value = "Missing customer's email & phone number";
      return value;
    }

    if (_emailEmpty) {
      value = 'This client has no email.';
      return value;
    }

    if (_phoneEmpty) {
      value = 'This client has no phone number.';
      return value;
    }

    return value;
  }

  Color _colorCheck() {
    return (_val != -1 &&
                (_isCallButtonPressed || (_emailEmpty && _phoneEmpty)) ||
            (_val != -1 && issueReasons[_val].isContactRequired == false))
        ? AppColors.primaryColor
        : AppColors.grey5;
  }

  Widget _buildButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 2,
          child: SizedBox(
            height: 48,
            child: Btn.outline(
              style: (_phoneEmpty || isConnected == false)
                  ? AppButtonStyle.outlineStyle(
                      context,
                      borderColor: Colors.transparent,
                      backgroundColor: Colors.transparent,
                      props: BtnStyleProps(
                        padding: const EdgeInsets.all(6),
                      ),
                    )
                  : null,
              child: Text(
                'SMS',
                style: TextStyle(
                  color: (_phoneEmpty || isConnected == false)
                      ? Theme.of(context).hintColor
                      : Theme.of(context).primaryColor,
                ),
              ),
              onPressed: (_phoneEmpty || isConnected == false)
                  ? null
                  : () {
                      _onSmsLauncher();
                      setState(() {
                        _isCallButtonPressed = true;
                      });
                    },
            ).pr(value: 10),
          ),
        ),
        Expanded(
          flex: 2,
          child: SizedBox(
            height: 48,
            child: Btn.outline(
              style: _emailEmpty
                  ? AppButtonStyle.outlineStyle(
                      context,
                      borderColor: Colors.transparent,
                      backgroundColor: Colors.transparent,
                      props: BtnStyleProps(
                        padding: const EdgeInsets.all(6),
                      ),
                    )
                  : null,
              child: Text(
                'Email',
                style: TextStyle(
                  color: _emailEmpty
                      ? Theme.of(context).hintColor
                      : Theme.of(context).primaryColor,
                ),
              ),
              // text: 'Email',
              onPressed: _emailEmpty
                  ? null
                  : () {
                      final reason = _val == 0 ? 'dog_out' : 'lock_out';
                      BlocProvider.of<JobListBloc>(App.overlayContext!).add(
                        JobIssuePosted(
                          job: widget.job,
                          reason: reason,
                        ),
                      );
                      setState(() {
                        _isCallButtonPressed = true;
                      });
                      // _onMailLauncher();
                    },
            ).pr(value: 10),
          ),
        ),
        Expanded(
          flex: 3,
          child: SizedBox(
            height: 48,
            child: Btn.main(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icon.call
                      .svg(
                        width: 20,
                        color: _phoneEmpty ? Theme.of(context).hintColor : null,
                      )
                      .pr(value: 10),
                  Text(
                    'Call',
                    style: TextStyle(
                        color:
                            _phoneEmpty ? Theme.of(context).hintColor : null),
                  ).pr(value: 20),
                ],
              ),
              style: AppButtonStyle.redAccentStyle(context,
                  props: BtnStyleProps(
                      radius: 8, padding: const EdgeInsets.only(left: 20))),
              onPressed: _phoneEmpty
                  ? null
                  : () {
                      _onCallLauncher();
                      setState(() {
                        _isCallButtonPressed = true;
                      });
                    },
            ),
          ),
        ),
      ],
    ).pl(value: 20).pt(value: 10).pr(value: 20);
  }

  Widget _buildErrorMessage() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const CircleAvatar(
          radius: 10.0,
          backgroundColor: AppColors.redAccentColor,
          child: Icon(
            Icons.close,
            size: 15,
            color: Colors.white,
          ),
        ),
        10.toHSizeBox(),
        Flexible(
          child: Text(
            errorValue,
            textAlign: TextAlign.center,
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: AppColors.redAccentColor, fontSize: 12),
          ),
        ),
      ],
    ).pl(value: 20).pt(value: 10).pr(value: 20);
  }

  Widget _buildAccessButton() {
    return InkWell(
      onTap: () {
        if (_val != -1 && issueReasons[_val].isContactRequired == false) {
          _onUnableToAccess(context);
          return;
        }

        (_val != -1 && (_isCallButtonPressed || (_emailEmpty && _phoneEmpty)))
            ? _onUnableToAccess(context)
            : null;
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            'Unable to Access',
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: _colorCheck(), fontWeight: FontWeight.bold),
          ),
          10.toHSizeBox(),
          Icon(
            Icons.arrow_forward_ios,
            color: _colorCheck(),
            size: 15,
          ),
        ],
      ).pt(value: 20).pb(value: 20),
    );
  }

  Widget _buildSelector(int value, String title) {
    return SizedBox(
      width: context.screenWidth * 0.4,
      child: Row(
        children: [
          Transform.scale(
            scale: 1.5,
            child: Radio(
              value: value,
              groupValue: _val,
              onChanged: (value) {
                setState(() {
                  _val = int.parse(value.toString());
                });
              },
              activeColor: AppColors.primaryColor,
            ),
          ),
          Text(
            title,
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: AppColors.grey, fontSize: 14),
          ),
        ],
      ).pt(value: 20),
    );
  }
}
