import 'package:flutter/material.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/edit_comment_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

class EditCommentPage extends StatefulWidget {
  const EditCommentPage({Key? key, required this.args}) : super(key: key);

  final EditCommentArg args;

  @override
  State<EditCommentPage> createState() => _EditCommentPageState();
}

class _EditCommentPageState extends State<EditCommentPage> {
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    _controller.text = widget.args.comment.body;
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    return SafeArea(
      child: Column(
        children: [
          40.toVSizeBox(),
          Expanded(
            child: Container(
                decoration: BoxDecoration(
                    color: Theme.of(context).backgroundColor,
                    borderRadius: const BorderRadius.only(
                      topRight: Radius.circular(Dimens.rad_XXL),
                      topLeft: Radius.circular(Dimens.rad_XXL),
                    )),
                child: Column(
                  children: [
                    16.toVSizeBox(),
                    _buildAvatar(widget.args.comment),
                    OutLineTextField.big(
                      controller: _controller,
                      radius: 8,
                      maxLines: 5,
                      autoFocus: false,
                      hintText: 'Your comment',
                      backgroundColor: AppColors.grey4,
                      contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 16),
                      showBorder: false,
                      hintStyle: Theme.of(context)
                          .textTheme
                          .bodyText2
                          ?.copyWith(color: AppColors.grey),
                      // onFieldSubmitted: (value) => _onCommentSubmit(context, value),
                    ),
                    Gaps.vGap12,
                    _buildButton(),
                  ],
                ).px16()),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(JobComment jobComment) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            ClipOval(
                child: Assets.icon.noAvatar
                    .svg(width: 32, height: 32)
                    .backgroundColor(AppColors.grey)),
            Gaps.hGap8,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  (jobComment.employee.name)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .bold
                      .make(),
                  Gaps.vGap4,
                  TimeUtils.dateTimeToStr(jobComment.date)
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText2!)
                      .fontWeight(FontWeight.w600)
                      .color(AppColors.selectColor)
                      .make()
                ],
              ),
            ),
          ],
        ),
        Gaps.vGap4,
      ],
    );
  }

  Widget _buildButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      // mainAxisSize: MainAxisSize.min,
      children: [
        CommonOutlineButton(
          text: 'CANCEL',
          onPressed: () {
            App.pop();
          },
        ),
        Gaps.hGap12,
        CommonPrimaryButton(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 35),
          child: 'UPDATE'
              .text
              .textStyle(Theme.of(context).textTheme.bodyText1!)
              .size(Dimens.text_XL)
              .color(AppColors.materialWhite)
              .fontWeight(FontWeight.w600)
              .make(),
          onPressed: () {
            widget.args.onCommentEdit?.call(
              widget.args.comment,
              widget.args.job,
              _controller.text,
            );
            App.pop();

          },
        )
      ],
    );
  }
}
