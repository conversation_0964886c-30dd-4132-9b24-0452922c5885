import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_sms/flutter_sms.dart';
import 'package:jinja/jinja.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/launcher_utils.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

void showContactCustomerDialog({
  BuildContext? context,
  String? email,
  String? phoneNumber,
  SmsTemplate? smsTemplate,
  required Job job,
}) {
  showDialog(
      context: context ?? App.overlayContext!,
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            100.toVSizeBox(),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: GestureDetector(
                  onTap: () {
                    App.pop();
                  },
                  child: const Icon(
                    Icons.close,
                    size: 40,
                  )),
            ),
            AlertDialog(
              shape: const RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(24.0))),
              contentPadding: const EdgeInsets.only(top: 10.0),
              insetPadding: const EdgeInsets.only(left: 10, right: 10),
              content: ContactCustomerWidget(
                email: email,
                phoneNumber: phoneNumber,
                smsTemplate: smsTemplate,
                job: job,
              ),
            ),
          ],
        );
      });
}

class ContactCustomerWidget extends StatefulWidget {
  const ContactCustomerWidget({
    Key? key,
    this.email,
    this.phoneNumber,
    this.smsTemplate,
    required this.job,
  }) : super(key: key);
  final String? email;

  final String? phoneNumber;
  final SmsTemplate? smsTemplate;
  final Job job;

  @override
  _ContactCustomerWidgetState createState() => _ContactCustomerWidgetState();
}

class _ContactCustomerWidgetState extends State<ContactCustomerWidget> {
  void _onSmsLauncher() {
    final phoneNumber = widget.phoneNumber;

    if (phoneNumber != null) {
      final message =
          _onConvertTemplate(widget.job, widget.smsTemplate?.body ?? '');

      _sendSMS(message, [phoneNumber]);
    }

  }

  String _onConvertTemplate(Job job, String message) {
    final fieldManager = job.contact.fieldManager;

    final env = Environment(
      globals: <String, Object?>{
        'now': () {
          var dt = DateTime.now().toLocal();
          var hour = dt.hour.toString().padLeft(2, '0');
          var minute = dt.minute.toString().padLeft(2, '0');
          return '$hour:$minute';
        },
      },
      loader: MapLoader({}),
      leftStripBlocks: true,
      trimBlocks: true,
    );

    final reason = ''; // TODO: Update reason;
    final formatMessage = env
        .fromString(
      message
          .jinjaFormat(fieldManager?.phone, ' field_manager_phone ')
          .jinjaFormat(fieldManager?.name, ' field_manager_name ')
          .jinjaFormat(job.employee.name, ' employee_name ')
          .jinjaFormat(job.contact.getFullLocation(), ' customer_address ')
          .jinjaFormat(reason, ' reason ')
          .jinjaFormat(
              TimeUtils.dateTimeToStr(DateTime.now(),
                  format: TimeUtils.formatTime),
              ' date_time '),
    )
        .render({});

    return formatMessage
        .replaceAll('{employee_name}', job.employee.name)
        .replaceAll(
            '{date_time}',
            TimeUtils.dateTimeToStr(DateTime.now(),
                format: TimeUtils.formatTime))
        .replaceAll('{customer_address}', job.contact.getFullLocation())
        .replaceAll('{service_type}', job.details.serviceType)
        .replaceAll('{reason}', reason)
        .replaceAll('{field_manager_name}', fieldManager?.name ?? '')
        .replaceAll('{field_manager_phone}', fieldManager?.phone ?? '')
        .trim();
  }

  void _sendSMS(String message, List<String> receivers) async {
    await sendSMS(message: message, recipients: receivers)
        .catchError((onError) {
      // DialogHelper.showError(content: state.error);
    });
  }

  void _onCallLauncher() {
    if (widget.phoneNumber != null) {
      final template =
          UrlUtils.getCallLauncher(phoneNumber: widget.phoneNumber!);
      launch(template);
    }
  }

  bool _emailEmpty = true;
  bool _phoneEmpty = true;

  @override
  void initState() {
    _emailEmpty = widget.email == null || widget.email == '';
    _phoneEmpty = widget.phoneNumber == null || widget.phoneNumber == '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.maxFinite,
      child: BlocBuilder<JobListBloc, JobListState>(
        builder: (context, state) {
          return Stack(
            children: [
              Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: <Widget>[
                    Center(
                      child: Text(
                        'Contact Customer',
                        style: Theme.of(context).normalStyle.copyWith(
                            color: AppColors.black,
                            fontWeight: FontWeight.bold),
                      ).pb(value: 30),
                    ),
                    _buildButton(),
                  ]).pt(value: 30).pb(value: 40),
              if (state is JobListBusy)
                const Center(
                  child: LoadingIndicator(),
                )
            ],
          );
        },
      ),
    );
  }

  String get errorValue {
    String value = '';

    if (_emailEmpty && _phoneEmpty) {
      value = "Missing customer's email & phone number";
      return value;
    }

    if (_emailEmpty) {
      value = 'This client has no email.';
      return value;
    }

    if (_phoneEmpty) {
      value = 'This client has no phone number.';
      return value;
    }

    return value;
  }

  Widget _buildButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          flex: 2,
          child: SizedBox(
            height: 48,
            child: Btn.outline(
              style: _phoneEmpty
                  ? AppButtonStyle.outlineStyle(
                      context,
                      borderColor: Colors.transparent,
                      backgroundColor: Colors.transparent,
                      props: BtnStyleProps(
                        padding: const EdgeInsets.all(6),
                      ),
                    )
                  : null,
              child: Text(
                'SMS',
                style: TextStyle(
                  color: _phoneEmpty
                      ? Theme.of(context).hintColor
                      : Theme.of(context).primaryColor,
                ),
              ),
              onPressed: _phoneEmpty
                  ? null
                  : () {
                      _onSmsLauncher();
                      setState(() {});
                    },
            ).pr(value: 10),
          ),
        ),
        Expanded(
          flex: 2,
          child: SizedBox(
            height: 48,
            child: Btn.outline(
              style: _emailEmpty
                  ? AppButtonStyle.outlineStyle(
                      context,
                      borderColor: Colors.transparent,
                      backgroundColor: Colors.transparent,
                      props: BtnStyleProps(
                        padding: const EdgeInsets.all(6),
                      ),
                    )
                  : null,
              child: Text(
                'Email',
                style: TextStyle(
                  color: _emailEmpty
                      ? Theme.of(context).hintColor
                      : Theme.of(context).primaryColor,
                ),
              ),
              // text: 'Email',
              onPressed: null
            ).pr(value: 10),
          ),
        ),
        Expanded(
          flex: 3,
          child: SizedBox(
            height: 48,
            child: Btn.main(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icon.call
                      .svg(
                        width: 20,
                        color: _phoneEmpty ? Theme.of(context).hintColor : null,
                      )
                      .pr(value: 10),
                  Text(
                    'Call',
                    style: TextStyle(
                        color:
                            _phoneEmpty ? Theme.of(context).hintColor : null),
                  ).pr(value: 20),
                ],
              ),
              style: AppButtonStyle.redAccentStyle(context,
                  props: BtnStyleProps(
                      radius: 8, padding: const EdgeInsets.only(left: 20))),
              onPressed: _phoneEmpty
                  ? null
                  : () {
                      _onCallLauncher();
                      setState(() {});
                    },
            ),
          ),
        ),
      ],
    ).pl(value: 20).pt(value: 10).pr(value: 20);
  }
}
