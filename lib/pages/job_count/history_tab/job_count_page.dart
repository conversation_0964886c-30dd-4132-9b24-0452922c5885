import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart' as intl;
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/job_count/history_tab/table_scroll_controller.dart';
import 'package:ticking_app/widgets/drop_down/app_drop_down.dart';

import 'bloc/job_count_bloc.dart';
import 'bloc/job_count_event.dart';
import 'bloc/job_count_state.dart';

const double cellWidth = 94;
const double cellHeight = 51;

class JobCountPage extends StatefulWidget {
  const JobCountPage({
    Key? key,
  }) : super(key: key);

  @override
  State<JobCountPage> createState() => _JobCountPageState();
}

class _JobCountPageState extends State<JobCountPage> {
  final JobCountBloc _bloc = JobCountBloc();

  @override
  void initState() {
    _bloc.add(JobCountLoaded());
    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _buildBody();
  }

  Widget _buildBody() {
    return BlocProvider.value(
      value: _bloc,
      child: BlocListener<JobCountBloc, JobCountState>(
        listenWhen: (p, c) => p.status != c.status || p.error != c.error,
        listener: (context, state) async {
          if (state.status == JobCountStatus.actionFailure) {
            DialogHelper.showError(content: state.error ?? 'NA');
          }
        },
        child: Container(
          color: Colors.white,
          child: Column(
            children: [
              AppBar(
                title: Text(
                  "Job Count",
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: AppColors.black1,
                        fontWeight: FontWeight.bold,
                      ),
                ),
                centerTitle: true,
                backgroundColor: AppColors.background,
                actions: [
                  Assets.icon.noAvatar.svg(width: 25, height: 25).pr(value: 10),
                ],
                leading: const BackButton(
                  color: Color(0xFF000000),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: AppDropDown(
                  color: AppColors.grey11,
                  groupNameList: const ['This week', 'Last week'],
                  textColor: AppColors.black1,
                  headerText: 'In ',
                  iconColor: AppColors.grey12,
                  onSelectedItem: (index) {
                    _bloc.add(JobCountIndexChanged(index: index));
                  },
                  index: 0,
                ),
              ),
              const Expanded(child: _TableWidget()),
            ],
          ),
        ),
      ),
    );
  }
}

class _TableWidget extends StatefulWidget {
  const _TableWidget({Key? key}) : super(key: key);

  @override
  _TableWidgetState createState() => _TableWidgetState();
}

class _TableWidgetState extends State<_TableWidget> {
  late TableScrollController _controllers;
  late ScrollController _headController;
  late ScrollController _bodyController;

  @override
  void initState() {
    super.initState();
    _controllers = TableScrollController();
    _headController = _controllers.addAndGet();
    _bodyController = _controllers.addAndGet();
  }

  @override
  void dispose() {
    _headController.dispose();
    _bodyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _TableHead(
          scrollController: _headController,
        ),
        Expanded(
          child: _TableBody(
            scrollController: _bodyController,
          ),
        ),
      ],
    );
  }
}

class _TableBody extends StatefulWidget {
  final ScrollController scrollController;

  const _TableBody({
    Key? key,
    required this.scrollController,
  }) : super(key: key);

  @override
  _TableBodyState createState() => _TableBodyState();
}

class _TableBodyState extends State<_TableBody> {
  late TableScrollController _controllers;
  late ScrollController _firstColumnController;
  late ScrollController _restColumnsController;
  late ScrollController _nameColumnsController;

  @override
  void initState() {
    super.initState();
    _controllers = TableScrollController();
    _firstColumnController = _controllers.addAndGet();
    _restColumnsController = _controllers.addAndGet();
    _nameColumnsController = _controllers.addAndGet();
  }

  @override
  void dispose() {
    _firstColumnController.dispose();
    _restColumnsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<JobCountBloc, JobCountState>(
      buildWhen: (p, c) => p.jobCounts != c.jobCounts,
      builder: (context, state) {
        final jobCounts = state.jobCounts;
        if (jobCounts.isEmpty) {
          return const SizedBox();
        }
        final lines = jobCounts[0].lines ?? [];

        return RefreshIndicator(
          notificationPredicate: (ScrollNotification notification) {
            return notification.depth == 0 || notification.depth == 1;
          },
          onRefresh: () async {
            context.read<JobCountBloc>().add(JobCountLoaded());
          },
          child: Stack(
            children: [
              Row(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      controller: widget.scrollController,
                      scrollDirection: Axis.horizontal,
                      physics: const ClampingScrollPhysics(),
                      child: SizedBox(
                        width: (jobCounts.length) * cellWidth,
                        child: ListView(
                          padding: EdgeInsets.zero,
                          controller: _restColumnsController,
                          physics: const AlwaysScrollableScrollPhysics(
                            parent: BouncingScrollPhysics(),
                          ),
                          children: List.generate(lines.length, (y) {
                            return Row(
                              children: List.generate(jobCounts.length, (x) {
                                final line = (jobCounts[x].lines ?? [])[y];
                                return _TableCell(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                  padding: const EdgeInsets.only(top: 20),
                                  value: line.count.toString(),
                                  color: x % 2 == 0
                                      ? AppColors.grey13
                                      : Colors.white,
                                );
                              }),
                            );
                          }),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              SingleChildScrollView(
                controller: _nameColumnsController,
                physics: const AlwaysScrollableScrollPhysics(
                  parent: BouncingScrollPhysics(),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: List.generate(lines.length, (index) {
                    return SizedBox(
                      height: 80,
                      child: Text(
                        lines[index].name ?? '',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF363636),
                          fontWeight: FontWeight.w700,
                        ),
                      ).pl(value: 16).pt(value: 12),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class _TableCell extends StatelessWidget {
  const _TableCell({
    Key? key,
    required this.value,
    this.color,
    this.padding = EdgeInsets.zero,
    this.fontSize = 12,
    this.fontWeight = FontWeight.w600,
  }) : super(key: key);
  final String value;
  final Color? color;

  final EdgeInsets padding;
  final double fontSize;
  final FontWeight fontWeight;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: cellWidth,
      height: 80,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: color,
        border: const Border(
          bottom: BorderSide(width: 1.0, color: AppColors.grey11),
        ),
      ),
      alignment: Alignment.center,
      child: Padding(
        padding: padding,
        child: Text(
          value,
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: fontSize,
            color: const Color(0xFF363636),
            fontWeight: fontWeight,
          ),
        ),
      ),
    );
  }
}

class _TableHead extends StatelessWidget {
  final ScrollController scrollController;

  const _TableHead({
    Key? key,
    required this.scrollController,
  }) : super(key: key);

  String _getString(JobCount jobCount) {
    String value = 'NA';
    if (jobCount.type == "date") {
      value = jobCount.date != null
          ? intl.DateFormat('MM/dd').format(jobCount.date!)
          : 'NA';
    } else if (jobCount.type == "total") {
      value = "Total";
    }
    return value;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<JobCountBloc, JobCountState>(
      buildWhen: (p, c) => p.jobCounts != c.jobCounts,
      builder: (context, state) {
        final jobCounts = state.jobCounts;
        if (jobCounts.isEmpty) {
          return const SizedBox();
        }
        return SizedBox(
          height: cellHeight,
          child: Row(
            children: [
              Expanded(
                child: ListView(
                  controller: scrollController,
                  physics: const ClampingScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  children: List.generate(jobCounts.length, (index) {
                    return _TableCell(
                      color: index % 2 == 0 ? AppColors.grey13 : Colors.white,
                      value: _getString(jobCounts[index]),
                    );
                  }),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
