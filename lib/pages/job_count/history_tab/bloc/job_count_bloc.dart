import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'job_count_event.dart';
import 'job_count_state.dart';

///Bloc for repair ticket
class JobCountBloc extends Bloc<JobCountEvent, JobCountState> {
  JobCountBloc({
    JobApi? jobApi,
    TickingLogService? logService,
    TaskService? taskService,
    CacheService? cacheService,
    SecureConfigService? secureConfigService,
  }) : super(const JobCountState()) {
    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _tickingLogService.setClassName(toString());
    on<JobCountLoaded>(_onLoaded);
    on<JobCountIndexChanged>(_onIndexChanged);
  }

  late final JobApi _jobApi;
  late TickingLogService _tickingLogService;

  Future<void> _onIndexChanged(
    JobCountIndexChanged event,
    Emitter<JobCountState> emit,
  ) async {
    emit(state.copyWith(index: event.index));
    add(JobCountLoaded());
  }

  Future<void> _onLoaded(
    JobCountLoaded event,
    Emitter<JobCountState> emit,
  ) async {
    await EasyLoading.show(status: '');
    try {
      int index = state.index;
      String filter = '';
      if (index == 0) {
        filter = 'this_week';
      } else if (index == 1) {
        filter = 'last_week';
      }

      final jobCounts = await _jobApi.getJobCounts(filter);

      emit(
        state.copyWith(
          jobCounts: jobCounts.items,
          status: JobCountStatus.actionSuccess,
        ),
      );
    } catch (e, stack) {
      if (e is! SocketException) {
        emit(
          state.copyWith(
            error: e.toString(),
            status: JobCountStatus.actionFailure,
          ),
        );
      }

      _tickingLogService.error('JobCountLoaded', e.toString(), stack);
    }
    await EasyLoading.dismiss();
  }
}
