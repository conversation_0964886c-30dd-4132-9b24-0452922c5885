import 'package:ticking_api_client/ticking_api_client.dart';

abstract class JobCountEvent {}

///Load data
class JobCountLoaded extends JobCountEvent {
  JobCountLoaded();

  @override
  String toString() {
    return 'JobCountLoaded{}';
  }
}

class JobCountIndexChanged extends JobCountEvent {
  JobCountIndexChanged({
    required this.index,
  });

  final int index;

  @override
  String toString() {
    return 'JobCountLoaded{}';
  }
}
