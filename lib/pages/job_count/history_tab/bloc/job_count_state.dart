import 'dart:typed_data';
import 'package:ticking_api_client/ticking_api_client.dart';

class JobCountState {
  const JobCountState({
    this.status = JobCountStatus.init,
    this.error,
    this.jobCounts = const [],
    this.index = 0,
  });

  JobCountState copyWith({
    JobCountStatus? status,
    String? error,
    List<JobCount>? jobCounts,
    int? index,
  }) {
    return JobCountState(
      index: index ?? this.index,
      status: status ?? this.status,
      error: error ?? this.error,
      jobCounts: jobCounts ?? this.jobCounts,
    );
  }

  final String? error;
  final JobCountStatus status;
  final List<JobCount> jobCounts;
  final int index;
}

enum JobCountStatus {
  init,
  loading,
  actionSuccess,
  actionFailure,
}
