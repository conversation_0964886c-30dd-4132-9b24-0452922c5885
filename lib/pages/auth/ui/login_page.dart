import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

import 'forgot_password_page.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({Key? key}) : super(key: key);

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();
  final _scrollController = ScrollController();

  bool _passwordHide = true;

  @override
  void initState() {
    _accountController.text =
        BlocProvider.of<AuthBloc>(context).state.data.userName ?? '';
    _accountController.addListener(_inputListener);
    _passwordController.addListener(_inputListener);
    super.initState();
    // _accountController.text = '<EMAIL>';
    // _passwordController.text = '012345';
  }

  void _inputListener() {
    final isDisableLogin =
        BlocProvider.of<AuthBloc>(context).state.data.isDisableLogin;
    final bool isEmptyInput =
        (_accountController.text.isEmpty || _passwordController.text.isEmpty);

    if (isDisableLogin != isEmptyInput) {
      BlocProvider.of<AuthBloc>(context).add(AuthLoginDisabled(isEmptyInput));
    }
  }

  @override
  void dispose() {
    _accountController.dispose();
    _passwordController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final keyBoardHeight = MediaQuery.of(context).viewInsets.bottom;

    return Stack(
      children: [
        BlocConsumer<AuthBloc, AuthState>(
          listener: (context, state) {
            if (state is AuthSignInFailure) {
              DialogHelper.showError(content: state.error);
              return;
            }

            if (state is AuthSignInSuccess) {
              App.pushNamedAndPopUntil(AppRoutes.home, null, '/');
              return;
            }
          },
          builder: (context, state) {
            return _buildForm(state, keyBoardHeight);
          },
        ),
      ],
    );
  }

  Widget _buildForm(AuthState state, double keyBoardHeight) {
    return Stack(
      children: [
        SingleChildScrollView(
          controller: _scrollController,
          child: SizedBox(
            height: MediaQuery.of(context).size.height,
            child: Stack(
              children: [
                Image.asset(
                  'assets/image/login_image.png',
                  width: double.maxFinite,
                  fit: BoxFit.cover,
                ),
                Opacity(
                  opacity: 0.9,
                  child: Image.asset(
                    'assets/image/background.png',
                    width: double.maxFinite,
                    fit: BoxFit.cover,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 110),
                  child: Align(
                    alignment: FractionalOffset.topCenter,
                    child: Image.asset(
                      'assets/image/text_login.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Align(
                  alignment: Alignment.bottomCenter,
                  child: Image.asset(
                    'assets/image/shape_login.png',
                    height: MediaQuery.of(context).size.height / 2 - 40,
                    width: double.maxFinite,
                    fit: BoxFit.fill,
                    // height: 100,
                  ),
                ),
                SafeArea(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      (MediaQuery.of(context).size.height / 2 + 60)
                          .toVSizeBox(),
                      _buildFormPadding(
                        child: OutLineTextField.big(
                          controller: _accountController,
                          scrollPadding: const EdgeInsets.only(bottom: 200),
                          radius: 75,
                          hintText: 'Username',
                          // backgroundColor: AppColors.grey.withAlpha(50),
                          autoFocus: true,
                          contentPadding: const EdgeInsets.only(
                              left: 30, right: 5, top: 15, bottom: 15),
                          showBorder: true,
                        ),
                      ),
                      20.toVSizeBox(),
                      _buildFormPadding(
                        child: OutLineTextField.big(
                          controller: _passwordController,
                          scrollPadding: const EdgeInsets.only(bottom: 140),
                          radius: 75,
                          hintText: 'Password',
                          autoFocus: false,
                          obscureText: _passwordHide,
                          suffixIcon: IconButton(
                            icon: Icon(
                              // Based on passwordVisible state choose the icon
                              _passwordHide
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: Theme.of(context).lightGrey(),
                            ),
                            onPressed: () {
                              setState(() {
                                _passwordHide = !_passwordHide;
                              });
                            },
                          ),
                          contentPadding: const EdgeInsets.only(
                              left: 30, right: 5, top: 15, bottom: 15),
                          showBorder: true,
                        ),
                      ),
                      // 10.toVSizeBox(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          InkWell(
                            onTap: () {
                              _navigatorForgotPassword();
                            },
                            child: Padding(
                              padding: const EdgeInsets.only(
                                top: 10,
                                bottom: 10,
                                right: 50,
                                left: 20,
                              ),
                              child: Text(
                                'Forgot your password?',
                                style: Theme.of(context)
                                    .normalStyle
                                    .copyWith(color: AppColors.primaryColor),
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // 20.toVSizeBox(),
                      Container(
                        width: double.maxFinite,
                        margin: const EdgeInsets.only(left: 50, right: 50),
                        child: Btn.main(
                          text: 'LOGIN',
                          style: (_accountController.text.isEmpty ||
                                  _passwordController.text.isEmpty)
                              ? AppButtonStyle.ghostStyle(
                                  context,
                                  props: BtnStyleProps(
                                    padding: const EdgeInsets.all(16),
                                  ),
                                )
                              : AppButtonStyle.primaryStyle(
                                  context,
                                  props: BtnStyleProps(
                                    padding: const EdgeInsets.all(16),
                                  ),
                                ),
                          onPressed: (_accountController.text.isEmpty ||
                                  _passwordController.text.isEmpty)
                              ? null
                              : () {
                                  FocusScope.of(context).unfocus();
                                  BlocProvider.of<AuthBloc>(context).add(
                                    AuthSignedIn(
                                      _accountController.text,
                                      _passwordController.text,
                                    ),
                                  );
                                },
                        ),
                      ),
                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        if (state is AuthBusy)
          const Center(
            child: LoadingIndicator(),
          )
      ],
    );
  }

  void _navigatorForgotPassword() {
    Navigator.push(context,
        MaterialPageRoute(builder: (context) => const ForgotPassword()));
  }

  Widget _buildFormPadding({Widget? child}) {
    return Container(
      width: double.maxFinite,
      margin: const EdgeInsets.only(left: 50, right: 50),
      child: child,
    );
  }
}
