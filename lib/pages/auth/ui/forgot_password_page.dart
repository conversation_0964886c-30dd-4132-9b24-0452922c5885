import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/utils/utils.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

class ForgotPassword extends StatefulWidget {
  const ForgotPassword({Key? key}) : super(key: key);

  @override
  _ForgotPasswordState createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  final TextEditingController _emailController = TextEditingController();

  @override
  void initState() {
    _emailController.addListener(_inputListener);
    BlocProvider.of<AuthBloc>(context).add(AuthSendUpdate(false));
    super.initState();
  }

  void _inputListener() {
    final isDisableLogin =
        BlocProvider.of<AuthBloc>(context).state.data.isDisableEmail;
    final bool isEmptyInput =
        (_emailController.text.isEmpty || _emailController.text.isEmpty);

    if (isDisableLogin != isEmptyInput) {
      BlocProvider.of<AuthBloc>(context).add(AuthEmailDisabled(isEmptyInput));
    }
  }

  @override
  void dispose() {
    super.dispose();

    _emailController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: _buildAppbar(),
        body: _buildBody(),
      ),
    );
  }

  PreferredSizeWidget _buildAppbar() {
    return AppBar(
      leading: BackButton(
        onPressed: () {
          Navigator.pop(context);
        },
        color: Colors.black,
      ),
      title: Text(
        'Forgot Password',
        style: Theme.of(context).normalStyle.copyWith(color: AppColors.black),
      ),
      centerTitle: true,
    );
  }

  Widget _buildBody() {
    return BlocConsumer<AuthBloc, AuthState>(listener: (context, state) {
      if (state is AuthMailSendFailure) {
        DialogHelper.showError(content: state.error);
      }
    }, builder: (context, state) {
      return _buildForm(state);
    });
  }

  Widget _buildForm(AuthState state) {
    Widget child = _buildSendMail();

    if (state.data.isResend) {
      child = _buildCheckMail();
    }

    return Stack(
      children: [
        SizedBox(
          height: double.infinity,
          child: SingleChildScrollView(child: child),
        ),
        if (state is AuthBusy)
          Positioned(
            top: ViewUtils.getPercentHeight(percent: 0.12),
            left: 0,
            right: 0,
            child: const LoadingIndicator(),
          )
      ],
    );
  }

  Widget _buildSendMail() {
    return Column(
      children: [
        ViewUtils.getPercentHeight(percent: 0.087).toVSizeBox(),
        Center(
          child: Image.asset(
            'assets/image/input_email.png',
            fit: BoxFit.fill,
            // height: 100,
          ),
        ),
        ViewUtils.getPercentHeight(percent: 0.06).toVSizeBox(),
        Text(
          'Please enter the email address associated '
          '\nwith your account. '
          'We will email you a link to\n reset your password.',
          style: Theme.of(context).normalStyle.copyWith(color: AppColors.grey),
          textAlign: TextAlign.center,
        ),
        Padding(
          padding:
              const EdgeInsets.only(left: 50, right: 50, top: 30, bottom: 20),
          child: OutLineTextField.big(
            controller: _emailController,
            scrollPadding: const EdgeInsets.only(bottom: 250),
            radius: 75,
            hintText: 'Enter email address',
            hintStyle:
                Theme.of(context).normalStyle.copyWith(color: AppColors.grey),
            autoFocus: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
            showBorder: true,
          ),
        ),
        Padding(
          // width: double.maxFinite,
          padding: const EdgeInsets.only(left: 50, right: 50),
          child: Btn.main(
            style:
                (_emailController.text.isEmpty || _emailController.text.isEmpty)
                    ? AppButtonStyle.ghostStyle(
                        context,
                        props: BtnStyleProps(
                          padding: const EdgeInsets.all(16),
                        ),
                      )
                    : AppButtonStyle.primaryStyle(
                        context,
                        props: BtnStyleProps(
                          padding: const EdgeInsets.all(16),
                        ),
                      ),
            child: Stack(
              children: [
                Center(
                  child: Text(
                    'SEND',
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: (_emailController.text.isEmpty ||
                                  _emailController.text.isEmpty)
                              ? Theme.of(context).primaryColor
                              : Theme.of(context).backgroundColor,
                        ),
                  ).pt(value: 4),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 150),
                  child: Center(
                    child: Icon(
                      Icons.arrow_forward,
                      color: (_emailController.text.isEmpty ||
                              _emailController.text.isEmpty)
                          ? Theme.of(context).primaryColor
                          : Theme.of(context).backgroundColor,
                    ),
                  ),
                )
              ],
            ),
            // onPressed: () {
            //
            // },
            onPressed:
                (_emailController.text.isEmpty || _emailController.text.isEmpty)
                    ? null
                    : () {
                        FocusScope.of(context).unfocus();
                        BlocProvider.of<AuthBloc>(context).add(
                          AuthMailSend(
                            isResend: true,
                            email: _emailController.text,
                          ),
                        );
                      },
          ),
        ),
        30.toVSizeBox(),
      ],
    );
  }

  Widget _buildCheckMail() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ViewUtils.getPercentHeight(percent: 0.087).toVSizeBox(),
        Center(
          child: Image.asset(
            'assets/image/mail.png',
            fit: BoxFit.fill,
            // height: 100,
          ),
        ),
        ViewUtils.getPercentHeight(percent: 0.06).toVSizeBox(),
        Text('Check your mail',
            style: Theme.of(context).headerTextStyle,
            textAlign: TextAlign.center),
        10.toVSizeBox(),
        Text(
          'An email has been sent to\n'
          ' ${_emailController.text}.',
          style: Theme.of(context)
              .normalStyle
              .copyWith(color: AppColors.grey, fontSize: 16),
          textAlign: TextAlign.center,
        ),
        ViewUtils.getPercentHeight(percent: 0.087).toVSizeBox(),
        Container(
          width: double.maxFinite,
          margin: const EdgeInsets.only(left: 50, right: 50),
          child: Btn.main(
            text: 'OK',
            onPressed: () {
              App.pop();
              BlocProvider.of<AuthBloc>(context).add(AuthSendUpdate(false));
            },
          ),
        ),
        10.toVSizeBox(),
        RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: 'You not yet receive email? ',
                style: Theme.of(context)
                    .normalStyle
                    .copyWith(color: AppColors.grey, fontSize: 16),
              ),
              TextSpan(
                text: 'Resend',
                style: Theme.of(context).normalStyle.copyWith(
                    color: AppColors.primaryColor,
                    decoration: TextDecoration.underline,
                    fontSize: 16),
                recognizer: TapGestureRecognizer()
                  ..onTap = () {
                    BlocProvider.of<AuthBloc>(context).add(
                      AuthMailSend(
                        isResend: true,
                        email: _emailController.text,
                      ),
                    );
                  },
              ),
            ],
          ),
        ),
        30.toVSizeBox(),
      ],
    );
  }
}
