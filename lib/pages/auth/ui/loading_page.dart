import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/job_detail_arg.dart';
import 'package:ticking_app/core/arguments/job_repair_arg.dart';
import 'package:ticking_app/core/arguments/log_time_arg.dart';
import 'package:ticking_app/model/service_type.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

class LoadingPage extends StatefulWidget {
  const LoadingPage({Key? key}) : super(key: key);

  @override
  _LoadingPageState createState() => _LoadingPageState();
}

class _LoadingPageState extends State<LoadingPage> {
  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthAlreadySignIn) {
          ///Go to home page
          App.pushNamedAndPopUntil(AppRoutes.home, null, '/');
          return;
        }

        if (state is AuthNotSignIn) {
          ///Go to login page
          App.pushNamedAndPopUntil(AppRoutes.login, null, '/');
          return;
        }
        if (state is AuthJobStartSuccess) {
          ///Go to job detail

          final serviceType = state.data.job?.type;

          switch (serviceType.jobServiceType) {
            case ServiceType.cleaning:
              App.pushNamedAndPopUntil(
                  AppRoutes.details,
                  JobDetailArg(
                    job: state.data.job!,
                  ),
                  '/');

              break;
            case ServiceType.repair:
              App.pushNamedAndPopUntil(
                  AppRoutes.repair,
                  JobRepairArg(
                    job: state.data.job!,
                  ),
                  '/');

              break;
          }

          return;
        }

        if (state is AuthWorkLogStartSuccess) {
          ///Go to log time detail

          final workLog = state.data.workLog;
          App.pushNamedAndPopUntil(
              AppRoutes.logTime,
              LogTimeArg(
                workLog: workLog,
              ),
              '/');
          return;
        }

        ///listen
      },
      child: const Scaffold(
        body: Center(
          child: LoadingIndicator(),
        ),
      ),
    );
  }
}
