import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/auth/bloc/auth_bloc.dart';
import 'package:ticking_app/pages/auth/bloc/change_password/change_password_bloc.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:ticking_app/widgets/textfield/outline_textfield.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({Key? key}) : super(key: key);

  @override
  _ChangePasswordPageState createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final TextEditingController _currentPassword = TextEditingController();
  final TextEditingController _newPassword = TextEditingController();
  final TextEditingController _repeatNewPassword = TextEditingController();
  final ChangePasswordBloc _bloc = ChangePasswordBloc();

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: _buildAppbar(),
        body: BlocConsumer<ChangePasswordBloc, ChangePasswordState>(
            listener: (context, state) {
          if (state.status == ChangePasswordStatus.submitSuccess) {
            _showChangePasswordDialog(
              context: context,
            );
            return;
          } else if (state.status == ChangePasswordStatus.failure) {
            DialogHelper.showError(content: state.errorMessage);
            return;
          }
        }, builder: (context, state) {
          if (state.status == ChangePasswordStatus.loading) {
            return const Center(
              child: LoadingIndicator(),
            );
          }
          return Scrollbar(
            child: ListView(
              children: [
                100.toVSizeBox(),
                OutLineTextField.big(
                  controller: _currentPassword,
                  scrollPadding: const EdgeInsets.only(bottom: 200),
                  radius: 75,
                  obscureText: state.isHideCurrentPassword,
                  hintText: 'Current password',
                  hintStyle:
                      const TextStyle(color: AppColors.grey2, fontSize: 14),
                  autoFocus: false,
                  borderColor: state.isCurrentPasswordCorrect == false
                      ? AppColors.redAccentColor
                      : const Color(0xFFE5E5E5),
                  contentPadding: const EdgeInsets.only(
                      left: 30, right: 5, top: 15, bottom: 15),
                  showBorder: true,
                  obscuringCharacter: '●',
                  onChanged: (value) {
                    _bloc.add(ChangePasswordRepeatNotErrorDisplayed());
                  },
                  suffixIcon: IconButton(
                    icon: Icon(
                      state.isHideCurrentPassword == false
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Theme.of(context).lightGrey(),
                    ),
                    onPressed: () {
                      _bloc.add(ChangePasswordCurrentPasswordDisplayed(
                          isDisplay: state.isHideCurrentPassword));
                    },
                  ),
                ),
                _buildValidator(state.messageCurrentPassword,
                        state.isCurrentPasswordCorrect)
                    .pb(value: 20)
                    .pt(value: 3),
                OutLineTextField.big(
                  controller: _newPassword,
                  hintStyle:
                      const TextStyle(color: AppColors.grey2, fontSize: 14),
                  scrollPadding: const EdgeInsets.only(bottom: 140),
                  radius: 75,
                  hintText: 'New password',
                  obscuringCharacter: '●',
                  autoFocus: false,
                  onChanged: (value) {
                    _bloc.add(ChangePasswordRepeatNotErrorDisplayed());
                  },
                  obscureText: state.isHideNewPassword,
                  suffixIcon: IconButton(
                    icon: Icon(
                      state.isHideNewPassword == false
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Theme.of(context).lightGrey(),
                    ),
                    onPressed: () {
                      _bloc.add(ChangePasswordNewPasswordDisplayed(
                          isDisplay: state.isHideNewPassword));
                    },
                  ),
                  borderColor: state.isNewPasswordCorrect == false
                      ? AppColors.redAccentColor
                      : const Color(0xFFE5E5E5),
                  contentPadding: const EdgeInsets.only(
                      left: 30, right: 5, top: 15, bottom: 15),
                  showBorder: true,
                ),
                _buildValidator(
                        state.messageNewPassword, state.isNewPasswordCorrect)
                    .pb(value: 20)
                    .pt(value: 3),
                OutLineTextField.big(
                  controller: _repeatNewPassword,
                  scrollPadding: const EdgeInsets.only(bottom: 140),
                  radius: 75,
                  hintStyle:
                      const TextStyle(color: AppColors.grey2, fontSize: 14),
                  hintText: 'Repeat new password',
                  autoFocus: false,
                  obscuringCharacter: '●',
                  obscureText: state.isHideRepeatNewPassword,
                  borderColor: state.isRepeatPasswordCorrect == false
                      ? AppColors.redAccentColor
                      : const Color(0xFFE5E5E5),
                  suffixIcon: IconButton(
                    icon: Icon(
                      // Based on passwordVisible state choose the icon
                      state.isHideRepeatNewPassword == false
                          ? Icons.visibility
                          : Icons.visibility_off,
                      color: Theme.of(context).lightGrey(),
                    ),
                    onPressed: () {
                      _bloc.add(ChangePasswordRepeatNewPasswordDisplayed(
                          isDisplay: state.isHideRepeatNewPassword));
                    },
                  ),
                  onChanged: (value) {
                    _bloc.add(ChangePasswordRepeatNotErrorDisplayed());
                  },
                  contentPadding: const EdgeInsets.only(
                    left: 30,
                    right: 5,
                    top: 15,
                  ),
                  showBorder: true,
                ),
                _buildValidator(state.messageRepeatPassword,
                        state.isRepeatPasswordCorrect)
                    .pt(value: 3),
                SizedBox(
                  width: double.maxFinite,
                  child: Btn.main(
                    text: 'SUBMIT',
                    onPressed: () {
                      FocusScope.of(context).unfocus();
                      _bloc.add(ChangePasswordSubmitted(
                          newPassword: _newPassword.text,
                          oldPassword: _currentPassword.text,
                          repeatNewPassword: _repeatNewPassword.text));
                    },
                  ),
                ).pt(value: 25),
              ],
            ).pl(value: 50).pr(value: 50),
          );
        }),
      ),
    );
  }

  Widget _buildValidator(String content, bool validatorHide) {
    if (validatorHide == true) return const SizedBox();
    return Text(
      content,
      textAlign: TextAlign.right,
      style: const TextStyle(color: AppColors.redAccentColor, fontSize: 12),
    );
  }

  PreferredSizeWidget _buildAppbar() {
    return AppBar(
      title: Text(
        'Change password',
        style: Theme.of(context)
            .textTheme
            .headline6
            ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
      ),
      centerTitle: true,
      backgroundColor: Colors.white,
      actions: [
        Assets.icon.noAvatar.svg(width: 25, height: 25).pr(value: 10),
      ],
      leading: const BackButton(
        color: Colors.black,
      ),
    );
  }

  void _showChangePasswordDialog({BuildContext? context}) {
    showDialog(
        context: context ?? App.overlayContext!,
        builder: (BuildContext context) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              100.toVSizeBox(),
              Padding(
                padding: const EdgeInsets.all(10.0),
                child: GestureDetector(
                    onTap: () {
                      App.pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 40,
                    )),
              ),
              AlertDialog(
                  shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(16.0))),
                  contentPadding: const EdgeInsets.only(top: 10.0),
                  insetPadding: const EdgeInsets.only(left: 10, right: 10),
                  content: Column(
                    children: [
                      Assets.icon.log
                          .svg(
                            width: 47,
                            height: 47,
                          )
                          .pt(value: 24),
                      const Text(
                        'Change password successfully',
                        style: TextStyle(
                          color: Colors.black,
                          fontSize: 18,
                        ),
                        textAlign: TextAlign.center,
                      ).pt(value: 16),
                      SizedBox(
                        height: 48,
                        width: double.maxFinite,
                        child: Row(
                          children: [
                            Expanded(
                              child: CommonOutlineButton(
                                text: 'BACK TO HOME',
                                fontWeight: FontWeight.w600,
                                onPressed: () {
                                  App.pop();
                                  App.pushNamed(AppRoutes.home);
                                },
                              ),
                            ),
                            10.toHSizeBox(),
                            Expanded(
                              child: CommonPrimaryButton(
                                  child: 'LOGIN'
                                      .text
                                      .textStyle(Theme.of(context)
                                          .textTheme
                                          .bodyText1!)
                                      .size(Dimens.text_XL)
                                      .color(AppColors.materialWhite)
                                      .fontWeight(FontWeight.w600)
                                      .make(),
                                  onPressed: () {
                                    App.pop();
                                    BlocProvider.of<AuthBloc>(context)
                                        .add(AuthLogout());
                                  }),
                            ),
                          ],
                        ),
                      ).pt(value: 47).pb(value: 16).pr(value: 20).pl(value: 20),
                    ],
                  )),
            ],
          );
        });
  }
}
