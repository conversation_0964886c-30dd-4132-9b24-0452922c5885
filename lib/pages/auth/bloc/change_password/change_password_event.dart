part of 'change_password_bloc.dart';

abstract class ChangePasswordEvent {}

class ChangePasswordStarted extends ChangePasswordEvent {
  ChangePasswordStarted();

  @override
  String toString() {
    return 'ChangePasswordStarted{}';
  }
}

class ChangePasswordNewPasswordDisplayed extends ChangePasswordEvent {
  ChangePasswordNewPasswordDisplayed({required this.isDisplay});

  final bool isDisplay;

  @override
  String toString() {
    return 'ChangePasswordNewPasswordDisplayed{isDisplay: $isDisplay}';
  }
}

class ChangePasswordCurrentPasswordDisplayed extends ChangePasswordEvent {
  ChangePasswordCurrentPasswordDisplayed({required this.isDisplay});

  final bool isDisplay;

  @override
  String toString() {
    return 'ChangePasswordCurrentPasswordDisplayed{isDisplay: $isDisplay}';
  }
}

class ChangePasswordRepeatNewPasswordDisplayed extends ChangePasswordEvent {
  ChangePasswordRepeatNewPasswordDisplayed({required this.isDisplay});

  final bool isDisplay;

  @override
  String toString() {
    return 'ChangePasswordRepeatNewPasswordDisplayed{isDisplay: $isDisplay}';
  }
}

class ChangePasswordSubmitted extends ChangePasswordEvent {
  ChangePasswordSubmitted(
      {required this.oldPassword,
      required this.newPassword,
      required this.repeatNewPassword});

  final String oldPassword;
  final String newPassword;
  final String repeatNewPassword;

  @override
  String toString() {
    return 'ChangePasswordSubmitted{oldPassword: $oldPassword, newPassword: $newPassword, repeatNewPassword: $repeatNewPassword}';
  }
}

class ChangePasswordRepeatNotErrorDisplayed extends ChangePasswordEvent {
  @override
  String toString() {
    return 'ChangePasswordRepeatNotErrorDisplayed{}';
  }
}
