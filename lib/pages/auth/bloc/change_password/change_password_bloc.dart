import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_log_service/ticking_log_service.dart';

part 'change_password_event.dart';

part 'change_password_state.dart';

class ChangePasswordBloc
    extends Bloc<ChangePasswordEvent, ChangePasswordState> {
  ChangePasswordBloc(
      {AuthApi? authApi,
      TickingLogService? logService,
      SecureConfigService? secureConfigService,
      ApiClient? client})
      : super(const ChangePasswordState()) {
    on<ChangePasswordStarted>(_onStarted);
    on<ChangePasswordNewPasswordDisplayed>(_onNewPasswordDisplayed);
    on<ChangePasswordRepeatNewPasswordDisplayed>(_onRepeatNewPasswordDisplayed);
    on<ChangePasswordCurrentPasswordDisplayed>(_onCurrentPasswordDisplayed);
    on<ChangePasswordSubmitted>(_onSubmitted);
    on<ChangePasswordRepeatNotErrorDisplayed>(_onErrorDisplayed);
    _client = client ?? GetIt.I<ApiClient>();
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _authApi = authApi ?? GetIt.I<AuthApi>();
  }

  late final TickingLogService _tickingLogService;
  late final AuthApi _authApi;
  late final SecureConfigService _secureConfigService;
  late final ApiClient _client;

  Future<void> _onStarted(
    ChangePasswordStarted event,
    Emitter<ChangePasswordState> emit,
  ) async {}

  Future<void> _onErrorDisplayed(
    ChangePasswordRepeatNotErrorDisplayed event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(state.copyWith(
        isCurrentPasswordCorrect: true,
        isNewPasswordCorrect: true,
        isRepeatPasswordCorrect: true,
        status: ChangePasswordStatus.dataChange));
  }

  Future<void> _onNewPasswordDisplayed(
    ChangePasswordNewPasswordDisplayed event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(state.copyWith(
        isHideNewPassword: !event.isDisplay,
        status: ChangePasswordStatus.dataChange));
  }

  Future<void> _onCurrentPasswordDisplayed(
    ChangePasswordCurrentPasswordDisplayed event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(state.copyWith(
        isHideCurrentPassword: !event.isDisplay,
        status: ChangePasswordStatus.dataChange));
  }

  Future<void> _onRepeatNewPasswordDisplayed(
    ChangePasswordRepeatNewPasswordDisplayed event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(state.copyWith(
        isHideRepeatNewPassword: !event.isDisplay,
        status: ChangePasswordStatus.dataChange));
  }

  Future<void> _onSubmitted(
    ChangePasswordSubmitted event,
    Emitter<ChangePasswordState> emit,
  ) async {
    emit(state.copyWith(status: ChangePasswordStatus.loading));
    try {
      if (event.oldPassword.isEmpty) {
        emit(state.copyWith(
            isCurrentPasswordCorrect: false,
            status: ChangePasswordStatus.dataChange,
            messageCurrentPassword: 'Current password is required'));
        return;
      }

      if (event.newPassword.isEmpty) {
        emit(state.copyWith(
            isNewPasswordCorrect: false,
            status: ChangePasswordStatus.dataChange,
            messageNewPassword: 'New password is required'));
        return;
      }
      if (event.repeatNewPassword.isEmpty) {
        emit(state.copyWith(
            isRepeatPasswordCorrect: false,
            status: ChangePasswordStatus.dataChange,
            messageRepeatPassword: 'Repeat new password is required'));
        return;
      }
      if (event.newPassword != event.repeatNewPassword) {
        emit(state.copyWith(
            messageRepeatPassword: 'Repeat new password does not match',
            isRepeatPasswordCorrect: false,
            status: ChangePasswordStatus.dataChange));
        return;
      }
      await _authApi.changePassword(event.oldPassword, event.newPassword);
      await _client.clearSession();
      String? userName = _secureConfigService.userInfo?.userName;

      if (userName != null) {
        await _authApi.login(userName, event.newPassword);
      }
      emit(state.copyWith(status: ChangePasswordStatus.submitSuccess));
    } catch (e, stack) {
      _tickingLogService.error('LogTimeLogged', e.toString(), stack);
      if (e is SocketException) {
        emit(state.copyWith(
            errorMessage: 'No internet connection',
            status: ChangePasswordStatus.failure));
        return;
      } else {
        emit(state.copyWith(
            errorMessage: e.toString(), status: ChangePasswordStatus.failure));
      }
    }
  }
}
