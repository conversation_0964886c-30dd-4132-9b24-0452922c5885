part of 'change_password_bloc.dart';

class ChangePasswordState {
  const ChangePasswordState(
      {this.status = ChangePasswordStatus.load,
      this.errorMessage = 'N/a',
      this.isHideNewPassword = true,
      this.isHideRepeatNewPassword = true,
      this.isRepeatPasswordCorrect = true,
      this.isCurrentPasswordCorrect = true,
      this.isNewPasswordCorrect = true,
      this.messageCurrentPassword = '',
      this.messageNewPassword = '',
      this.messageRepeatPassword = '',
      this.isHideCurrentPassword = true});

  ChangePasswordState copyWith(
      {bool? isHideCurrentPassword,
      bool? isNewPasswordCorrect,
      bool? isCurrentPasswordCorrect,
      bool? isRepeatPasswordCorrect,
      bool? isHideNewPassword,
      bool? isHideRepeatNewPassword,
      String? errorMessage,
      String? messageRepeatPassword,
      String? messageCurrentPassword,
      String? messageNewPassword,
      ChangePasswordStatus? status}) {
    return ChangePasswordState(
      isHideCurrentPassword:
          isHideCurrentPassword ?? this.isHideCurrentPassword,
      messageNewPassword: messageNewPassword ?? this.messageNewPassword,
      messageCurrentPassword:
          messageCurrentPassword ?? this.messageCurrentPassword,
      messageRepeatPassword:
          messageRepeatPassword ?? this.messageRepeatPassword,
      isCurrentPasswordCorrect:
          isCurrentPasswordCorrect ?? this.isCurrentPasswordCorrect,
      isNewPasswordCorrect: isNewPasswordCorrect ?? this.isNewPasswordCorrect,
      isRepeatPasswordCorrect:
          isRepeatPasswordCorrect ?? this.isRepeatPasswordCorrect,
      isHideNewPassword: isHideNewPassword ?? this.isHideNewPassword,
      isHideRepeatNewPassword:
          isHideRepeatNewPassword ?? this.isHideRepeatNewPassword,
      errorMessage: errorMessage ?? this.errorMessage,
      status: status ?? this.status,
    );
  }

  final String errorMessage;
  final ChangePasswordStatus status;
  final bool isHideCurrentPassword;
  final bool isHideNewPassword;
  final bool isHideRepeatNewPassword;
  final bool isRepeatPasswordCorrect;
  final bool isCurrentPasswordCorrect;
  final bool isNewPasswordCorrect;
  final String messageRepeatPassword;
  final String messageCurrentPassword;
  final String messageNewPassword;
}

enum ChangePasswordStatus { loading, load, failure, dataChange, submitSuccess }
