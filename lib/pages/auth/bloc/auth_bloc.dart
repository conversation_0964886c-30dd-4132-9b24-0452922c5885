import 'dart:async';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:email_validator/email_validator.dart';
import 'package:get_it/get_it.dart';
import 'package:meta/meta.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/network_service/network_service.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_log_service/ticking_log_service.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  AuthBloc({
    SecureConfigService? secureConfigService,
    AuthApi? authApi,
    TickingLogService? logService,
    ApiClient? client,
    ConfigurationApi? configurationApi,
    TemplateApi? templateApi,
    CacheService? cacheService,
    NetworkService? networkService,
    TaskService? taskService,
  }) : super(const AuthLoading(AuthData())) {
    ///Get services
    _client = client ?? GetIt.I<ApiClient>();
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
    _authApi = authApi ?? GetIt.I<AuthApi>();
    _configurationApi = configurationApi ?? GetIt.I<ConfigurationApi>();
    _templateApi = templateApi ?? GetIt.I<TemplateApi>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _networkService = networkService ?? GetIt.I<NetworkService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _tickingLogService.setClassName(toString());

    ///Handle event
    on<AuthLoaded>(_onLoaded);
    on<AuthSignedIn>(_onSignedIn);
    on<AuthLogout>(_onLogout);
    on<AuthMailSend>(_onSendMail);
    on<AuthSendUpdate>(_onSendUpdate);
    on<AuthLoginDisabled>(_onLoginDisabled);
    on<AuthEmailDisabled>(_onEmailDisabled);
    on<AuthLogoutChecked>(_onLogoutChecked);
  }

  Timer? _timer;
  late final SecureConfigService _secureConfigService;
  late final TickingLogService _tickingLogService;
  late final ApiClient _client;
  late final AuthApi _authApi;
  late final TemplateApi _templateApi;
  late final ConfigurationApi _configurationApi;
  late final CacheService _cacheService;
  late final NetworkService _networkService;
  late final TaskService _taskService;

  Future<void> _onLogoutChecked(
    AuthLogoutChecked event,
    Emitter<AuthState> emit,
  ) async {
    try {
      final loginTime = _secureConfigService.loginTime;
      _timer?.cancel();
      if (loginTime != null) {
        final DateTime dateLoginTime = loginTime.clearTime();

        final DateTime now = DateTime.now();

        final DateTime dateNowTime = now.clearTime();

        ///Check session expired.
        if (dateLoginTime.difference(dateNowTime).inDays != 0) {
          add(AuthLogout());
          return;
        }
      }

      _timer = Timer.periodic(
        const Duration(seconds: 30),
        (Timer timer) {
          add(AuthLogoutChecked());
        },
      );
    } catch (e, _) {}
  }

  Future<void> _onEmailDisabled(
    AuthEmailDisabled event,
    Emitter<AuthState> emit,
  ) async {
    emit(
      AuthUpdateSuccess(
        state.data.copyWith(
          isDisableEmail: event.isDisabled,
        ),
      ),
    );
  }

  Future<void> _onLoginDisabled(
    AuthLoginDisabled event,
    Emitter<AuthState> emit,
  ) async {
    emit(
      AuthUpdateSuccess(
        state.data.copyWith(
          isDisableLogin: event.isDisabled,
        ),
      ),
    );
  }

  ///Load data event
  Future<void> _onLoaded(
    AuthLoaded event,
    Emitter<AuthState> emit,
  ) async {
    ///Init config service, api client
    await _secureConfigService.init();
    emit(
        AuthBusy(state.data.copyWith(userName: _secureConfigService.userName)));
    await _cacheService.init();
    await _client.init();
    await _client.config(ApiConfig(url: _secureConfigService.serverUrl));
    await _networkService.init();
    await _taskService.init();

    final loginTime = _secureConfigService.loginTime;

    ///Check previous login
    if (loginTime != null) {
      final DateTime dateLoginTime = loginTime.clearTime();

      final DateTime now = DateTime.now();

      final DateTime dateNowTime = now.clearTime();

      ///Check session expired.
      if (dateLoginTime.difference(dateNowTime).inDays != 0) {
        await _cacheService.emptyCache();

        await _client.clearSession();

        await _secureConfigService.clear();

        _taskService.startLoop();

        emit(AuthNotSignIn(state.data));
        return;
      }

      try {
        ///Get configuration  image threshold and distance threshold
        final configurationResult = await _configurationApi.getConfiguration();

        ///Get job task templates
        final jobTaskTemplatesResult = await _templateApi.getJobTaskTemplates();
        // final jobTaskTemplatesResult =
        //     ApiListResult<JobTask>.fromJson(templateMocData);

        final smsTemplateResult = await _templateApi.getSmsTemplate();

        await _cacheService.setJobTaskTemplates(jobTaskTemplatesResult.items);

        await _cacheService.setSmsTemplate(smsTemplateResult.data);

        ///Set configuration image threshold and distance threshold
        await _secureConfigService.setConfiguration(configurationResult.data);
      } catch (_) {}

      final dataTaskList = _cacheService.getDataTasks;
      await _taskService.addDataTaskList(dataTaskList);

      await _taskService.executeAllTasks();
      _taskService.startLoop();

      ///Check have job detail
      DateTime? _jobDateStart = _cacheService.jobDateStart;
      Job? job = _cacheService.jobStart;

      /// Check have work log
      WorkLog? _workLog = _cacheService.workLog;

      _timer?.cancel();
      _timer = Timer.periodic(
        const Duration(seconds: 30),
        (Timer timer) {
          add(AuthLogoutChecked());
        },
      );

      if (_jobDateStart != null && job != null) {
        emit(
          AuthJobStartSuccess(
            state.data.copyWith(jobDateStart: _jobDateStart, job: job),
          ),
        );

        return;
      }

      if(_workLog != null){
        emit(
          AuthWorkLogStartSuccess(
            state.data.copyWith(workLog: _workLog),
          ),
        );

        return;
      }

      ///Check upload session
      emit(AuthAlreadySignIn(state.data));
      return;
    }

    /// go to login page
    emit(AuthNotSignIn(state.data));
  }

  ///Sign in event
  Future<void> _onSignedIn(
    AuthSignedIn event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(AuthBusy(state.data));

      final result = await _authApi.login(event.userName, event.password);

      ///Set login time
      await _secureConfigService.setLoginTime(DateTime.now());
      final userInfo = result.data.copyWith(
        userName: event.userName,
      );
      final DefaultRoutingDestination? defaultRoutingDestination =
          result.data.defaultRoutingDestination;

      if (defaultRoutingDestination != null) {
        _cacheService.setDefaultRoutingDestination(defaultRoutingDestination);
      }

      ///Cache user info
      await _secureConfigService.setUserInfo(userInfo);
      if (userInfo.userName != null) {
        await _secureConfigService.setUserName(userInfo.userName!);
      }

      ///Get configuration  image threshold and distance threshold
      final configurationResult = await _configurationApi.getConfiguration();

      ///Get job task templates
      final jobTaskTemplatesResult = await _templateApi.getJobTaskTemplates();
      // final jobTaskTemplatesResult =
      //     ApiListResult<JobTask>.fromJson(templateMocData);

      final smsTemplateResult = await _templateApi.getSmsTemplate();

      await _cacheService.setJobTaskTemplates(jobTaskTemplatesResult.items);

      await _cacheService.setSmsTemplate(smsTemplateResult.data);

      ///Set configuration image threshold and distance threshold
      await _secureConfigService.setConfiguration(configurationResult.data);

      final dataTaskList = _cacheService.getDataTasks;
      await _taskService.addDataTaskList(dataTaskList);

      await _taskService.executeAllTasks();

      _taskService.startLoop();

      emit(
        AuthSignInSuccess(
          state.data.copyWith(userInfo: userInfo, userName: userInfo.userName),
        ),
      );
      _timer?.cancel();
      _timer = Timer.periodic(
        const Duration(seconds: 30),
        (Timer timer) {
          add(AuthLogoutChecked());
        },
      );
    } catch (e, stack) {
      _tickingLogService.error('AuthSignInFailure', e.toString(), stack);

      if (e is SocketException) {
        emit(
          AuthSignInFailure(data: state.data, error: 'No internet connection'),
        );
        return;
      }

      emit(
        AuthSignInFailure(
          data: state.data,
          error: e.toString(),
        ),
      );
    }
  }

  Future<void> _onLogout(
    AuthLogout event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _client.clearSession();
      await _cacheService.emptyCache();
      ///Set login time
      await _secureConfigService.clear();
      _timer?.cancel();

      emit(AuthLogoutSuccess(state.data));
    } catch (e, _) {}
  }

  Future<void> _onSendUpdate(
    AuthSendUpdate event,
    Emitter<AuthState> emit,
  ) async {
    emit(
      AuthMailSendSuccess(
        state.data.copyWith(
          isResend: event.isResend,
        ),
      ),
    );
  }

  ///Send to email if forgot password
  Future<void> _onSendMail(
    AuthMailSend event,
    Emitter<AuthState> emit,
  ) async {
    emit(AuthBusy(state.data));
    try {
      final email = event.email ?? '';

      if (email.isEmpty) {
        throw Exception('Invalid email format. Please try again.');
      }

      if (!EmailValidator.validate(email)) {
        throw Exception('Invalid email format. Please try again.');
      }

      bool value = await _authApi.resetPassword(email);

      if (!value) {
        throw Exception('Can not send request to email');
      }

      emit(AuthMailSendSuccess(state.data.copyWith(isResend: event.isResend)));
    } catch (e) {
      if (e is SocketException) {
        emit(
          AuthMailSendFailure(
              data: state.data, error: 'No internet connection'),
        );
        return;
      }

      emit(
        AuthMailSendFailure(
          error: e.toString().replaceAll('Exception:', '').trim(),
          data: state.data,
        ),
      );
    }
  }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
