Map<String, dynamic> templateMocData = {
  "jsonrpc": "2.0",
  "id": null,
  "result": [
    {
      "id": "1",
      "group": "ChemTestingWeekly",
      "name": "Free Chlorine",
      "unit": "ppm",
      "input_type": "number",
      "default": "2",
      "domain": ["0", "0.5", "8"],
      "constraints": "{1} <= {30}"
    },
    {
      "id": "2",
      "group": "ChemTestingWeekly",
      "name": "pH",
      "unit": "",
      "input_type": "number",
      "default": "7.5",
      "domain": ["7", "0.1", "8"],
      "constraints": null
    },
    {
      "id": "3",
      "group": "ChemTestingWeekly",
      "name": "Total Alkalinity",
      "unit": "ppm",
      "input_type": "number",
      "default": "130",
      "domain": ["0", "10", "180"],
      "constraints": null
    },
    {
      "id": "4",
      "group": "ChemTestingWeekly",
      "name": "Salt (if applicable)",
      "unit": "ppm",
      "input_type": "number",
      "default": "3200",
      "domain": ["0", "100", "6000"],
      "constraints": null
    },
    {
      "id": "5",
      "group": "ChemTestingMonthly",
      "name": "Calcium Hardness",
      "unit": "ppm",
      "input_type": "number",
      "default": "200",
      "domain": ["0", "10", "800"],
      "constraints": null
    },
    {
      "id": "6",
      "group": "ChemTestingMonthly",
      "name": "Chlorine Stabilizer",
      "unit": "ppm",
      "input_type": "number",
      "default": "40",
      "domain": ["0", "10", "300"],
      "constraints": null
    },
    {
      "id": "7",
      "group": "ChemTestingMonthly",
      "name": "Total Dissolved Solids",
      "unit": "ppm",
      "input_type": "number",
      "default": "600",
      "domain": ["200", "50", "1500"],
      "constraints": null
    },
    {
      "id": "8",
      "group": "ChemicalsAdded",
      "name": "Granular Chlorine",
      "unit": "pounds",
      "input_type": "number",
      "default": "1",
      "domain": ["0", "0.5", "10"],
      "constraints": null
    },
    {
      "id": "9",
      "group": "ChemicalsAdded",
      "name": "Tri-Chlor (3 inch tablets)",
      "unit": "tablets",
      "input_type": "number",
      "default": "1",
      "domain": ["0", "1", "15"],
      "constraints": null
    },
    {
      "id": "10",
      "group": "ChemicalsAdded",
      "name": "Liquid Chlorine",
      "unit": "gallons",
      "input_type": "number",
      "default": "0.25",
      "domain": ["0", "0.25", "20"],
      "constraints": null
    },
    {
      "id": "11",
      "group": "ChemicalsAdded",
      "name": "Algecide",
      "unit": "ounces",
      "input_type": "number",
      "default": "1",
      "domain": ["0", "1", "16"],
      "constraints": null
    },
    {
      "id": "12",
      "group": "ChemicalsAdded",
      "name": "Muriatic Acid",
      "unit": "gallons",
      "input_type": "number",
      "default": "0.25",
      "domain": ["0", "0.0625", "4"],
      "constraints": null
    },
    {
      "id": "13",
      "group": "ChemicalsAdded",
      "name": "Sodium Bicarbonate",
      "unit": "pounds",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "1.2", "50"],
      "constraints": null
    },
    {
      "id": "14",
      "group": "ChemTestingMonthly",
      "name": "Water Temperature",
      "unit": "degrees",
      "input_type": "number",
      "default": "75",
      "domain": ["32", "1", "110"],
      "constraints": null
    },
    {
      "id": "15",
      "group": "CirculationMonthly",
      "name": "Backwash filter",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "16",
      "group": "CirculationWeekly",
      "name": "Filter Pressure Upon Leaving",
      "unit": "psi",
      "input_type": "number",
      "default": "12",
      "domain": ["0", "1", "45"],
      "constraints": null
    },
    {
      "id": "17",
      "group": "CirculationWeekly",
      "name": "Verify Circulation is Running",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "18",
      "group": "CirculationWeekly",
      "name": "Clean Skimmer Baskets",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "19",
      "group": "CirculationWeekly",
      "name": "Clean Pump Basket",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "20",
      "group": "Cleaning",
      "name": "Net Surface (Full Service)",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "21",
      "group": "Cleaning",
      "name": "Brush Walls",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "22",
      "group": "Cleaning",
      "name": "Brush Steps and Spa",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "23",
      "group": "Cleaning",
      "name": "Hose / Leaf Vac.(Full Service)",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "24",
      "group": "PoolSweep",
      "name": "Check Operation",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "25",
      "group": "PoolSweep",
      "name": "Clean Bag",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "26",
      "group": "PoolSweep",
      "name": "Clean Strainer / E-filter",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "27",
      "group": "PoolSweep",
      "name": "Check / Clean Wall Screen",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "28",
      "group": "ChemicalsAdded",
      "name": "Salt added (if applicable)",
      "unit": "bags",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "0.5", "5"],
      "constraints": null
    },
    {
      "id": "29",
      "group": "Cleaning",
      "name": "Filter Cleaning",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "30",
      "group": "ChemTestingWeekly",
      "name": "Total Chlorine",
      "unit": "ppm",
      "input_type": "number",
      "default": "2",
      "domain": ["0", "0.5", "8"],
      "constraints": null
    },
    {
      "id": "31",
      "group": "ChemTestingWeekly",
      "name": "Chloramines",
      "unit": "ppm",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "1", "10"],
      "constraints": null
    },
    {
      "id": "32",
      "group": "Cleaning",
      "name": "Filter Type",
      "unit": null,
      "input_type": "selection",
      "default": "Not Set",
      "domain": ["Not Set", "Cartridge", "DE"],
      "constraints": null
    },
    {
      "id": "33",
      "group": "Cleaning",
      "name": "Salt Cell Cleaned",
      "unit": "bool",
      "input_type": "checkbox",
      "default": false,
      "domain": [false, true],
      "constraints": null
    },
    {
      "id": "34",
      "group": "Cleaning",
      "name": "Salt Generator Percent",
      "unit": "pct",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "5", "100"],
      "constraints": null
    },
    {
      "id": "35",
      "group": "ChemTestingWeekly",
      "name": "Phosphates",
      "unit": "ppb",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "100", "2500"],
      "constraints": null
    },
    {
      "id": "36",
      "group": "ChemicalsAdded",
      "name": "Calcium",
      "unit": "pounds",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "1", "8"],
      "constraints": null
    },
    {
      "id": "37",
      "group": "ChemicalsAdded",
      "name": "Cyanuric Acid",
      "unit": "pounds",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "0.5", "10"],
      "constraints": null
    },
    {
      "id": "38",
      "group": "ChemicalsAdded",
      "name": "Metal control",
      "unit": "ounces",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "1", "128"],
      "constraints": null
    },
    {
      "id": "39",
      "group": "ChemicalsAdded",
      "name": "Phosphate Remover",
      "unit": "ounces",
      "input_type": "number",
      "default": "0",
      "domain": ["0", "1", "128"],
      "constraints": null
    },
    {
      "id": "40",
      "group": "ChemTestingWeekly",
      "name": "Chloramines",
      "unit": "ppm",
      "input_type": "calculation",
      "default": "0",
      "domain": ["{1} * 1.5"],
      "constraints": null
    }
  ]
};
