part of 'auth_bloc.dart';

@immutable
abstract class AuthEvent {}

class AuthLoaded extends AuthEvent {}

class AuthSignedIn extends AuthEvent {
  final String userName;
  final String password;

  AuthSignedIn(this.userName, this.password);
}

class AuthLogout extends AuthEvent {}

class AuthLoginDisabled extends AuthEvent {
  final bool isDisabled;

  AuthLoginDisabled(this.isDisabled);
}

class AuthEmailDisabled extends AuthEvent {
  final bool isDisabled;

  AuthEmailDisabled(this.isDisabled);
}

class AuthSendUpdate extends AuthEvent {
  final bool isResend;

  AuthSendUpdate(this.isResend);
}

class AuthMailSend extends AuthEvent {
  AuthMailSend({this.isResend = false, this.email});

  final String? email;
  final bool isResend;
}

class AuthLogoutChecked extends AuthEvent {}
