part of 'auth_bloc.dart';

class AuthData {
  final String? requiredUserName;
  final bool isResend;
  final bool isDisableLogin;
  final DateTime? jobDateStart;
  final Job? job;
  final bool isDisableEmail;
  final UserInfo? userInfo;
  final String? userName;
  final WorkLog? workLog;

  const AuthData(
      {this.requiredUserName,
      this.isResend = false,
      this.isDisableLogin = false,
      this.isDisableEmail = false,
      this.jobDateStart,
      this.job,
      this.userInfo,
      this.userName,
      this.workLog});

  AuthData copyWith(
      {String? requiredUserName,
      bool? isResend,
      bool? isDisableLogin,
      bool? isDisableEmail,
      DateTime? jobDateStart,
      Job? job,
      UserInfo? userInfo,
      String? userName,
      WorkLog? workLog}) {
    return AuthData(
      userName: userName ?? this.userName,
      requiredUserName: requiredUserName ?? this.requiredUserName,
      isResend: isResend ?? this.isResend,
      isDisableLogin: isDisableLogin ?? this.isDisableLogin,
      jobDateStart: jobDateStart ?? this.jobDateStart,
      job: job ?? this.job,
      isDisableEmail: isDisableEmail ?? this.isDisableEmail,
      userInfo: userInfo ?? this.userInfo,
      workLog: workLog ?? this.workLog
    );
  }
}

@immutable
abstract class AuthState {
  final AuthData data;

  const AuthState(this.data);
}

class AuthLoading extends AuthState {
  const AuthLoading(AuthData data) : super(data);
}

class AuthBusy extends AuthState {
  const AuthBusy(AuthData data) : super(data);
}

class AuthLogoutSuccess extends AuthState {
  const AuthLogoutSuccess(AuthData data) : super(data);
}

class AuthAlreadySignIn extends AuthState {
  const AuthAlreadySignIn(AuthData data) : super(data);
}

class AuthNotSignIn extends AuthState {
  const AuthNotSignIn(AuthData data) : super(data);
}

class AuthSignInSuccess extends AuthState {
  const AuthSignInSuccess(AuthData data) : super(data);
}

class AuthUpdateSuccess extends AuthState {
  const AuthUpdateSuccess(AuthData data) : super(data);
}

class AuthSignInFailure extends AuthState {
  const AuthSignInFailure({required this.error, required AuthData data})
      : super(data);

  final String error;
}

class AuthMailSendSuccess extends AuthState {
  const AuthMailSendSuccess(AuthData data) : super(data);
}

class AuthMailSendFailure extends AuthState {
  const AuthMailSendFailure({required this.error, required AuthData data})
      : super(data);

  final String error;
}

class AuthJobStartSuccess extends AuthState {
  const AuthJobStartSuccess(AuthData data) : super(data);
}

class AuthWorkLogStartSuccess extends AuthState {
  const AuthWorkLogStartSuccess(AuthData data) : super(data);
}

