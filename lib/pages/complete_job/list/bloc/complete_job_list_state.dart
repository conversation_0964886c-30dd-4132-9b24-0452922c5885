part of 'complete_job_list_bloc.dart';

class CompleteJobListState {
  const CompleteJobListState({
    this.status = CompleteJobStatus.load,
    this.errorMessage = 'N/a',
    this.jobs = const [],
    required this.selectedDateTimeRange,
    required this.dateTimeRangeList,
    this.detailJob,
    this.errorTitle,
  });

  CompleteJobListState copyWith({
    String? errorMessage,
    CompleteJobStatus? status,
    List<Job>? jobs,
    DateTimeRange? selectedDateTimeRange,
    List<DateTimeRange>? dateTimeRangeList,
    Job? detailJob,
    String? errorTitle,
  }) {
    return CompleteJobListState(
      errorTitle: errorTitle ?? this.errorTitle,
      dateTimeRangeList: dateTimeRangeList ?? this.dateTimeRangeList,
      selectedDateTimeRange:
          selectedDateTimeRange ?? this.selectedDateTimeRange,
      jobs: jobs ?? this.jobs,
      errorMessage: errorMessage ?? this.errorMessage,
      status: status ?? this.status,
      detailJob: detailJob ?? this.detailJob,
    );
  }

  final CompleteJobStatus status;
  final String errorMessage;
  final List<Job> jobs;
  final DateTimeRange selectedDateTimeRange;
  final List<DateTimeRange> dateTimeRangeList;
  final Job? detailJob;
  final String? errorTitle;
}

enum CompleteJobStatus {
  loading,
  load,
  payrollFailure,
  actionFailure,
  actionSuccess,
}
