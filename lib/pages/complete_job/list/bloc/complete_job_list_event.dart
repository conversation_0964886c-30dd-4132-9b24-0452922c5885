part of 'complete_job_list_bloc.dart';

@immutable
abstract class CompleteJobListEvent {}

class CompleteJobListLoaded extends CompleteJobListEvent {}
class CompleteJobListLoadMoreRequested extends CompleteJobListEvent {}
class CompleteJobListDateTimeChanged extends CompleteJobListEvent {
  CompleteJobListDateTimeChanged({required this.dateTimeRange});

  final DateTimeRange dateTimeRange;
}

class CompleteJobDetailLoaded extends CompleteJobListEvent {
  CompleteJobDetailLoaded(this.job);

  final Job job;
}

class CompleteJobCommentPosted extends CompleteJobListEvent {
  CompleteJobCommentPosted(this.comment, this.job);

  final String comment;
  final Job job;
}

class CompleteJobCommentDeleted extends CompleteJobListEvent {
  final JobComment comment;
  final Job job;

  CompleteJobCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class CompleteJobCommentUpdated extends CompleteJobListEvent {
  final JobComment comment;

  final String body;
  final Job job;

  CompleteJobCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}


class CompleteJobCustomerCommentPosted extends CompleteJobListEvent {
  CompleteJobCustomerCommentPosted(this.comment, this.job);

  final String comment;
  final Job job;
}

class CompleteJobCustomerCommentDeleted extends CompleteJobListEvent {
  final JobComment comment;
  final Job job;

  CompleteJobCustomerCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class CompleteJobCustomerCommentUpdated extends CompleteJobListEvent {
  final JobComment comment;

  final String body;
  final Job job;

  CompleteJobCustomerCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}
