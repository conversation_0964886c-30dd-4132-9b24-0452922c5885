import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/dialog_service/dialog_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/services/loop_service/loop_service.dart';
import 'package:ticking_app/services/network_service/network_service.dart';
import 'package:ticking_app/services/task_service/model/job_create_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_delete_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_update_comment_task.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_range.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_until.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/uuid_util.dart';

part 'complete_job_list_event.dart';

part 'complete_job_list_state.dart';

class CompleteJobListBloc
    extends Bloc<CompleteJobListEvent, CompleteJobListState> {
  CompleteJobListBloc(
      {SecureConfigService? secureConfigService,
      JobApi? jobApi,
      TickingLogService? logService,
      LocationService? locationService,
      CacheService? cacheService,
      NetworkService? networkService,
      LoopService? loopService,
      TaskService? taskService,
      DialogService? dialogService})
      : super(
          CompleteJobListState(
            dateTimeRangeList: [
              getTimeRangeLast3Day(),
              getTimeRangeLast7Day(),
              getTimeRangeLast30Day(),
              getTimeRangeLast120Day(),
            ],
            selectedDateTimeRange: getTimeRangeLast3Day(),
          ),
        ) {
    ///Get services
    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _locationService = locationService ?? GetIt.I<LocationService>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _networkService = networkService ?? GetIt.I<NetworkService>();
    _loopService = loopService ?? GetIt.I<LoopService>();
    // _lockService = lockService ?? GetIt.I<LockService>();
    _tickingLogService.setClassName(toString());
    _dialogService = dialogService ?? GetIt.I<DialogService>();
    on<CompleteJobListLoaded>(_onLoaded);
    on<CompleteJobListLoadMoreRequested>(_onLoadMoreRequested);
    on<CompleteJobListDateTimeChanged>(_onDateTimeChanged);
    on<CompleteJobDetailLoaded>(_onDetailLoaded);
    on<CompleteJobCommentPosted>(_onCommentPosted);
    on<CompleteJobCommentDeleted>(_onCommentDeleted);
    on<CompleteJobCommentUpdated>(_onCommentUpdated);
    on<CompleteJobCustomerCommentPosted>(_onCustomerCommentPosted);
    on<CompleteJobCustomerCommentDeleted>(_onCustomerCommentDeleted);
    on<CompleteJobCustomerCommentUpdated>(_onCustomerCommentUpdated);
  }

  late final JobApi _jobApi;
  late final TaskService _taskService;
  late final LoopService _loopService;
  late SecureConfigService _secureConfigService;
  late LocationService _locationService;
  late TickingLogService _tickingLogService;
  late CacheService _cacheService;
  late NetworkService _networkService;
  late final DialogService _dialogService;

  Future<void> _onLoadMoreRequested(
    CompleteJobListLoadMoreRequested event,
    Emitter<CompleteJobListState> emit,
  ) async {}

  ///Load data event
  Future<void> _onLoaded(
    CompleteJobListLoaded event,
    Emitter<CompleteJobListState> emit,
  ) async {
    try {
      emit(state.copyWith(
        status: CompleteJobStatus.loading,
      ));
      final dateTime = state.selectedDateTimeRange;
      final request = JobListRequest(
        gteScheduleDate: dateTime.startDate,
        lteScheduleDate: dateTime.endDate,
        status: 'complete',
        limit: 500,
        isToIso8601String: true,
      );
      final result = await _jobApi.getJobs(request);
      emit(
        state.copyWith(
          jobs: result.items,
          status: CompleteJobStatus.load,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException) {
        return;
      }
      if (e is ApiBadRequestException &&
          e.error?.code == 'JOB-0010' &&
          e.statusCode == 400) {
        String message = e.error?.message ?? '';
        if (message.isNotEmpty) {
          final Map map = jsonDecode(message);

          emit(state.copyWith(
            errorTitle: map['title'] ?? '',
            errorMessage: map['message'] ?? 'No Data',
            status: CompleteJobStatus.payrollFailure,
          ));

          return;
        }
      }
      emit(state.copyWith(
        errorMessage: e.toString().replaceAll('Exception:', '').trim(),
        status: CompleteJobStatus.actionFailure,
      ));

      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }

      _tickingLogService.error('CompleteJobListLoaded', e.toString(), stack);
    }
  }

  Future<void> _onDateTimeChanged(
    CompleteJobListDateTimeChanged event,
    Emitter<CompleteJobListState> emit,
  ) async {
    emit(state.copyWith(selectedDateTimeRange: event.dateTimeRange));
    add(CompleteJobListLoaded());
  }

  Future<void> _onCommentPosted(
    CompleteJobCommentPosted event,
    Emitter<CompleteJobListState> emit,
  ) async {
    ///Get jobs by cache

    final jobModels = _cacheService.getJobList;

    Job job = event.job;
    final jobIndex =
        jobModels.indexWhere((element) => element.job.id == job.id);

    ///Get jobs by state
    List<Job> jobs = List.from(state.jobs);
    int index = jobs.indexWhere((element) => element.id == job.id);

    try {
      final result = await _jobApi.createJobComment(
        jobId: job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final comments = List<JobComment>.from(job.comments);

      comments.insert(0, result.data);
      job = job.copyWith(comments: comments);

      if (jobIndex > 0) {
        jobModels[jobIndex] = jobModels[jobIndex].copyWith(
          job: job,
        );

        await _cacheService.updateJobModels(jobModels);
      }
      if (index != -1) {
        jobs[index] = job;
      }
      emit(
        state.copyWith(
          detailJob: job,
          jobs: jobs,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(body: event.comment, date: now),
            jobId: job.id,
          ),
          type: TaskType.createJobComment,
        );

        final comments = List<JobComment>.from(job.comments);
        comments.insert(
          0,
          JobComment(
            date: DateTime.now(),
            jobId: job.id,
            employee: job.employee,
            body: event.comment,
            id: taskId,
          ),
        );

        job = job.copyWith(comments: comments);

        if (jobIndex > 0) {
          jobModels[jobIndex] = jobModels[jobIndex].copyWith(job: job);

          await _cacheService.updateJobModels(jobModels);
        }

        if (index != -1) {
          jobs[index] = job;
        }
        emit(
          state.copyWith(
            detailJob: job,
            jobs: jobs,
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(state.copyWith(
        errorMessage: e.toString().replaceAll('Exception:', '').trim(),
        status: CompleteJobStatus.actionFailure,
      ));
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCommentDeleted(
    CompleteJobCommentDeleted event,
    Emitter<CompleteJobListState> emit,
  ) async {
    final job = event.job;
    final JobComment comment = event.comment;
    try {
      if (!event.comment.id.contains('commentTask')) {
        await _jobApi.deleteJobComment(
          jobId: job.id,
          dateTime: DateTime.now(),
          commentId: comment.id,
        );
      }

      await _taskService.removeCommentJobTask(job.id, comment.id);

      job.comments.remove(comment);

      emit(
        state.copyWith(
          detailJob: job,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(
          job.id,
          comment.id,
        );

        job.comments.remove(comment);

        emit(
          state.copyWith(
            detailJob: job,
          ),
        );

        if (comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: comment.id,
            dateTime: now,
            jobId: job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        state.copyWith(
          errorMessage: e.toString().replaceAll('Exception:', '').trim(),
          status: CompleteJobStatus.actionFailure,
        ),
      );
      _tickingLogService.error('_onCommentDeleted', e.toString(), stack);
    }
  }

  Future<void> _onCommentUpdated(
    CompleteJobCommentUpdated event,
    Emitter<CompleteJobListState> emit,
  ) async {
    final job = event.job;
    JobComment comment = event.comment;
    final commentIndex = job.comments.indexWhere(
      (element) => element == comment,
    );
    try {
      ApiResult<JobComment> commentResult;
      if (!comment.id.contains('commentTask')) {
        commentResult = await _jobApi.updateJobComment(
          jobId: job.id,
          commentId: comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      } else {
        commentResult = await _jobApi.createJobComment(
          jobId: job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      }

      comment = commentResult.data;

      if (commentIndex >= 0) {
        job.comments[commentIndex] = job.comments[commentIndex].copyWith(
          body: event.body,
          id: comment.id,
        );
      }

      emit(
        state.copyWith(
          detailJob: job,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(job.id, comment.id);
        late DataTask task;

        if (comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: job.id,
            ),
            type: TaskType.createJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] = job.comments[commentIndex]
                .copyWith(body: event.body, id: taskId);
          }
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] =
                job.comments[commentIndex].copyWith(body: event.body);
          }
        }

        emit(
          state.copyWith(
            detailJob: job,
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }
      emit(
        state.copyWith(
          errorMessage: e.toString().replaceAll('Exception:', '').trim(),
          status: CompleteJobStatus.actionFailure,
        ),
      );

      _tickingLogService.error('_onCommentUpdated', e.toString(), stack);
    }
  }

  Future<void> _onDetailLoaded(
    CompleteJobDetailLoaded event,
    Emitter<CompleteJobListState> emit,
  ) async {
    final List<Job> jobs = List.from(state.jobs);
    int index = jobs.indexWhere(
      (element) => element.id == event.job.id,
    );
    if (index != -1) {
      jobs[index] = event.job;
    }

    final result = await _jobApi.createJobCustomerComment(jobId: event.job.id);

    if (result != null) {
      final list = List.from(result as List)
          .map((e) => JobComment.fromJson(Map<String, dynamic>.from(e)))
          .toList();
      event.job.customerComments = list;
    }

    emit(state.copyWith(detailJob: event.job, jobs: jobs));
  }

  @override
  Future<void> close() {
    return super.close();
  }

  Future<void> _onCustomerCommentPosted(
    CompleteJobCustomerCommentPosted event,
    Emitter<CompleteJobListState> emit,
  ) async {
    ///Get jobs by cache

    final jobModels = _cacheService.getJobList;

    Job job = event.job;
    final jobIndex =
        jobModels.indexWhere((element) => element.job.id == job.id);

    ///Get jobs by state
    List<Job> jobs = List.from(state.jobs);
    int index = jobs.indexWhere((element) => element.id == job.id);

    try {
      final result = await _jobApi.postJobCustomerComment(
        jobId: job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final comments = List<JobComment>.from(job.customerComments);

      comments.insert(0, result.data);
      job = job.copyWith(customerComments: comments);

      if (jobIndex > 0) {
        jobModels[jobIndex] = jobModels[jobIndex].copyWith(
          job: job,
        );

        await _cacheService.updateJobModels(jobModels);
      }
      if (index != -1) {
        jobs[index] = job;
      }
      emit(
        state.copyWith(
          detailJob: job,
          jobs: jobs,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(body: event.comment, date: now),
            jobId: job.id,
          ),
          type: TaskType.createJobComment,
        );

        final comments = List<JobComment>.from(job.comments);
        comments.insert(
          0,
          JobComment(
            date: DateTime.now(),
            jobId: job.id,
            employee: job.employee,
            body: event.comment,
            id: taskId,
          ),
        );

        job = job.copyWith(comments: comments);

        if (jobIndex > 0) {
          jobModels[jobIndex] = jobModels[jobIndex].copyWith(job: job);

          await _cacheService.updateJobModels(jobModels);
        }

        if (index != -1) {
          jobs[index] = job;
        }
        emit(
          state.copyWith(
            detailJob: job,
            jobs: jobs,
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(state.copyWith(
        errorMessage: e.toString().replaceAll('Exception:', '').trim(),
        status: CompleteJobStatus.actionFailure,
      ));
      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentDeleted(
    CompleteJobCustomerCommentDeleted event,
    Emitter<CompleteJobListState> emit,
  ) async {
    final job = event.job;
    final JobComment comment = event.comment;
    try {
      if (!event.comment.id.contains('commentTask')) {
        await _jobApi.deleteJobCustomerComment(
          jobId: job.id,
          dateTime: DateTime.now(),
          commentId: comment.id,
        );
      }

      await _taskService.removeCommentJobTask(job.id, comment.id);

      job.customerComments.remove(comment);

      emit(
        state.copyWith(
          detailJob: job,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(
          job.id,
          comment.id,
        );

        job.comments.remove(comment);

        emit(
          state.copyWith(
            detailJob: job,
          ),
        );

        if (comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: comment.id,
            dateTime: now,
            jobId: job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        state.copyWith(
          errorMessage: e.toString().replaceAll('Exception:', '').trim(),
          status: CompleteJobStatus.actionFailure,
        ),
      );
      _tickingLogService.error('_onCommentDeleted', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentUpdated(
    CompleteJobCustomerCommentUpdated event,
    Emitter<CompleteJobListState> emit,
  ) async {
    final job = event.job;
    JobComment comment = event.comment;
    final commentIndex = job.customerComments.indexWhere(
      (element) => element == comment,
    );
    try {
      ApiResult<JobComment> commentResult;
      if (!comment.id.contains('commentTask')) {
        commentResult = await _jobApi.updateJobCustomerComment(
          jobId: job.id,
          commentId: comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      } else {
        commentResult = await _jobApi.postJobCustomerComment(
          jobId: job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      }

      comment = commentResult.data;

      if (commentIndex >= 0) {
        job.customerComments[commentIndex] =
            job.customerComments[commentIndex].copyWith(
          body: event.body,
          id: comment.id,
        );
      }

      emit(
        state.copyWith(
          detailJob: job,
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(job.id, comment.id);
        late DataTask task;

        if (comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: job.id,
            ),
            type: TaskType.createJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] = job.comments[commentIndex]
                .copyWith(body: event.body, id: taskId);
          }
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] =
                job.comments[commentIndex].copyWith(body: event.body);
          }
        }

        emit(
          state.copyWith(
            detailJob: job,
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }
      emit(
        state.copyWith(
          errorMessage: e.toString().replaceAll('Exception:', '').trim(),
          status: CompleteJobStatus.actionFailure,
        ),
      );

      _tickingLogService.error('_onCommentUpdated', e.toString(), stack);
    }
  }
}
