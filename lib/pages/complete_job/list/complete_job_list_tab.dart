import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/complete_job/complete_job_detail_bs.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_until.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_range.dart' as date;
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';
import 'package:ticking_app/widgets/drop_down/app_drop_down.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

import 'bloc/complete_job_list_bloc.dart';

class CompleteJobListTab extends StatefulWidget {
  const CompleteJobListTab({Key? key}) : super(key: key);

  @override
  _CompleteJobListTabState createState() => _CompleteJobListTabState();
}

class _CompleteJobListTabState extends State<CompleteJobListTab> {
  final CompleteJobListBloc _bloc = CompleteJobListBloc();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _bloc.close();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    _bloc.add(CompleteJobListLoaded());
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        _bloc.add(CompleteJobListLoadMoreRequested());
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: Scaffold(
        appBar: _buildAppbar(),
        backgroundColor: AppColors.grey4,
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    return BlocConsumer<CompleteJobListBloc, CompleteJobListState>(
        listener: (context, state) {
          if (state.status == CompleteJobStatus.actionFailure) {
            DialogHelper.showError(content: state.errorMessage);
          }
        },
        listenWhen: (previous, current) => previous.status != current.status,
        buildWhen: (previous, current) =>
            previous.dateTimeRangeList != current.dateTimeRangeList ||
            previous.status != current.status ||
            previous.selectedDateTimeRange != current.selectedDateTimeRange ||
            previous.jobs != current.jobs ||
            previous.errorTitle != current.errorTitle ||
            previous.errorMessage != current.errorMessage,
        builder: (context, state) {
          if (state.status == CompleteJobStatus.loading) {
            return const LoadingIndicator();
          } else if (state.status == CompleteJobStatus.payrollFailure) {
            return Center(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icon.illustrations.svg(),
                  Text(
                    state.errorTitle ?? '',
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(0xFFF2002C),
                          fontWeight: FontWeight.w900,
                          fontSize: 18,
                        ),
                  ).pb(value: 8).pt(value: 16),
                  Text(
                    state.errorMessage,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(0xFF798287),
                          fontWeight: FontWeight.w500,
                          fontSize: 14,
                        ),
                  ).pr(value: 16).pl(value: 16),
                ],
              ),
            );
          }
          List<date.DateTimeRange> dateTimeRangeList = state.dateTimeRangeList;
          int index = dateTimeRangeList.indexWhere(
            (element) => element.name == state.selectedDateTimeRange.name,
          );
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16),
                child: AppDropDown(
                  color: AppColors.grey11,
                  groupNameList:
                      dateTimeRangeList.map((e) => e.name ?? '').toList(),
                  textColor: AppColors.black1,
                  headerText: 'In ',
                  iconColor: AppColors.grey12,
                  onSelectedItem: (index) {
                    _bloc.add(CompleteJobListDateTimeChanged(
                      dateTimeRange: dateTimeRangeList[index],
                    ));
                  },
                  index: index,
                ),
              ),
              Container(
                // color: AppColors.grey4,
                // padding: const EdgeInsets.symmetric(horizontal: Dimens.pad),
                child: Text(
                  'Done on',
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: AppColors.grey,
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        // height:
                      ),
                ).px16().objectTopLeft().pb8().pt20(),
              ),
              Expanded(
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    Job job = state.jobs[index];
                    return InkWell(
                      onTap: () {
                        _bloc.add(
                          CompleteJobDetailLoaded(
                            job,
                          ),
                        );
                        state.detailJob;
                        showCompleteJobDetailBS(
                          context: context,
                          isDoneButtonEnable: false,
                          job: job,
                          group: '',
                          // smsTemplate: '',
                          isStart: false,
                          // JobDetailsRepairPosted
                          // onRepairPosted: (Job job, String description) {
                          //   BlocProvider.of<JobListBloc>(context).add(
                          //     JobRepairPosted(
                          //       job: job,
                          //       description: description,
                          //     ),
                          //   );
                          // },
                          onCommentPosted: (String comment, Job job) {
                            _bloc.add(
                              CompleteJobCommentPosted(
                                comment,
                                job,
                              ),
                            );
                          },
                          onUnableToAccess: () {},
                          onCommentDeleted: (JobComment comment, Job job) {
                            _bloc.add(
                              CompleteJobCommentDeleted(
                                comment: comment,
                                job: job,
                              ),
                            );
                          },
                          onCommentEdit: (
                            JobComment comment,
                            Job job,
                            String body,
                          ) {
                            _bloc.add(CompleteJobCommentUpdated(
                              job: job,
                              comment: comment,
                              body: body,
                            ));
                          },
                          completeJobListBloc: _bloc,
                        );
                      },
                      child: _JobWidget(
                        job: job,
                      ).pb(value: 20),
                    );
                  },
                  itemCount: state.jobs.length,
                ),
              ),
            ],
          );
        });
  }

  PreferredSizeWidget _buildAppbar() {
    return AppBar(
      title: Text(
        'Completed Jobs',
        style: Theme.of(context)
            .textTheme
            .headline6
            ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
      ),
      centerTitle: true,
      backgroundColor: AppColors.grey4,
      leading: CircleButton(
        child: Assets.icon.refreshEnable.svg(
          color: Theme.of(context).primaryColor,
        ),
        onPressed: () {
          _bloc.add(CompleteJobListLoaded());
        },
        size: Dimens.ic_XL,
      ),
    );
  }

  Widget _buildRefreshWidget({required Widget child}) {
    return RefreshIndicator(
      onRefresh: () async {
        // _jobListBloc.add(JobListRefreshed());
      },
      child: child,
    );
  }

  ///Build empty job list
  Widget _buildEmpty() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container().pt(value: 50),
        Gaps.vGap50,
        Text(
          'No selected jobs.\n'
          'Please go to "Assigned Jobs" to select jobs.',
          style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey,
                fontWeight: FontWeight.w600,
              ),
          textAlign: TextAlign.center,
        ).centered(),
      ],
    );
  }
}

class _JobWidget extends StatelessWidget {
  final Job job;

  const _JobWidget({
    Key? key,
    required this.job,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 65,
          child: Column(
            children: [
              Text(
                job.completeDate != null
                    ? (DateFormat.MMMM()
                            .format(
                              job.completeDate!,
                            )
                            .substring(0, 3) +
                        ' ' +
                        job.completeDate!.day.toString())
                    : '',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
              ),
              Text(
                job.completeDate != null
                    ? job.completeDate!.year.toString()
                    : '',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyText2?.copyWith(
                      color: AppColors.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
              ).pt(value: 8),
            ],
          ),
        ),
        Gaps.hGap16,
        InkWell(
          child: Container(
            color: Theme.of(context).canvasColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  color: Theme.of(context).primaryColor,
                  height: 20,
                  width: 3,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      job.contact.name ?? '',
                      style: Theme.of(context).textTheme.bodyText1?.copyWith(
                            fontWeight: FontWeight.w800,
                            overflow: TextOverflow.ellipsis,
                            fontSize: Dimens.text,
                            color: Colors.black,
                          ),
                      maxLines: 1,
                    ),
                    Gaps.vGap6,
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            job.contact.getFullLocation(),
                            style:
                                Theme.of(context).textTheme.bodyText2?.copyWith(
                                      color: AppColors.grey10,
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: Dimens.text,
                                      fontWeight: FontWeight.w500,
                                      height: 1.5,
                                    ),
                            maxLines: 3,
                          ),
                        ),
                      ],
                    ),
                    if (job.details.specialRequests != null)
                      Text(
                        job.details.specialRequests ?? "",
                        style: Theme.of(context).textTheme.bodyText2?.copyWith(
                            color: AppColors.specialColor,
                            overflow: TextOverflow.ellipsis,
                            fontSize: Dimens.text,
                            fontWeight: FontWeight.w400,
                            height: 1.5),
                        maxLines: 10,
                      ).pt(value: 5),
                    Wrap(
                      crossAxisAlignment: WrapCrossAlignment.start,
                      alignment: WrapAlignment.start,
                      runAlignment: WrapAlignment.start,
                      spacing: Dimens.gap_dp8,
                      runSpacing: Dimens.gap_dp8,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                              color: AppColors.neutral1Color,
                              borderRadius:
                                  BorderRadius.circular(Dimens.rad_XXXXL)),
                          child: job.details.serviceType.text
                              .color(Theme.of(context).primaryColor)
                              .make()
                              .py8()
                              .px16(),
                        ),

                        if (job.contact.isFiberglassPool == true)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.neutral1Color,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: "Fiber Glass"
                                .text
                                .color(Theme.of(context).primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),
                        if (job.details.serviceLevel != null)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.neutral1Color,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: job.details.serviceLevel?.text
                                .color(Theme.of(context).primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),


                        if (job.contact.filterType != null &&
                            job.contact.filterType != "")
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.neutral1Color,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: (job.contact.filterType ?? "")
                                .text
                                .color(Theme.of(context).primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),

                        if (job.details.isLightningService == true)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.neutral1Color,
                                borderRadius:
                                BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: 'Lightning Service'
                                .text
                                .color(AppColors.primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),

                        if (job.details.estimatedTime != null)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.grey13,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: 'Estimate: ${printDuration(
                              Duration(
                                seconds: job.details.estimatedTime!,
                              ),
                            )}'
                                .text
                                .color(AppColors.grey12)
                                .make()
                                .py8()
                                .px16(),
                          ),
                        if (job.contact.isSaltWater == true)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.neutral1Color,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: "Salt Water"
                                .text
                                .color(Theme.of(context).primaryColor)
                                .make()
                                .py8()
                                .px16(),
                          ),

                        if (job.details.specialRequests != null)
                          Container(
                            decoration: BoxDecoration(
                                color: AppColors.specialBox,
                                borderRadius:
                                    BorderRadius.circular(Dimens.rad_XXXXL)),
                            child: 'Special Requests'
                                .text
                                .color(AppColors.specialColor)
                                .make()
                                .py8()
                                .px16(),
                          ),

                        // Btn(
                        //   onPressed: null,
                        //   style: AppButtonStyle.filterCleanStyle(context),
                        //   text: widget.job.details.serviceType,
                        // ),
                        // Btn(
                        //   onPressed: null,
                        //   style: widget.group == 'Overdue'
                        //       ? AppButtonStyle.overDueStyle(context)
                        //       : AppButtonStyle.filterCleanStyle(context).copyWith(
                        //           foregroundColor: MaterialStateProperty.resolveWith<Color>(
                        //               (Set<MaterialState> states) => AppColors.primaryColor2)),
                        //   text: 'Due ${TimeUtils.dateToStr(widget.job.scheduleDate)}',
                        // )
                      ],
                    ).pt(value: 8)
                  ],
                ).p(16).expand()
              ],
            ),
          ).cornerRadius(Dimens.rad_XXL),
        ).expand()
      ],
    ).px16();
  }
}
