import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:simple_connection_checker/simple_connection_checker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/app_single_ton.dart';
import 'package:ticking_app/core/arguments/job_detail_arg.dart';
import 'package:ticking_app/core/arguments/job_repair_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/model/service_type.dart';
import 'package:ticking_app/pages/complete_job/list/bloc/complete_job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/access_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/account_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/description_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/request_bs.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/special_request_tab.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/ticket_tab.dart';
import 'package:ticking_app/pages/home/<USER>/share/fade_indexed_stack.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_issue_widget.dart';
import 'package:ticking_app/pages/home/<USER>/share/on_the_way_widget.dart';
import 'package:ticking_app/pages/home/<USER>/tab/parts_tab.dart';
import 'package:ticking_app/utils/datetime_utils/datetime_until.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/btn.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';
import 'package:ticking_app/widgets/button/circle_shadow_button.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:url_launcher/url_launcher.dart';

void showCompleteJobDetailBS({
  BuildContext? context,
  required CompleteJobListBloc completeJobListBloc,
  String? distance,
  required String group,
  required Function onUnableToAccess,
  required Job job,
  bool isStart = false,
  SmsTemplate? smsTemplate,
  Function(JobComment comment, Job job)? onCommentDeleted,
  Function(JobComment comment, Job job, String body)? onCommentEdit,
  Function(String comment, Job job)? onCommentPosted,
  // Function(Job job, String comment)? onRepairPosted,
  bool isDoneButtonEnable = true,
}) {
  AppSingleton.setJob(job);

  showModalBottomSheet(
    context: context ?? App.overlayContext!,
    builder: (context) => BlocProvider(
      create: (BuildContext context) => JobDetailBsBloc(),
      child: BlocBuilder<CompleteJobListBloc, CompleteJobListState>(
        bloc: completeJobListBloc,
        builder: (context, state) {
          Job? job = state.detailJob;
          if (job == null) return const LoadingIndicator();
          return JobDetailBS(
            job: job,
            isDoneButtonEnable: isDoneButtonEnable,
            distance: distance,
            group: group,
            smsTemplate: smsTemplate,
            isStart: isStart,

            onUnableToAccess: onUnableToAccess,
            onCommentDeleted: onCommentDeleted,
            onCommentEdit: onCommentEdit,
            onCommentPosted: onCommentPosted,
            completeJobListBloc: completeJobListBloc,
            // onRepairPosted: onRepairPosted,
          );
        },
      ),
    ),
    // backgroundColor: Colors.green,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(Dimens.rad_XXXSL),
        topRight: Radius.circular(Dimens.rad_XXXSL),
      ),
    ),
  );
}

class JobDetailBS extends StatefulWidget {
  const JobDetailBS({
    Key? key,
    required this.job,
    required this.distance,
    required this.onUnableToAccess,
    required this.group,
    this.smsTemplate,
    // this.jobDetailsBloc,
    this.isStart = false,
    this.onCommentDeleted,
    this.onCommentEdit,
    this.onCommentPosted,
    this.onCompleteJob,
    // this.onRepairPosted,
    // this.repairTicketBloc,
    // this.completeJobListBloc,
    this.isDoneButtonEnable = true,
    required this.completeJobListBloc,
  }) : super(key: key);
  final Job job;
  final String? distance;
  final String group;
  final bool isStart;

  // final JobDetailsBloc? jobDetailsBloc;
  final SmsTemplate? smsTemplate;
  final Function onUnableToAccess;
  final Function(Job job)? onCompleteJob;
  final Function(JobComment comment, Job job)? onCommentDeleted;
  final Function(String comment, Job job)? onCommentPosted;
  final Function(JobComment comment, Job job, String body)? onCommentEdit;

  // final Function(Job job, String comment)? onRepairPosted;
  // final RepairTicketBloc? repairTicketBloc;
  final CompleteJobListBloc completeJobListBloc;
  final bool isDoneButtonEnable;

  @override
  State<JobDetailBS> createState() => _JobDetailBSState();
}

class _JobDetailBSState extends State<JobDetailBS> {
  @override
  void initState() {
    if (!widget.isStart) {
      BlocProvider.of<JobListBloc>(context).add(JobListBSUpdated(true));
    }

    super.initState();
  }

  @override
  void dispose() {
    if (!widget.isStart) {
      BlocProvider.of<JobListBloc>(App.overlayContext!)
          .add(JobListBSUpdated(false));
    }

    super.dispose();
  }

  // void _onMapSelected(
  //     {required double? latitude, required double? longitude}) async {
  //   final mapUrl =
  //       'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
  //   if (!await launch(mapUrl)) throw 'Could not launch $mapUrl';
  // }

  Future<void> _launchMaps({
    required double? latitude,
    required double? longitude,
  }) async {
    // final street = widget.job.contact.street;
    // final city = widget.job.contact.city;
    // final state = widget.job.contact.state?.name;
    // final zipCode = widget.job.contact.zipCode;
    if (Platform.isIOS) {
      String googleUrl =
          'comgooglemapsurl://maps.google.com/?f=d&daddr=$latitude,$longitude&directionsmode=driving';
      String appleUrl = 'https://maps.apple.com/?sll=$latitude,$longitude';

      if (await canLaunch("comgooglemaps://")) {
        await launch(googleUrl);
      } else if (await canLaunch(appleUrl)) {
        await launch(appleUrl);
      } else {
        MapsLauncher.launchCoordinates(latitude ?? 0, longitude ?? 0);
      }
    } else {
      String googleUrl = 'google.navigation:q=$latitude,$longitude&mode=d';
      await launch(googleUrl);
    }
  }

  void _onTabChanged(BuildContext context, int index) {
    BlocProvider.of<JobDetailBsBloc>(context).add(JobTabChanged(index));
  }

  void _onStartJob(BuildContext context) {
    BlocProvider.of<JobListBloc>(context).add(JobStarted(widget.job));
  }

  void _onCompleteJob(BuildContext context) {
    widget.onCompleteJob?.call(widget.job);
    // ??
    //     widget.jobDetailsBloc?.add(
    //       JobDetailsCompleted(
    //         request: JobTaskListRequest(
    //           endDate: DateTime.now(),
    //           reason: 'complete',
    //           tasks: [],
    //           startDate: DateTime.now(),
    //         ),
    //       ),
    //     );
  }

  @override
  Widget build(BuildContext context) {
    return _wWithKeyBoardVisible(context);
  }

  Widget _wWithKeyBoardVisible(BuildContext context) {
    return SizedBox(
      height: ViewUtils.getPercentHeight(percent: 0.8),
      child: Column(
        children: [
          _wRowNameAndPinIcon(context).px16().pt8(),
          _wBodyWithKeyBoardVisible(context).px16().py8().expand(),
          _wFooterButtons(context)
        ],
      ),
    );
  }

  Widget _wBodyWithKeyBoardVisible(BuildContext context) {
    return Scrollbar(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _wRowJobInfo(context),
            Gaps.vGap12,
            _wGroupButton(context),
            Gaps.vGap12,
            _wTabBar(context),
            Gaps.vGap8,
            _wBodyOfTabWithKeyBoardVisible(context),
            MediaQuery.of(context).viewInsets.bottom.toVSizeBox(),
            // Gaps.vGap20
          ],
        ),
      ),
    );
  }

  Widget _wRowNameAndPinIcon(BuildContext context) {
    return Row(
      children: [
        // (widget.job.id)
        //     .text
        //     .bodyText1(context)
        //     .fontWeight(FontWeight.bold)
        //     .size(Dimens.text_XXL)
        //     .make(),
        (widget.job.contact.name ?? '')
            .text
            .bodyText1(context)
            .fontWeight(FontWeight.bold)
            .size(Dimens.text_XXL)
            .make()
            .expand(),

        CircleShadowButton(
          child: Assets.image.googleMap.image().p8(),
          backgroundColor: Colors.white,
          onPressed: () {
            // _onMapSelected(
            //   latitude: widget.job.contact.gps?.latitude,
            //   longitude: widget.job.contact.gps?.longitude,
            // );

            _launchMaps(
              latitude: widget.job.contact.gps?.latitude,
              longitude: widget.job.contact.gps?.longitude,
            );
          },
          size: Dimens.ic_4XL,
        ),
      ],
    ).pt(value: 12);
  }

  Widget _wRowJobInfo(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Row(
              // mainAxisAlignment: MainAxisAlignment.,
              // crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.home,
                  color: AppColors.grey,
                ),
                Gaps.hGap12,
                widget.job.contact
                    .getFullLocation()
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .lineHeight(1.5)
                    .make()
                    .expand(),
              ],
            ),
            Gaps.vGap12,
            Row(
              children: [
                const Icon(Icons.mail, color: AppColors.grey),
                Gaps.hGap12,
                (widget.job.contact.email ?? '')
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .make()
                    .expand(),
              ],
            ),
            Gaps.vGap12,
            Row(
              children: [
                const Icon(Icons.phone, color: AppColors.grey),
                Gaps.hGap12,
                (widget.job.contact.phone ?? '')
                    .text
                    .bodyText2(context)
                    .size(Dimens.text)
                    .fontWeight(FontWeight.normal)
                    .make(),
              ],
            ),

            if (widget.job.contact.lastVacuumDate != null) ...[
              Gaps.vGap12,
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Assets.icon.calendar.svg(
                    color: AppColors.grey,
                    fit: BoxFit.fill,
                    width: Dimens.ic,
                  ),
                  Gaps.hGap17,
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      'Last Vaccum Date'
                          .text
                          .bodyText2(context)
                          .color(AppColors.grey)
                          .size(Dimens.text)
                          .fontWeight(FontWeight.normal)
                          .make(),
                      Gaps.vGap2,
                      (widget.job.contact.lastVacuumDate ?? '')
                          .text
                          .size(Dimens.text)
                          .fontWeight(FontWeight.normal)
                          .make(),
                    ],
                  ),
                ],
              ),
            ],

            Gaps.vGap12,
            Row(
              children: [
                CircleButton(
                  child: Assets.icon.running.svg(
                    color: AppColors.grey,
                  ),
                  size: Dimens.ic,
                ),
                Gaps.hGap12,
                InkWell(
                  onTap: () async {
                    final _isConnected =
                        await SimpleConnectionChecker.isConnectedToInternet();

                    if (_isConnected == true) {
                      showOnTheWayDialog(
                        job: widget.job,
                      );
                    }
                  },
                  child: ('On the Way')
                      .text
                      .bodyText2(context)
                      .size(Dimens.text_L)
                      .color(AppColors.textIcon)
                      .fontWeight(FontWeight.bold)
                      .make(),
                ),
              ],
            ),
          ],
        ).expand(flex: 7),
        Gaps.hGap16,
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            'Done by'
                .text
                .bodyText2(context)
                .color(AppColors.grey)
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .lineHeight(1.5)
                .make(),
            Gaps.vGap2,
            widget.job.employee.name.text
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            if (widget.job.details.estimatedTime != null)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Gaps.vGap8,
                  'Estimate time'
                      .text
                      .bodyText2(context)
                      .color(AppColors.grey)
                      .size(Dimens.text)
                      .fontWeight(FontWeight.normal)
                      .lineHeight(1.5)
                      .make(),
                  Gaps.vGap2,
                  printDuration(
                    Duration(
                      seconds: widget.job.details.estimatedTime!,
                    ),
                  ).text.size(Dimens.text).fontWeight(FontWeight.normal).make(),
                ],
              ),
            Gaps.vGap8,
            'Complete time'
                .text
                .bodyText2(context)
                .color(AppColors.grey)
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            Gaps.vGap2,
            TimeUtils.dateToStr(widget.job.completeDate)
                .text
                .size(Dimens.text)
                .fontWeight(FontWeight.normal)
                .make(),
            Gaps.vGap12,
          ],
        ).expand(flex: 3),
      ],
    );
  }

  Widget _wGroupButton(BuildContext context) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.start,
      alignment: WrapAlignment.start,
      runAlignment: WrapAlignment.start,
      spacing: 10,
      children: [
        Btn(
          style: AppButtonStyle.filterCleanStyle(context),
          text: widget.job.details.serviceType,
        ),
        if (widget.job.contact.isFiberglassPool == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: "Fiber Glass",
          ),
        if (!widget.job.details.reoccurrence.isEmptyOrNull)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: '${widget.job.details.reoccurrence}',
          ),
        if (widget.job.details.serviceLevel != null)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: widget.job.details.serviceLevel,
          ),

        if (widget.job.contact.filterType!=null && widget.job.contact.filterType!="")
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: widget.job.contact.filterType,
          ),

        if (widget.job.details.isLightningService == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                    (Set<MaterialState> states) => AppColors.primaryColor2)),
            text: "Lightning Service",
          ),



        if (widget.group == 'Overdue')
          Btn(
            style: widget.group == 'Overdue'
                ? AppButtonStyle.overDueStyle(context)
                : AppButtonStyle.filterCleanStyle(context).copyWith(
                    foregroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) =>
                            AppColors.primaryColor2)),
            text: 'Overdue',
          ),

        if (widget.job.contact.isSaltWater == true)
          Btn(
            style: AppButtonStyle.filterCleanStyle(context),
            text: "Salt Water",
          ),

        if (widget.job.details.specialRequests != null)
          Btn(
            style: AppButtonStyle.specialStyle(context).copyWith(
                foregroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) => AppColors.specialColor)),
            text: 'Special Requests',
          ),
      ],
    ).pb(value: Dimens.rad_S);
  }

  Widget _wTabBar(BuildContext context) {
    return BlocBuilder<JobDetailBsBloc, JobDetailBsState>(
        builder: (context, state) {
      final isShowPartTab = widget.job.details.repairParts.isNotEmpty;
      int countTab = isShowPartTab ? 4 : 3;
      if (widget.job.details.specialRequests != null) {
        countTab++;
      }
      return DefaultTabController(
        length: countTab,
        child: Stack(
          children: [
            Positioned.fill(
              child: Container(
                decoration: const BoxDecoration(
                    border: Border(
                        bottom:
                            BorderSide(color: AppColors.grey4, width: 2.0))),
              ),
            ),
            SizedBox(
              height: 50,
              child: TabBar(
                tabs: <Widget>[
                  if(widget.job.details.specialRequests!=null)
                  const Tab(text: ' Special\nRequests'),
                  const Tab(text: 'Access'),
                  const Tab(text: 'Description'),
                  const Tab(text: 'Ticket'),
                  if (isShowPartTab) const Tab(text: 'Parts'),
                ],
                onTap: (index) {
                  // BlocProvider.of<HomeBloc>(context).add(HomeTabChanged(index));
                  _onTabChanged(context, index);
                },
                indicatorColor: Theme.of(context).primaryColor,
                labelColor: AppColors.primaryColor,
                padding: EdgeInsets.zero,
                labelPadding: EdgeInsets.zero,
                indicatorPadding: EdgeInsets.zero,
                labelStyle: Theme.of(context).textTheme.bodyText1?.copyWith(
                    color: Theme.of(context).primaryColor,
                    fontSize: Dimens.font_sp12,
                    fontWeight: FontWeight.bold),
                unselectedLabelColor: AppColors.grey,
                indicator: UnderlineTabIndicator(
                    borderSide: BorderSide(
                        color: Theme.of(context).primaryColor, width: 2.0),
                    insets: const EdgeInsets.symmetric(horizontal: 10.0)),
              ),
            )
          ],
        ),
      );
    });
  }

  Widget _wBodyOfTabWithKeyBoardVisible(BuildContext context) {
    final isShowPartTab = widget.job.details.repairParts.isNotEmpty;
    return BlocBuilder<JobDetailBsBloc, JobDetailBsState>(
      // buildWhen: (_, current)=> current is! JobPostCommentSuccess,
      builder: (context, state) {

        return FadeIndexedStack(
          index: state.tabIndex,
          duration: const Duration(milliseconds: 200),
          children: [
            if(widget.job.details.specialRequests!=null)
              SpecialRequestTab(
                text: widget.job.details.specialRequests,
              ),
            const AccessTab(),
            DescriptionTab(
              job: widget.job,
            ),
            TicketTab(
              job: widget.job,
              completeJobListBloc: widget.completeJobListBloc,
              isKeyBoardVisible: true,
              isStart: widget.isStart,
              onCommentEdit: widget.onCommentEdit,
              onCommentDeleted: widget.onCommentDeleted,
              onCommentPosted: widget.onCommentPosted,
            ),
            if (isShowPartTab)
              PartsTab(
                job: widget.job,
              ),
          ],
        );
      },
    );
  }

  Widget _wRepairFooterButtons(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: AppDecorStyle.topShadowDecor(
          surfaceColor: AppColors.materialWhite,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CommonOutlineButton(
              text: 'ISSUE',
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 45),
              onPressed: () {
                showIssueDialog(
                  email: widget.job.contact.email,
                  phoneNumber: widget.job.contact.phone,
                  smsTemplate: widget.smsTemplate,
                  job: widget.job,
                  onUnableToAccess: (String reason) {
                    App.pop();
                    widget.onUnableToAccess();
                    // widget.repairTicketBloc
                    //     ?.add(RepairTicketsUnableAccessed(reason));
                  },
                );
              },
            ),
            12.toHSizeBox(),
            CommonPrimaryButton(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 45),
              child: 'DONE'
                  .text
                  .textStyle(Theme.of(context).textTheme.bodyText1!)
                  .size(Dimens.text_XL)
                  .color(AppColors.materialWhite)
                  .fontWeight(FontWeight.w600)
                  .make(),
              onPressed: () {
                // widget.repairTicketBloc?.add(RepairTicketCompleted(
                //   request: JobTaskListRequest(
                //     endDate: DateTime.now(),
                //     reason: 'complete',
                //     tasks: [],
                //     startDate: DateTime.now(),
                //   ),
                // ));
              },
            ),
          ],
        ).px16().pOnly(bottom: 10),
      ),
    );
  }

  Widget _wFooterButtons(BuildContext context) {
    return SafeArea(
      child: Container(
        decoration: AppDecorStyle.topShadowDecor(
          surfaceColor: AppColors.materialWhite,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            CommonOutlineButton(
              text: 'REPAIR',
              onPressed: () {
                showRequestBS(
                  // onSubmitted: (description) => widget.onRepairPosted != null
                  //     ? widget.onRepairPosted?.call(widget.job, description)
                  //     : BlocProvider.of<JobListBloc>(context).add(
                  //         JobRepairPosted(
                  //           job: widget.job,
                  //           description: description,
                  //         ),
                  //       ),
                  job: widget.job,
                );
              },
            ),
            Gaps.hGap12,
            Expanded(
              child: CommonPrimaryButton(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 45),
                child: 'View'
                    .text
                    .textStyle(Theme.of(context).textTheme.bodyText1!)
                    .size(Dimens.text_XL)
                    .color(AppColors.materialWhite)
                    .fontWeight(FontWeight.w600)
                    .make(),
                onPressed: () {
                  final type = widget.job.type;
                  switch (type.jobServiceType) {
                    case ServiceType.cleaning:
                      App.pushNamed(
                        AppRoutes.details,
                        JobDetailArg(
                          job: widget.job,
                          jobDetailType: JobDetailType.reviewing,
                        ),
                      );
                      break;
                    case ServiceType.repair:
                      App.pushNamed(
                        AppRoutes.repair,
                        JobRepairArg(
                          job: widget.job,
                          jobRepairType: JobRepairType.reviewing,
                          bloc: widget.completeJobListBloc,
                        ),
                      );
                      break;
                  }
                  // widget.repairTicketBloc?.add(RepairTicketCompleted(
                  //   request: JobTaskListRequest(
                  //     endDate: DateTime.now(),
                  //     reason: 'complete',
                  //     tasks: [],
                  //     startDate: DateTime.now(),
                  //   ),
                  // ));
                },
              ),
            ),
            Gaps.hGap12,
            if (widget.isDoneButtonEnable)
              CommonPrimaryButton(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    (widget.isStart ? 'DONE' : 'START')
                        .text
                        .textStyle(Theme.of(context).textTheme.bodyText1!)
                        .size(Dimens.text_XL)
                        .color(AppColors.materialWhite)
                        .fontWeight(FontWeight.w600)
                        .make(),
                    const Icon(
                      Icons.arrow_forward_ios_sharp,
                      color: AppColors.materialWhite,
                      size: 16,
                    )
                  ],
                ),
                onPressed: () {
                  if (!widget.isStart) {
                    _onStartJob(context);
                  } else {
                    _onCompleteJob(context);
                  }
                },
              ).expand()
          ],
        ).px16().pOnly(bottom: 10),
      ),
    );
  }
}
