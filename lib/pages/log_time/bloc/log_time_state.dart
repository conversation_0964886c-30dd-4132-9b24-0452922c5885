part of 'log_time_bloc.dart';

class LogTimeData {
  LogTimeData({
    this.status = LogTimeStatus.loading,
    this.errorMessage = 'N/a',
    this.isBreak = false,
    this.startedAt,
    this.timeSheetType = '',
    this.workLog,
    this.isStarted = false,
    this.description = '',
    this.twoDigitHour = '00',
    this.twoDigitMinutes = '00',
    this.twoDigitSeconds = '00',
  });

  LogTimeData copyWith({
    LogTimeStatus? status,
    String? errorMessage,
    bool? isBreak,
    DateTime? startedAt,
    String? timeSheetType,
    bool? isBusy,
    WorkLog? workLog,
    bool? isStarted,
    String? description,
    String? twoDigitHour,
    String? twoDigitMinutes,
    String? twoDigitSeconds,
  }) {
    return LogTimeData(
        isBreak: isBreak ?? this.isBreak,
        errorMessage: errorMessage ?? this.errorMessage,
        status: status ?? this.status,
        startedAt: startedAt ?? this.startedAt,
        timeSheetType: timeSheetType ?? this.timeSheetType,
        workLog: workLog ?? this.workLog,
        isStarted: isStarted ?? this.isStarted,
        description: description ?? this.description,
        twoDigitHour: twoDigitHour ?? this.twoDigitHour,
        twoDigitMinutes: twoDigitMinutes ?? this.twoDigitMinutes,
        twoDigitSeconds: twoDigitSeconds ?? this.twoDigitSeconds);
  }

  // final TimeOfDay timeOfDay;
  final LogTimeStatus status;

  // final DateTime dateTime;
  final String errorMessage;
  final bool isBreak;
  final DateTime? startedAt;
  final String timeSheetType;
  final WorkLog? workLog;
  final bool isStarted;
  final String description;
  final String twoDigitHour;
  final String twoDigitMinutes;
  final String twoDigitSeconds;
}

@immutable
class LogTimeState {
  const LogTimeState(this.data);

  final LogTimeData data;
}

class LogTimeLoading extends LogTimeState {
  LogTimeLoading(WorkLog? workLog)
      : super(
          LogTimeData(
              workLog: workLog,
              isBreak: false,
              errorMessage: '',
              status: LogTimeStatus.loading,
              startedAt: null,
              timeSheetType: '',
              description: ''),
        );
}

class LogTimeLoadSuccess extends LogTimeState {
  const LogTimeLoadSuccess(LogTimeData data) : super(data);
}

class LogTimeValueChanged extends LogTimeState {
  const LogTimeValueChanged(LogTimeData data) : super(data);
}

class LogTimeResetSuccess extends LogTimeState {
  LogTimeResetSuccess()
      : super(
          LogTimeData(
              workLog: null,
              isBreak: false,
              errorMessage: '',
              status: LogTimeStatus.loading,
              startedAt: null,
              timeSheetType: '',
              description: ''),
        );
}

class LogTimeSubmitSuccess extends LogTimeState {
  const LogTimeSubmitSuccess(LogTimeData data) : super(data);
}

class LogTimeValidationError extends LogTimeState {
  const LogTimeValidationError(LogTimeData data) : super(data);
}

class LogTimeSubmitError extends LogTimeState {
  const LogTimeSubmitError(LogTimeData data) : super(data);
}

enum LogTimeStatus {
  loading,
  load,
  failure,
  dataChange,
  logSuccess,
  logFailure
}

final List<String> timesheetTypes = ['', 'Meeting', 'Training'];
