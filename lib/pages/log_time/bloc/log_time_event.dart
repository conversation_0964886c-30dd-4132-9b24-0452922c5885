part of 'log_time_bloc.dart';

abstract class LogTimeEvent {}

class LogTimeLoaded extends LogTimeEvent {}

class LogTimeStarted extends LogTimeEvent {
  LogTimeStarted({required this.startedAt});

  final DateTime startedAt;

  @override
  String toString() {
    return 'LogTimeStarted{}';
  }
}

class LogTimeDateChanged extends LogTimeEvent {
  LogTimeDateChanged({required this.timeOfDay});

  final TimeOfDay timeOfDay;

  @override
  String toString() {
    return 'LogTimeDateChanged{timeOfDay: $timeOfDay}';
  }
}

class LogTimeLogged extends LogTimeEvent {
  LogTimeLogged({required this.workLog});

  final WorkLog workLog;

  @override
  String toString() {
    return 'LogTimeLogged{workLog: $workLog}';
  }
}

class LogTimeDateTimeChanged extends LogTimeEvent {
  LogTimeDateTimeChanged({required this.dateTime});

  final DateTime dateTime;

  @override
  String toString() {
    return 'LogTimeDateTimeChanged{dateTime: $dateTime}';
  }
}

class LogTimeBreakRequested extends LogTimeEvent {
  LogTimeBreakRequested();

  @override
  String toString() {
    return 'LogTimeBreakRequested{}';
  }
}

class LogTimeTimeSheetTypeChanged extends LogTimeEvent {
  LogTimeTimeSheetTypeChanged({required this.timeSheetType});

  final String timeSheetType;
}

class LogTimeDescriptionChanged extends LogTimeEvent {
  LogTimeDescriptionChanged({required this.description});

  final String description;
}

class LogTimeResetRequested extends LogTimeEvent {
  LogTimeResetRequested({required this.startedAt});

  final DateTime startedAt;
}

class LogTimeDurationChanged extends LogTimeEvent {
  LogTimeDurationChanged(
      {required this.twoDigitHour,
      required this.twoDigitMinutes,
      required this.twoDigitSeconds});

  final String twoDigitHour;
  final String twoDigitMinutes;
  final String twoDigitSeconds;
}

class LogTimeSubmitRequested extends LogTimeEvent {
  LogTimeSubmitRequested();
}
