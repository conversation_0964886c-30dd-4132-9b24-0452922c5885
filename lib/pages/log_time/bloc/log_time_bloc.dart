import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:http/http.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/task_service/model/log_time_task.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/uuid_util.dart';

part 'log_time_event.dart';

part 'log_time_state.dart';

class LogTimeBloc extends Bloc<LogTimeEvent, LogTimeState> {
  LogTimeBloc(
      {required WorkLog? workLog,
      WorkLogsApi? workLogsApi,
      TickingLogService? logService,
      TaskService? taskService,
      CacheService? cacheService})
      : super(LogTimeLoading(workLog)) {
    on<LogTimeLoaded>(_onLoaded);
    on<LogTimeStarted>(_onStarted);
    on<LogTimeBreakRequested>(_onBreakRequested);
    on<LogTimeTimeSheetTypeChanged>(_onTimeSheetTypeChanged);
    on<LogTimeDescriptionChanged>(_onDescriptionChanged);
    on<LogTimeResetRequested>(_onResetRequested);
    on<LogTimeSubmitRequested>(_onLogged);
    on<LogTimeDurationChanged>(_onDurationChanged);
    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _workLogsApi = workLogsApi ?? GetIt.I<WorkLogsApi>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
  }

  late final TaskService _taskService;
  late final WorkLogsApi _workLogsApi;
  late final TickingLogService _tickingLogService;
  late final CacheService _cacheService;

  Future<void> _onLoaded(
    LogTimeLoaded event,
    Emitter<LogTimeState> emit,
  ) async {
    emit(LogTimeLoadSuccess(state.data.copyWith(
        isStarted: state.data.workLog?.startDate != null,
        status: LogTimeStatus.load,
        isBreak: state.data.workLog?.isBreak ?? state.data.isBreak,
        startedAt: state.data.workLog?.startDate ?? state.data.startedAt,
        timeSheetType: state.data.workLog?.jobType?.capitalizeFirstOnly() ??
            state.data.timeSheetType,
        description: state.data.workLog?.description ?? state.data.description,
        workLog: state.data.workLog ?? WorkLog())));
  }

  Future<void> _onStarted(
    LogTimeStarted event,
    Emitter<LogTimeState> emit,
  ) async {
    WorkLog workLog = WorkLog(
        jobType: state.data.timeSheetType,
        startDate: event.startedAt,
        timeSpent: '${state.data.twoDigitHour}h ${state.data.twoDigitMinutes}m',
        isBreak: state.data.isBreak,
        description: state.data.description);

    _cacheService.setWorkLogStart(workLog);

    emit(LogTimeValueChanged(state.data.copyWith(
      workLog: workLog,
      startedAt: event.startedAt,
      isStarted: true,
    )));
  }

  Future<void> _onBreakRequested(
    event,
    Emitter<LogTimeState> emit,
  ) async {
    WorkLog workLog = state.data.workLog!;
    workLog.isBreak = !state.data.isBreak;

    if (state.data.isStarted && state.data.startedAt != null) {
      _cacheService.setWorkLogStart(workLog);
    }

    emit(LogTimeValueChanged(state.data.copyWith(
      isBreak: !state.data.isBreak,
    )));
  }

  Future<void> _onTimeSheetTypeChanged(
    LogTimeTimeSheetTypeChanged event,
    Emitter<LogTimeState> emit,
  ) async {
    WorkLog workLog = state.data.workLog!;
    workLog.jobType = event.timeSheetType;
    workLog.isBreak = event.timeSheetType == 'Training' ? false : null;

    if (state.data.isStarted && state.data.startedAt != null) {
      _cacheService.setWorkLogStart(workLog);
    }

    emit(LogTimeValueChanged(state.data.copyWith(
        timeSheetType: event.timeSheetType,
        isBreak: event.timeSheetType == 'Training' ? false : null)));
  }

  Future<void> _onDescriptionChanged(
    LogTimeDescriptionChanged event,
    Emitter<LogTimeState> emit,
  ) async {
    WorkLog workLog = state.data.workLog!;
    workLog.description = event.description;

    if (state.data.isStarted && state.data.startedAt != null) {
      _cacheService.setWorkLogStart(workLog);
    }

    emit(LogTimeValueChanged(
        state.data.copyWith(description: event.description)));
  }

  Future<void> _onDurationChanged(
    LogTimeDurationChanged event,
    Emitter<LogTimeState> emit,
  ) async {
    emit(LogTimeValueChanged(state.data.copyWith(
        twoDigitHour: event.twoDigitHour,
        twoDigitMinutes: event.twoDigitMinutes,
        twoDigitSeconds: event.twoDigitSeconds)));
  }

  Future<void> _onResetRequested(
    LogTimeResetRequested event,
    Emitter<LogTimeState> emit,
  ) async {
    await _cacheService.removeWorkLogStart();

    WorkLog workLog = state.data.workLog!;
    workLog.startDate = event.startedAt;
    workLog.jobType = '';
    workLog.description = '';
    workLog.isBreak = false;

    _cacheService.setWorkLogStart(workLog);

    emit(LogTimeValueChanged(state.data.copyWith(
        isBreak: workLog.isBreak,
        startedAt: workLog.startDate,
        timeSheetType: workLog.jobType,
        description: workLog.description,
        status: LogTimeStatus.load)));
  }

  Future<void> _onLogged(
    LogTimeSubmitRequested event,
    Emitter<LogTimeState> emit,
  ) async {
    emit(LogTimeSubmitSuccess(
        state.data.copyWith(status: LogTimeStatus.loading)));

    if (state.data.timeSheetType.isNullOrEmpty()) {
      emit(LogTimeValidationError(state.data.copyWith(
          status: LogTimeStatus.logFailure,
          errorMessage:
              'You must select the Timesheet Type \n before Clock Out')));
      return;
    }

    if (state.data.description.isNullOrEmpty()) {
      emit(LogTimeValidationError(state.data.copyWith(
          status: LogTimeStatus.logFailure,
          errorMessage: 'You must enter the Description before \n Clock Out')));
      return;
    }

    WorkLog workLog = state.data.workLog!;

    try {
      workLog.startDate = state.data.startedAt;

      workLog.timeSpent =
          '${state.data.twoDigitHour}h ${state.data.twoDigitMinutes}m';

      switch (state.data.timeSheetType) {
        case 'Meeting':
          workLog.jobType = 'meeting';
          break;
        case "Training":
          workLog.jobType = 'training';
          break;
        default:
          workLog.jobType = '';
      }

      workLog.isBreak = state.data.isBreak;
      workLog.description = state.data.description;

      await _workLogsApi.postWorkLogs(workLog);
      _cacheService.removeWorkLogStart();
      emit(LogTimeSubmitSuccess(
          state.data.copyWith(status: LogTimeStatus.logSuccess)));
    } catch (e, stack) {
      if (e is SocketException ||
          e is ApiTimeoutException ||
          e is ClientException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}';
        DataTask task = DataTask(
          taskId: taskId,
          task: LogTimeTask(workLog: workLog),
          type: TaskType.logTimeTask,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);
        emit(LogTimeSubmitSuccess(
            state.data.copyWith(status: LogTimeStatus.logSuccess)));
        _cacheService.removeWorkLogStart();
        return;
      } else {
        emit(LogTimeSubmitError(state.data.copyWith(
          errorMessage: e.toString().replaceAll('Exception:', '').trim(),
          status: LogTimeStatus.logFailure,
        )));

        if (!kDebugMode) {
          await Sentry.captureException(e, stackTrace: stack);
        }

        _tickingLogService.error('LogTimeLogged', e.toString(), stack);
      }
    }
  }
}
