import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/log_time_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

import '../../widgets/drop_down/app_drop_down.dart';
import 'bloc/log_time_bloc.dart';

class LogTimePage extends StatefulWidget {
  const LogTimePage({Key? key, this.arg}) : super(key: key);
  final LogTimeArg? arg;

  @override
  _LogTimePageState createState() => _LogTimePageState();
}

class _LogTimePageState extends State<LogTimePage> {
  late LogTimeBloc _bloc;
  final TextEditingController _descriptionController = TextEditingController();
  DateTime timeNow = DateTime.now().subtract(const Duration(days: 1));
  Timer? _timer;

  _formatDuration(Duration? duration) async {
    String twoDigits(int n) {
      if (n >= 10) return "$n";
      return "0$n";
    }

    String twoDigitHour = '00';
    String twoDigitMinutes = '00';
    String twoDigitSeconds = '00';

    if (duration != null) {
      twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
      twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
      twoDigitHour = twoDigits(duration.inHours);
    } else {
      twoDigitMinutes = "00";
      twoDigitSeconds = "00";
      twoDigitHour = "00";
    }

    _bloc.add(LogTimeDurationChanged(
        twoDigitHour: twoDigitHour,
        twoDigitMinutes: twoDigitMinutes,
        twoDigitSeconds: twoDigitSeconds));
  }

  void _updateTimer(DateTime time) {
    final duration = DateTime.now().difference(time);
    _formatDuration(duration);
  }

  @override
  void dispose() {
    super.dispose();
    if (_timer != null && _timer!.isActive) {
      _timer!.cancel();
    }
  }

  @override
  void initState() {
    _bloc = LogTimeBloc(workLog: widget.arg?.workLog);

    WorkLog? workLog = widget.arg?.workLog;
    if (workLog != null && workLog.startDate != null) {
      _descriptionController.text = workLog.description!;
      _timer = Timer.periodic(
        const Duration(seconds: 1),
        (timer) => _updateTimer(workLog.startDate!),
      );
    }

    _bloc.add(LogTimeLoaded());

    super.initState();
  }

  void _onClockIn(DateTime time) {
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) => _updateTimer(time),
    );
  }

  void _reset() {
    _stopTimer();
    _descriptionController.clear();
    DateTime date = DateTime.now();
    _bloc.add(LogTimeResetRequested(startedAt: date));
    setState(() {
      _timer = Timer.periodic(
        const Duration(seconds: 1),
        (timer) => _updateTimer(date),
      );
    });
  }

  void _stopTimer() {
    if (_timer!.isActive) {
      _timer?.cancel();
    }
  }

  Future<bool> _willPopCallback() async {
    if (_bloc.state.data.isStarted == false &&
        _bloc.state.data.startedAt == null) {
      return Future.value(true);
    }

    SystemChannels.platform.invokeMethod<void>('SystemNavigator.pop');
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: BlocListener<LogTimeBloc, LogTimeState>(
          listenWhen: (previous, current) =>
           previous.data.status != current.data.status,
          listener: (context, state) {
            if (state.data.status == LogTimeStatus.logFailure) {
              DialogHelper.showError(content: state.data.errorMessage);
              _bloc.add(LogTimeLoaded());
            }
            if (state.data.status == LogTimeStatus.logSuccess) {
              _stopTimer();
              _showLogTimeDialog(
                context: context,
                onReset: _reset,
              );
            }
          },
          child: WillPopScope(
            onWillPop: () {
              return _willPopCallback();
            },
            child: BlocBuilder<LogTimeBloc, LogTimeState>(
                buildWhen: (previous, current) =>
                    previous.data.status != current.data.status,
                builder: (context, state) {
                  if (state.data.status == LogTimeStatus.loading) {
                    return const Center(
                      child: LoadingIndicator(),
                    );
                  }

                  return Scaffold(
                    backgroundColor: Colors.white,
                    appBar: const _AppBarBuilder(),
                    body: SingleChildScrollView(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).size.height / 7),
                        child: Stack(
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const _TitleBuilder(),
                                _DurationBuilder(
                                  onReset: _reset,
                                ),
                                _TimeSheetTypeBuilder(
                                  onReset: _reset,
                                  stopTimer: _stopTimer,
                                ),
                                const _IsBreakBuilder(),
                                _DescriptionBuilder(
                                    descriptionController:
                                        _descriptionController),
                              ],
                            )
                          ],
                        ).pl(value: Dimens.pad_L)),
                    bottomSheet: BlocBuilder<LogTimeBloc, LogTimeState>(
                      buildWhen: (previous, current) =>
                          previous.data.status != current.data.status,
                      builder: (context, state) => _BottomBuilder(
                        onReset: () {
                          _reset();
                        },
                        onClockIn: () {
                          if (state.data.isStarted == false &&
                              state.data.startedAt == null) {
                            DateTime now = DateTime.now();
                            _onClockIn(now);
                            context
                                .read<LogTimeBloc>()
                                .add(LogTimeStarted(startedAt: now));
                          }
                        },
                        isPressed: state.data.status != LogTimeStatus.loading,
                      ),
                    ),
                  );
                }),
          )),
    );
  }
}

class _AppBarBuilder extends StatelessWidget implements PreferredSizeWidget {
  const _AppBarBuilder({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
      buildWhen: (previous, current) =>
          previous.data.isStarted != current.data.isStarted,
      builder: (context, state) => AppBar(
          automaticallyImplyLeading: false,
          title: Text(
            'Log time',
            style: Theme.of(context)
                .textTheme
                .headline6
                ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
          ),
          centerTitle: true,
          backgroundColor: Colors.white,
          leading: state.data.isStarted == false
              ? Container(
                  padding: const EdgeInsets.all(0),
                  margin: const EdgeInsets.all(4),
                  child: IconButton(
                    icon: const BackButtonIcon(),
                    color: Colors.black,
                    onPressed: () {
                      if (state.data.isStarted == false &&
                          state.data.startedAt == null) {
                        Navigator.pop(context);
                      }
                    },
                  ),
                )
              : null),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}

class _TitleBuilder extends StatelessWidget {
  const _TitleBuilder({Key? key}) : super(key: key);

  formatDateTime(DateTime dateTime) {
    return "${dateTime.year.toString()}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}";
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
        buildWhen: (previous, current) =>
            previous.data.startedAt != current.data.startedAt ||
            previous.data.isStarted != current.data.isStarted,
        builder: (context, state) {
          return Text(
            state.data.startedAt == null
                ? 'Click "Clock In" to start the timer'
                : 'Clocked in at ${formatDateTime(state.data.startedAt ?? DateTime.now())}',
            style: Theme.of(context).textTheme.bodyText1?.copyWith(
                color: AppColors.grey2,
                fontSize: Dimens.font_sp16,
                fontWeight: FontWeight.w800),
          ).pt(value: Dimens.pad).pb(value: Dimens.pad_S);
        });
  }
}

class _TimeSheetTypeBuilder extends StatelessWidget {
  const _TimeSheetTypeBuilder(
      {Key? key, required this.onReset, required this.stopTimer})
      : super(key: key);

  final VoidCallback onReset;
  final VoidCallback stopTimer;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
        buildWhen: (previous, current) =>
            (current.data.isStarted &&
                previous.data.timeSheetType != current.data.timeSheetType) ||
            current.data.isStarted != previous.data.isStarted,
        builder: (context, state) => Column(
              children: [
                Row(children: [
                  Text(
                    'Timesheet\nType',
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: AppColors.black,
                        fontSize: Dimens.font_sp16,
                        fontWeight: FontWeight.w800),
                  ).pr(value: Dimens.pad_L),
                  SizedBox(
                      width: MediaQuery.of(context).size.width * 0.5,
                      child: AppDropDown(
                        groupNameList: timesheetTypes,
                        onSelectedItem: (index) {
                          context.read<LogTimeBloc>().add(
                              LogTimeTimeSheetTypeChanged(
                                  timeSheetType: timesheetTypes[index]));
                        },
                        index: timesheetTypes.indexWhere(
                            (element) => element == state.data.timeSheetType),
                        color: Colors.white,
                        margin: const EdgeInsets.only(left: 0, right: 0),
                        textColor: AppColors.grey,
                        icon: const Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.grey,
                        ),
                        popUpLeft: 0,
                        decoration: BoxDecoration(
                            border: Border.all(color: AppColors.grey3),
                            borderRadius: BorderRadius.circular(8)),
                        textPopUpColor: AppColors.grey,
                      )),
                ]),
              ],
            ));
  }
}

class _DurationBuilder extends StatelessWidget {
  const _DurationBuilder({Key? key, required this.onReset}) : super(key: key);

  final VoidCallback onReset;

  getTimeColor(bool isStarted) {
    return isStarted == false ? AppColors.grey3 : AppColors.systemBlueColor;
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
        buildWhen: (previous, current) =>
            previous.data.twoDigitHour != current.data.twoDigitHour ||
            previous.data.twoDigitMinutes != current.data.twoDigitMinutes ||
            previous.data.twoDigitSeconds != current.data.twoDigitSeconds,
        builder: (context, state) {
          return Stack(
            children: [
              Text(
                'Duration',
                style: Theme.of(context).textTheme.bodyText1?.copyWith(
                    color: AppColors.black,
                    fontSize: Dimens.font_sp16,
                    fontWeight: FontWeight.w800),
              ).pb(value: Dimens.pad_S),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    state.data.twoDigitHour,
                    textAlign: TextAlign.end,
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: getTimeColor(state.data.isStarted),
                        fontSize: 50,
                        fontWeight: FontWeight.w800),
                  ),
                  Text(
                    ":",
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: getTimeColor(state.data.isStarted),
                        fontSize: 50,
                        fontWeight: FontWeight.w800),
                  ).px(Dimens.pad_S),
                  Text(
                    state.data.twoDigitMinutes,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: getTimeColor(state.data.isStarted),
                        fontSize: 50,
                        fontWeight: FontWeight.w800),
                  ),
                  Text(
                    ":",
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: getTimeColor(state.data.isStarted),
                        fontSize: 50,
                        fontWeight: FontWeight.w800),
                  ).px(Dimens.pad_S),
                  Text(
                    state.data.twoDigitSeconds,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyText1?.copyWith(
                        color: getTimeColor(state.data.isStarted),
                        fontSize: 50,
                        fontWeight: FontWeight.w800),
                  ),
                ],
              ).pOnly(left: 40, right: 40, top: 20, bottom: 20),
            ],
          );
        });
  }
}

class _IsBreakBuilder extends StatelessWidget {
  const _IsBreakBuilder({
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(builder: (context, state) {
      return Row(
        children: [
          if (state.data.timeSheetType != 'Training')
            SizedBox(
                width: MediaQuery.of(context).size.width * 0.22,
                child: Text(
                  'Is a Break',
                  style: Theme.of(context).textTheme.bodyText1?.copyWith(
                      color: AppColors.black,
                      fontSize: Dimens.font_sp16,
                      fontWeight: FontWeight.w800),
                )).pr(value: Dimens.pad_L),
          if (state.data.timeSheetType != 'Training')
            SizedBox(
              child: FlutterSwitch(
                width: 60.0,
                height: 28.0,
                value: state.data.isBreak,
                borderRadius: 40.0,
                activeColor: AppColors.primaryColor,
                inactiveColor: Colors.white,
                inactiveToggleColor: AppColors.grey3,
                activeToggleColor: Colors.white,
                padding: 4.0,
                switchBorder: Border.all(
                  color: AppColors.grey3,
                  width: 1.0,
                ),
                onToggle: (val) {
                  context.read<LogTimeBloc>().add(LogTimeBreakRequested());
                },
              ).pr(value: 210),
            )
        ],
      ).pt(value: Dimens.pad_L);
    });
  }
}

class _DescriptionBuilder extends StatelessWidget {
  const _DescriptionBuilder({Key? key, required this.descriptionController})
      : super(key: key);

  final TextEditingController descriptionController;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
      buildWhen: (previous, current) =>
          (current.data.isStarted &&
              previous.data.description != current.data.description) ||
          current.data.isStarted != previous.data.isStarted,
      builder: (context, state) => Column(children: [
        SizedBox(
          child: TextField(
            controller: descriptionController,
            decoration: InputDecoration(
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.only(top: 8, left: 8),
              filled: true,
              hintText: 'Please enter the task description to Clock Out',
              hintStyle: const TextStyle(color: Color(0xFFD0E1EB)),
              border: OutlineInputBorder(
                borderSide:
                    const BorderSide(color: AppColors.grey5, width: 2.0),
                borderRadius: BorderRadius.circular(4.0),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide:
                    const BorderSide(color: AppColors.grey5, width: 2.0),
                borderRadius: BorderRadius.circular(4.0),
              ),
            ),
            keyboardType: TextInputType.multiline,
            maxLines: 16,
            onEditingComplete: () {
              FocusScope.of(context).unfocus();
            },
            onChanged: (value) => {
              context
                  .read<LogTimeBloc>()
                  .add(LogTimeDescriptionChanged(description: value))
            },
          ).pr(value: Dimens.pad_L).pt(value: Dimens.pad_L),
        )
      ]),
    );
  }
}

class _BottomBuilder extends StatelessWidget {
  const _BottomBuilder(
      {Key? key,
      required this.onReset,
      required this.isPressed,
      required this.onClockIn})
      : super(key: key);
  final bool isPressed;
  final VoidCallback onReset;
  final VoidCallback onClockIn;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LogTimeBloc, LogTimeState>(
        buildWhen: (previous, current) =>
            previous.data.isStarted != current.data.isStarted,
        builder: (context, state) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (state.data.isStarted == false)
                    SizedBox(
                      width: 120,
                      child: CommonPrimaryButton(
                        child: 'CLOCK IN'
                            .text
                            .textStyle(Theme.of(context).textTheme.bodyText1!)
                            .size(Dimens.text_XL)
                            .color(AppColors.materialWhite)
                            .fontWeight(FontWeight.w600)
                            .make(),
                        onPressed: () {
                          onClockIn();
                        },
                      ),
                    ),
                  if (state.data.isStarted == true)
                    SizedBox(
                        width: 120,
                        child: CommonOutlineButton(
                          text: 'RESET',
                          onPressed: () {
                            FocusScope.of(context).unfocus();
                            onReset();
                          },
                        )),
                  16.toHSizeBox(),
                  state.data.isStarted
                      ? CommonPrimaryButton(
                          child: 'CLOCK OUT'
                              .text
                              .textStyle(
                                  Theme.of(context).textTheme.bodyText1!)
                              .size(Dimens.text_XL)
                              .color(AppColors.materialWhite)
                              .fontWeight(FontWeight.w600)
                              .make(),
                          onPressed: () {
                            context
                                .read<LogTimeBloc>()
                                .add(LogTimeSubmitRequested());
                            FocusScope.of(context).unfocus();
                          },
                        )
                      : CommonOutlineButton(
                          text: 'CLOCK OUT',
                          onPressed: () {
                            FocusScope.of(context).unfocus();
                          },
                        ),
                ],
              ),
            ));
  }
}

void _showLogTimeDialog(
    {BuildContext? context, required VoidCallback onReset}) {
  showDialog(
      barrierDismissible: false,
      context: context ?? App.overlayContext!,
      builder: (BuildContext context) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            100.toVSizeBox(),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: GestureDetector(
                  onTap: () {
                    App.pop();
                    App.pushNamed(AppRoutes.home);
                  },
                  child: const Icon(
                    Icons.close,
                    size: 40,
                  )),
            ),
            AlertDialog(
                shape: const RoundedRectangleBorder(
                    borderRadius: BorderRadius.all(Radius.circular(16.0))),
                contentPadding: const EdgeInsets.only(top: 10.0),
                insetPadding: const EdgeInsets.only(left: 10, right: 10),
                content: Column(
                  children: [
                    Assets.icon.log
                        .svg(
                          width: 47,
                          height: 47,
                        )
                        .pt(value: 24),
                    const Text(
                      'Work has been logged successfully',
                      style: TextStyle(
                        color: Colors.black,
                        fontSize: 18,
                      ),
                      textAlign: TextAlign.center,
                    ).pt(value: 16),
                    SizedBox(
                      height: 48,
                      width: double.maxFinite,
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonOutlineButton(
                              text: 'LOG MORE',
                              fontWeight: FontWeight.w600,
                              onPressed: () {
                                App.pop();
                                onReset();
                              },
                            ),
                          ),
                          10.toHSizeBox(),
                          Expanded(
                            child: CommonPrimaryButton(
                                child: 'BACK TO HOME'
                                    .text
                                    .textStyle(
                                        Theme.of(context).textTheme.bodyText1!)
                                    .size(Dimens.text_XL)
                                    .color(AppColors.materialWhite)
                                    .fontWeight(FontWeight.w600)
                                    .make(),
                                onPressed: () {
                                  App.pop();
                                  App.pushNamed(AppRoutes.home);
                                }),
                          ),
                        ],
                      ),
                    ).pt(value: 47).pb(value: 16).pr(value: 20).pl(value: 20),
                  ],
                )),
          ],
        );
      });
}
