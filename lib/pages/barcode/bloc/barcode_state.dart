part of 'barcode_bloc.dart';

class BarcodeState {
  const BarcodeState({
    this.status = BarcodeStatus.load,
    this.errorMessage = 'N/a',
    this.truck,
  });

  BarcodeState copyWith({
    String? errorMessage,
    BarcodeStatus? status,
    Truck? truck,
  }) {
    return BarcodeState(
      errorMessage: errorMessage ?? this.errorMessage,
      status: status ?? this.status,
      truck: truck ?? this.truck,
    );
  }

  final BarcodeStatus status;
  final Truck? truck;
  final String errorMessage;
}

enum BarcodeStatus {
  loading,
  load,
  actionFailure,
  actionSuccess,
}
