import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';

import 'package:ticking_api_client/ticking_api_client.dart';

import 'package:ticking_app/services/config_service/secure_config_service.dart';

part 'barcode_event.dart';

part 'barcode_state.dart';

class BarcodeBloc extends Bloc<BarcodeEvent, BarcodeState> {
  BarcodeBloc({
    SecureConfigService? secureConfigService,
  }) : super(const BarcodeState()) {
    on<BarcodeStarted>(_onStarted);

    _secureConfigService =
        secureConfigService ?? GetIt.I<SecureConfigService>();
  }

  late final SecureConfigService _secureConfigService;

  Future<void> _onStarted(
    BarcodeStarted event,
    Emitter<BarcodeState> emit,
  ) async {
    UserInfo? userInfo = _secureConfigService.userInfo;
    Truck? truck = userInfo?.truck;
    emit(state.copyWith(
      status: BarcodeStatus.load,
      truck: truck,
    ));
  }
}
