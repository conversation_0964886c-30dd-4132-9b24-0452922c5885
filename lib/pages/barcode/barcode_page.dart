import 'package:barcode_widget/barcode_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import '../../gen/assets.gen.dart';
import 'bloc/barcode_bloc.dart';

class BarcodePage extends StatefulWidget {
  const BarcodePage({Key? key}) : super(key: key);

  @override
  State<BarcodePage> createState() => _BarcodePageState();
}

class _BarcodePageState extends State<BarcodePage> {
  final BarcodeBloc _bloc = BarcodeBloc();

  @override
  void initState() {
    _bloc.add(BarcodeStarted());
    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _bloc,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: _buildAppbar(),
        body: Center(
          child: BlocBuilder<BarcodeBloc, BarcodeState>(
            buildWhen: (previous, current) =>
                previous.truck != current.truck ||
                previous.status != current.status,
            builder: (context, state) {
              Truck? truck = state.truck;
              String barcode = truck?.barcode ?? '';
              if (barcode.isNotEmpty) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    BarcodeWidget(
                      barcode: Barcode.code128(),
                      // Barcode type and settings
                      data: barcode,
                      // Content
                      width: 212,
                      height: 142,
                    ),
                    Text(
                      'Your Truck barcode',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: const Color(0xFF798287),
                            fontWeight: FontWeight.w500,
                          ),
                    ).pb(value: 8).pt(value: 16),
                    Text(
                      barcode,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: const Color(0xFF363636),
                            fontWeight: FontWeight.w900,
                          ),
                    ),
                  ],
                );
              }
              return Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icon.emptyBarcode.svg().pb(value: 16),
                  Text(
                    'No barcode',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: const Color(0xFF363636),
                          fontWeight: FontWeight.w900,
                          fontSize: 18,
                        ),
                  ),
                  Text(
                    truck == null
                        ? 'You have not been assigned to any Truck. Please contact your manager for more information!'
                        : 'Your truck has not been assigned to any barcode. Please contact your manager for more information!',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: const Color(0xFF798287),
                          fontWeight: FontWeight.w500,
                        ),
                    textAlign: TextAlign.center,
                  ).pr(value: 16).pl(value: 16).pt(value: 8),
                ],
              );
            },
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppbar() {
    return AppBar(
      title: Text(
        'Barcode',
        style: Theme.of(context)
            .textTheme
            .headline6
            ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
      ),
      centerTitle: true,
      backgroundColor: AppColors.background,
      actions: [
        Assets.icon.noAvatar.svg(width: 25, height: 25).pr(value: 10),
      ],
      leading: const BackButton(
        color: Colors.black,
      ),
    );
  }
}
