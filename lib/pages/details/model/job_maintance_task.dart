import 'package:ticking_api_client/ticking_api_client.dart';

class JobMaintenanceTask {
  JobMaintenanceTask({
    required this.jobTaskTemplate,
    required this.maintenanceTask,
    this.value,
    this.isChangedValue = false,
    // required this.images,
  });

  final JobTask jobTaskTemplate;
  final MaintenanceTask maintenanceTask;
  dynamic value;
  bool isChangedValue;

  Map<String, dynamic> toMap() {
    return {
      'jobTaskTemplate': jobTaskTemplate.toJson(),
      'maintenanceTask': maintenanceTask.toJson(),
      'value': value,
      'isChangedValue': isChangedValue,
    };
  }

  factory JobMaintenanceTask.fromMap(Map<String, dynamic> map) {
    // final imageList = map['images'];
    // List<String> images = [];
    // if (imageList is List) {
    //   images = imageList.map((image) => image.toString()).toList();
    // }
    return JobMaintenanceTask(
        jobTaskTemplate: JobTask.fromJson(
          Map<String, dynamic>.from(map['jobTaskTemplate']),
        ),
        maintenanceTask: MaintenanceTask.from<PERSON>son(
          Map<String, dynamic>.from(map['maintenanceTask']),
        ),
        value: map['value'],
        isChangedValue: map['isChangedValue']
        // images: images,
        );
  }
}
