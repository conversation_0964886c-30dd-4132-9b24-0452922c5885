import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';

class JobDetailsModel {
  final List<JobMaintenanceTask> jobMaintenanceTasks;

  final List<String> images;

  final List<JobEventRequest> jobEventRequests;

  const JobDetailsModel({
    required this.jobMaintenanceTasks,
    required this.images,
    required this.jobEventRequests,
  });

  JobDetailsModel copyWith({
    List<JobMaintenanceTask>? jobMaintenanceTasks,
    List<String>? images,
    List<JobEventRequest>? jobEventRequests,
  }) {
    return JobDetailsModel(
      jobMaintenanceTasks: jobMaintenanceTasks ?? this.jobMaintenanceTasks,
      images: images ?? this.images,
      jobEventRequests: jobEventRequests ?? this.jobEventRequests,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'jobMaintenanceTasks': jobMaintenanceTasks,
      'images': images,
      'jobEventRequests': jobEventRequests,
    };
  }

  factory JobDetailsModel.fromMap(Map<String, dynamic> map) {
    final jobMaintenanceJsonList = map['jobMaintenanceTasks'];
    List<JobMaintenanceTask> jobMaintenanceTasks = [];
    if (jobMaintenanceJsonList is List) {
      jobMaintenanceTasks = jobMaintenanceJsonList
          .map((json) => JobMaintenanceTask.fromMap(json))
          .toList();
    }

    final jobEventRequestJsonList = map['jobEventRequests'];
    List<JobEventRequest> jobEventRequests = [];
    if (jobEventRequestJsonList is List) {
      jobEventRequests = jobEventRequestJsonList
          .map((json) => JobEventRequest.fromJson(json))
          .toList();
    }

    final imageJsonList = map['images'];
    List<String> images = [];
    if (imageJsonList is List) {
      images = imageJsonList.map((json) => json.toString()).toList();
    }
    return JobDetailsModel(
      jobMaintenanceTasks: jobMaintenanceTasks,
      images: images,
      jobEventRequests: jobEventRequests,
    );
  }
}
