// import 'package:ticking_api_client/ticking_api_client.dart';
//
// class JobMaintenanceTask {
//   JobMaintenanceTask({
//     required this.jobTaskTemplate,
//     required this.maintenanceTask,
//     this.value,
//     // required this.images,
//   });
//
//   final JobTask jobTaskTemplate;
//   late final String id;
//   late final String templateId;
//   late final String jobId;
//   late final String name;
//   late final DateTime? lastDoneOn;
//   late final dynamic value;
//   late final bool isRequired;
//
//   // final List<String> images;
//
//   Map<String, dynamic> toMap() {
//     return {
//       'jobTaskTemplate': jobTaskTemplate.toJson(),
//       'maintenanceTask': maintenanceTask.toJson(),
//       'value': value,
//     };
//   }
//
//   factory JobMaintenanceTask.fromMap(Map<String, dynamic> map) {
//     // final imageList = map['images'];
//     // List<String> images = [];
//     // if (imageList is List) {
//     //   images = imageList.map((image) => image.toString()).toList();
//     // }
//     return JobMaintenanceTask(
//         jobTaskTemplate: JobTask.fromJson(
//           Map<String, dynamic>.from(map['jobTaskTemplate']),
//         ),
//         maintenanceTask: MaintenanceTask.fromJson(
//           Map<String, dynamic>.from(map['maintenanceTask']),
//         ),
//         value: map['value']
//         // images: images,
//         );
//   }
// }
