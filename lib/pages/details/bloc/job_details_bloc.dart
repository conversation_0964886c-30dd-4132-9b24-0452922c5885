import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:async/async.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:sentry_flutter/sentry_flutter.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/core/app_single_ton.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/services/cache_service/cache_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/services/task_service/model/job_create_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_delete_comment_task.dart';
import 'package:ticking_app/services/task_service/model/job_event_task.dart';
import 'package:ticking_app/services/task_service/model/job_reschedule_task.dart';
import 'package:ticking_app/services/task_service/model/job_update_comment_task.dart';
import 'package:ticking_app/services/task_service/task_service.dart';
import 'package:ticking_app/utils/dialog_helper.dart';
import 'package:ticking_app/utils/extend/view_extend.dart';
import 'package:ticking_log_service/ticking_log_service.dart';
import 'package:uuid/uuid.dart';
import 'package:uuid/uuid_util.dart';

part 'job_details_event.dart';

part 'job_details_state.dart';

class JobDetailsBloc extends Bloc<JobDetailsEvent, JobDetailsState> {
  JobDetailsBloc({
    required Job job,
    AuthApi? authApi,
    TickingLogService? logService,
    ApiClient? client,
    ConfigurationApi? configurationApi,
    CacheService? cacheService,
    JobApi? jobApi,
    LocationService? locationService,
    TaskService? taskService,
  }) : super(JobDetailsLoading(job)) {
    ///Get services

    _tickingLogService = logService ?? GetIt.I<TickingLogService>();
    _cacheService = cacheService ?? GetIt.I<CacheService>();
    _taskService = taskService ?? GetIt.I<TaskService>();
    _jobApi = jobApi ?? GetIt.I<JobApi>();
    _tickingLogService.setClassName(toString());
    _locationService = locationService ?? GetIt.I<LocationService>();
    _job = job;

    on<JobDetailsLoaded>(_onLoaded);
    on<JobDetailsReviewingLoaded>(_onReviewingLoaded);
    on<JobDetailsIndexChanged>(_onIndexChanged);
    on<JobDetailsCompleted>(_onJobDetailsCompleted);
    on<JobDetailsTimeExpired>(_onJobDetailsTimeExpired);
    on<JobDetailsDataChanged>(_onDataChanged);
    on<JobDetailsGpsUpdated>(_onGpsUpdated);
    on<JobDetailsUnableAccessed>(_onUnableAccessed);
    on<JobDetailsCommentPosted>(_onCommentPosted);
    on<JobDetailsEmailPosted>(_onEmailPosted);
    on<JobDetailsCommentDeleted>(_onCommentDeleted);
    on<JobDetailsCommentUpdated>(_onCommentUpdated);
    on<JobDetailsCustomerCommentPosted>(_onCustomerCommentPosted);
    on<JobDetailsCustomerCommentDeleted>(_onCustomerCommentDeleted);
    on<JobDetailsCustomerCommentUpdated>(_onCustomerCommentUpdated);
    on<JobDetailsTaskUpdated>(_onTaskUpdated);
    on<ImageInputsInserted>(_onImageInputsInserted);

    // on<JobDetailsRepairPosted>(_onRepairPosted);
  }

  late Job _job;
  late CacheService _cacheService;
  late LocationService _locationService;
  late TickingLogService _tickingLogService;
  late JobApi _jobApi;
  late final TaskService _taskService;
  CancelableOperation? _cancellableOperation;
  StreamSubscription? _taskCompleteStreamSubscription;
  StreamSubscription? _locationSubscription;

  // Future<void> _onRepairPosted(
  //   JobDetailsRepairPosted event,
  //   Emitter<JobDetailsState> emit,
  // ) async {
  //   try {
  //
  //     await _jobApi.requestRepair(
  //         jobId: event.job.id, description: event.description);
  //
  //   } catch (e, stack) {
  //     if (e is SocketException || e is ApiTimeoutException) {
  //       const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
  //       final now = DateTime.now();
  //       final taskId = '${uuid.v4()}_${now.millisecond.toString()}';
  //
  //       DataTask task = DataTask(
  //         taskId: taskId,
  //         task: JobRepairTask(
  //           description: event.description,
  //           jobId: event.job.id,
  //           images: [],
  //         ),
  //         type: TaskType.jobRepairTask,
  //       );
  //
  //       await _cacheService.addDataTask(task);
  //
  //       _taskService.addDataTask(task);
  //
  //       return;
  //     }
  //
  //     _tickingLogService.error('_onRepairPosted', e.toString(), stack);
  //   }
  // }

  Future<void> _onImageInputsInserted(
      ImageInputsInserted event,
      Emitter<JobDetailsState> emit,
      ) async {
    emit(JobDetailsBusy(state.data));

    Job job = state.data.job;
    List<JobEvent> newJobEvents = [];

    try {
      for (XFile file in event.files) {
        final File imageFile = File(file.path);
        final bytes = await imageFile.readAsBytes();
        final base64Image = base64Encode(bytes);

        // Simple mime type detection
        String mimeType = 'image/jpeg';
        if (file.path.toLowerCase().endsWith('.png')) {
          mimeType = 'image/png';
        }

        JobEventRequest jobEventRequest = JobEventRequest(
          date: DateTime.now(),
          reason: 'in_progress',
          description: event.description,
          image: JobImage(
            mimetype: mimeType,
            src: base64Image,
          ),
          jobType: job.type,
        );

        final result = await _jobApi.createJobEvents(
          id: job.id,
          request: jobEventRequest,
        );
        newJobEvents.add(result.data);
      }

      // Update job with new events
      List<JobEvent> allEvents = List.from(job.events);
      for (var event in newJobEvents) {
        allEvents.insert(0, event);
      }

      Job updatedJob = job.copyWith(events: allEvents);

      // Update cache
      await _cacheService.setJobStart(updatedJob);

      emit(JobDetailsLoadSuccess(
        state.data.copyWith(job: updatedJob, isBusy: false),
      ));

    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        // Handle offline mode - create tasks for each image
        List<JobEvent> tempJobEvents = [];

        for (XFile file in event.files) {
          final File imageFile = File(file.path);
          final bytes = await imageFile.readAsBytes();
          final base64Image = base64Encode(bytes);

          // Simple mime type detection
          String mimeType = 'image/jpeg';
          if (file.path.toLowerCase().endsWith('.png')) {
            mimeType = 'image/png';
          }

          JobEventRequest jobEventRequest = JobEventRequest(
            date: DateTime.now(),
            reason: 'in_progress',
            description: event.description,
            image: JobImage(
              mimetype: mimeType,
              src: base64Image,
            ),
            jobType: job.type,
          );

          // Create task for offline processing
          const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
          final now = DateTime.now();
          final taskId = '${uuid.v4()}_${now.millisecond.toString()}_eventTask';

          final task = DataTask(
            taskId: taskId,
            task: JobEventTask(
              request: jobEventRequest,
              jobId: job.id,
              mine: mimeType,
            ),
            type: TaskType.jobEvent,
          );

          // Add task to cache and service
          await _cacheService.addDataTask(task);
          _taskService.addDataTask(task);

          // Create temporary job event for immediate UI feedback
          // Using taskId as temporary ID to track offline events
          final tempJobEvent = JobEvent(
            id: taskId,
            jobId: job.id,
            employee: JobEmployee(
              name: '', // Will be filled when task completes
            ),
            image: JobImage(
              mimetype: mimeType,
              src: base64Image,
            ),
            reason: 'in_progress',
            date: now,
            description: event.description,
          );

          tempJobEvents.add(tempJobEvent);
        }

        // Update job with temporary events for immediate UI feedback
        List<JobEvent> allEvents = List.from(job.events);
        for (var tempEvent in tempJobEvents) {
          allEvents.insert(0, tempEvent);
        }

        // Also add image bytes to job.imageBytesList for immediate UI display
        List<Uint8List> imageBytesList = List.from(job.imageBytesList);
        for (XFile file in event.files) {
          final File imageFile = File(file.path);
          final bytes = await imageFile.readAsBytes();
          imageBytesList.insert(0, bytes);
        }

        Job updatedJob = job.copyWith(
          events: allEvents,
          imageBytesList: imageBytesList,
        );

        // Update cache
        await _cacheService.setJobStart(updatedJob);

        emit(JobDetailsLoadSuccess(
          state.data.copyWith(job: updatedJob, isBusy: false),
        ));

        return;
      }

      _tickingLogService.error('JobDetailsImageInputsInserted', e.toString(), stack);
      emit(JobDetailsError(
        data: state.data.copyWith(isBusy: false),
        error: e.toString(),
      ));
    }
  }


  Future<void> _onTaskUpdated(
    JobDetailsTaskUpdated event,
    Emitter<JobDetailsState> emit,
  ) async {
    final dataTask = event.task;
    final job = state.data.job;
    switch (dataTask.task.runtimeType) {
      case JobCreateCommentTask:
        // final jobCommentTask = dataTask.task as JobCreateCommentTask;
        final commentId = dataTask.taskId;
        // final jobId = jobCommentTask.jobId;

        final jobComment = dataTask.completeData as JobComment;
        final comments = job.comments;

        final findCommentIndex =
            comments.indexWhere((element) => element.id == commentId);

        if (findCommentIndex > -1) {
          comments[findCommentIndex] = jobComment;
        }

        _cacheService.setJobStart(job);

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: job),
          ),
        );

        break;
      case JobEventTask:
        // Handle completed job event task
        final eventTaskId = dataTask.taskId;

        // Check if task has completed data
        if (dataTask.completeData != null) {
          final jobEvent = dataTask.completeData as JobEvent;
          final events = job.events;

          // Find and replace the temporary event with the real one
          final findEventIndex =
              events.indexWhere((element) => element.id == eventTaskId);

          if (findEventIndex > -1) {
            events[findEventIndex] = jobEvent;
          }

          _cacheService.setJobStart(job);

          emit(
            JobDetailsLoadSuccess(
              state.data.copyWith(job: job),
            ),
          );
        }

        break;
    }
  }

  Future<void> _onCommentUpdated(
    JobDetailsCommentUpdated event,
    Emitter<JobDetailsState> emit,
  ) async {
    final job = state.data.job;
    final commentIndex = job.comments.indexWhere(
      (element) => element == event.comment,
    );
    try {
      JobComment comment;
      ApiResult<JobComment> commentResult;
      if (!event.comment.id.contains('commentTask')) {
        commentResult = await _jobApi.updateJobComment(
          jobId: event.job.id,
          commentId: event.comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      } else {
        commentResult = await _jobApi.createJobComment(
          jobId: event.job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      }

      comment = commentResult.data;

      if (commentIndex >= 0) {
        job.comments[commentIndex] = job.comments[commentIndex].copyWith(
          body: event.body,
          id: comment.id,
        );
      }

      _cacheService.setJobStart(job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);
        late DataTask task;

        if (event.comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.createJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] = job.comments[commentIndex]
                .copyWith(body: event.body, id: taskId);
          }
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: event.comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] =
                job.comments[commentIndex].copyWith(body: event.body);
          }
        }

        // final task = DataTask(
        //   taskId: taskId,
        //   task: JobUpdateCommentTask(
        //     commentId: event.comment.id,
        //     request: JobCommentRequest(
        //       body: event.body,
        //       date: DateTime.now(),
        //     ),
        //     jobId: event.job.id,
        //   ),
        //   type: TaskType.createJobComment,
        // );

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: job),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onCommentUpdated', e.toString(), stack);
    }
  }

  Future<void> _onCommentDeleted(
    JobDetailsCommentDeleted event,
    Emitter<JobDetailsState> emit,
  ) async {
    final job = state.data.job;

    try {
      if (!event.comment.id.contains('commentTask')) {
        await _jobApi.deleteJobComment(
          jobId: job.id,
          dateTime: DateTime.now(),
          commentId: event.comment.id,
        );
      }

      await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

      job.comments.remove(event.comment);

      _cacheService.setJobStart(job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: _job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(
            state.data.job.id, event.comment.id);

        job.comments.remove(event.comment);

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: job),
          ),
        );

        _cacheService.setJobStart(job);

        if (event.comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: event.comment.id,
            dateTime: now,
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onCommentDeleted', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentUpdated(
    JobDetailsCustomerCommentUpdated event,
    Emitter<JobDetailsState> emit,
  ) async {
    final job = state.data.job;
    final commentIndex = job.customerComments.indexWhere(
      (element) => element == event.comment,
    );
    try {
      JobComment comment;
      ApiResult<JobComment> commentResult;
      if (!event.comment.id.contains('commentTask')) {
        commentResult = await _jobApi.updateJobCustomerComment(
          jobId: event.job.id,
          commentId: event.comment.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      } else {
        commentResult = await _jobApi.postJobCustomerComment(
          jobId: event.job.id,
          request: JobCommentRequest(
            body: event.body,
            date: DateTime.now(),
          ),
        );
      }

      comment = commentResult.data;

      if (commentIndex >= 0) {
        job.customerComments[commentIndex] =
            job.customerComments[commentIndex].copyWith(
          body: event.body,
          id: comment.id,
        );
      }

      _cacheService.setJobStart(job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(event.job.id, event.comment.id);
        late DataTask task;

        if (event.comment.id.contains('commentTask')) {
          task = DataTask(
            taskId: taskId,
            task: JobCreateCommentTask(
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.createJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] = job.comments[commentIndex]
                .copyWith(body: event.body, id: taskId);
          }
        } else {
          task = DataTask(
            taskId: taskId,
            task: JobUpdateCommentTask(
              commentId: event.comment.id,
              request: JobCommentRequest(
                body: event.body,
                date: now,
              ),
              jobId: event.job.id,
            ),
            type: TaskType.updateJobComment,
          );

          if (commentIndex >= 0) {
            job.comments[commentIndex] =
                job.comments[commentIndex].copyWith(body: event.body);
          }
        }

        // final task = DataTask(
        //   taskId: taskId,
        //   task: JobUpdateCommentTask(
        //     commentId: event.comment.id,
        //     request: JobCommentRequest(
        //       body: event.body,
        //       date: DateTime.now(),
        //     ),
        //     jobId: event.job.id,
        //   ),
        //   type: TaskType.createJobComment,
        // );

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: job),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onCommentUpdated', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentDeleted(
    JobDetailsCustomerCommentDeleted event,
    Emitter<JobDetailsState> emit,
  ) async {
    final job = state.data.job;

    try {
      if (!event.comment.id.contains('commentTask')) {
        await _jobApi.deleteJobCustomerComment(
          jobId: job.id,
          dateTime: DateTime.now(),
          commentId: event.comment.id,
        );
      }

      await _taskService.removeCommentJobTask(event.job.id, event.comment.id);

      job.customerComments.remove(event.comment);

      _cacheService.setJobStart(job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: _job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        const uuid = Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        await _taskService.removeCommentJobTask(
            state.data.job.id, event.comment.id);

        job.comments.remove(event.comment);

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: job),
          ),
        );

        _cacheService.setJobStart(job);

        if (event.comment.id.contains('commentTask')) {
          return;
        }

        final task = DataTask(
          taskId: taskId,
          task: JobDeleteCommentTask(
            commentId: event.comment.id,
            dateTime: now,
            jobId: event.job.id,
          ),
          type: TaskType.createJobComment,
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onCommentDeleted', e.toString(), stack);
    }
  }

  Future<void> _onEmailPosted(
    JobDetailsEmailPosted event,
    Emitter<JobDetailsState> emit,
  ) async {}

  Future<void> _onCommentPosted(
    JobDetailsCommentPosted event,
    Emitter<JobDetailsState> emit,
  ) async {
    final jobModels = _cacheService.getJobList;

    final jobIndex =
        jobModels.indexWhere((element) => element.job.id == _job.id);

    try {
      final result = await _jobApi.createJobComment(
        jobId: _job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final comments = List<JobComment>.from(_job.comments);

      comments.insert(0, result.data);
      _job = _job.copyWith(comments: comments);

      if (jobIndex > 0) {
        jobModels[jobIndex] = jobModels[jobIndex].copyWith(
          job: _job,
        );

        await _cacheService.updateJobModels(jobModels);
      }

      _cacheService.setJobStart(_job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: _job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(body: event.comment, date: now),
            jobId: _job.id,
          ),
          type: TaskType.createJobComment,
        );

        final comments = List<JobComment>.from(_job.comments);
        comments.insert(
          0,
          JobComment(
            date: DateTime.now(),
            jobId: _job.id,
            employee: _job.employee,
            body: event.comment,
            id: taskId,
          ),
        );

        _job = _job.copyWith(comments: comments);

        if (jobIndex > 0) {
          jobModels[jobIndex] = jobModels[jobIndex].copyWith(job: _job);

          await _cacheService.updateJobModels(jobModels);
        }

        _cacheService.setJobStart(_job);

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: _job),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onCustomerCommentPosted(
    JobDetailsCustomerCommentPosted event,
    Emitter<JobDetailsState> emit,
  ) async {
    final jobModels = _cacheService.getJobList;

    final jobIndex =
        jobModels.indexWhere((element) => element.job.id == _job.id);

    try {
      final result = await _jobApi.postJobCustomerComment(
        jobId: _job.id,
        request: JobCommentRequest(
          body: event.comment,
          date: DateTime.now(),
        ),
      );

      final comments = List<JobComment>.from(_job.customerComments);

      comments.insert(0, result.data);
      _job = _job.copyWith(customerComments: comments);

      if (jobIndex > 0) {
        jobModels[jobIndex] = jobModels[jobIndex].copyWith(
          job: _job,
        );

        await _cacheService.updateJobModels(jobModels);
      }

      _cacheService.setJobStart(_job);

      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(job: _job),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}_commentTask';

        final task = DataTask(
          taskId: taskId,
          task: JobCreateCommentTask(
            request: JobCommentRequest(body: event.comment, date: now),
            jobId: _job.id,
          ),
          type: TaskType.createJobComment,
        );

        final comments = List<JobComment>.from(_job.comments);
        comments.insert(
          0,
          JobComment(
            date: DateTime.now(),
            jobId: _job.id,
            employee: _job.employee,
            body: event.comment,
            id: taskId,
          ),
        );

        _job = _job.copyWith(comments: comments);

        if (jobIndex > 0) {
          jobModels[jobIndex] = jobModels[jobIndex].copyWith(job: _job);

          await _cacheService.updateJobModels(jobModels);
        }

        _cacheService.setJobStart(_job);

        emit(
          JobDetailsLoadSuccess(
            state.data.copyWith(job: _job),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data,
          error: e.toString(),
        ),
      );

      _tickingLogService.error('_onJobPostComment', e.toString(), stack);
    }
  }

  Future<void> _onUnableAccessed(
    JobDetailsUnableAccessed event,
    Emitter<JobDetailsState> emit,
  ) async {
    try {
      emit(
        JobDetailsLoadSuccess(
          state.data.copyWith(
            isBusy: true,
          ),
        ),
      );

      await _jobApi.unableToAccess(
        id: _job.id,
        request: JobReschedulingRequest(
          reason: event.reason,
          date: DateTime.now(),
        ),
      );

      final jobModels = _cacheService.getJobList;

      jobModels.removeWhere((element) => element.job.id == _job.id);

      await _cacheService.updateJobModels(jobModels);

      await _cacheService.setJobStart(null);
      await _cacheService.setJobDateStart(null);

      emit(
        JobDetailsUnableAccessSuccess(
          state.data.copyWith(
            isBusy: false,
          ),
        ),
      );
    } catch (e, stack) {
      if (e is SocketException || e is ApiTimeoutException) {
        var uuid = const Uuid(options: {'grng': UuidUtil.cryptoRNG});
        final now = DateTime.now();
        final taskId = '${uuid.v4()}_${now.millisecond.toString()}';

        final task = DataTask(
          taskId: taskId,
          task: JobRescheduleTask(
            request: JobReschedulingRequest(
              date: now,
              reason: event.reason,
            ),
            jobId: _job.id,
          ),
          type: TaskType.jobReschedule,
        );

        final jobModels = _cacheService.getJobList;
        jobModels.removeWhere((element) => element.job.id == _job.id);
        await _cacheService.updateJobModels(jobModels);

        await _cacheService.setJobStart(null);
        await _cacheService.setJobDateStart(null);

        emit(
          JobDetailsUnableAccessSuccess(
            state.data.copyWith(
              isBusy: false,
            ),
          ),
        );

        await _cacheService.addDataTask(task);

        _taskService.addDataTask(task);

        return;
      }

      emit(
        JobDetailsError(
          data: state.data.copyWith(
            isBusy: false,
          ),
          error: e.toString(),
        ),
      );
      if (!kDebugMode) {
        await Sentry.captureException(e, stackTrace: stack);
      }
      _tickingLogService.error('_onUnableToAccess', e.toString(), stack);
    }
  }

  Future<void> _onGpsUpdated(
    JobDetailsGpsUpdated event,
    Emitter<JobDetailsState> emit,
  ) async {
    emit(
      JobDetailsLoadSuccess(
        state.data.copyWith(
          currentLocation: event.gps,
        ),
      ),
    );
  }

  Future<void> _onDataChanged(
    JobDetailsDataChanged event,
    Emitter<JobDetailsState> emit,
  ) async {
    await _cancellableOperation?.cancel();
    _cancellableOperation = CancelableOperation.fromFuture(
      Future.microtask(() async {
        List<JobMaintenanceTask> jobMaintenanceTasks =
            state.data.maintenanceTasks;
        int index = jobMaintenanceTasks.indexWhere((element) =>
            element.maintenanceTask.id ==
            event.jobMaintenanceTask.maintenanceTask.id);

        if (index != -1) {
          jobMaintenanceTasks[index].value = event.value;
          jobMaintenanceTasks[index].isChangedValue = event.isChangedValue;
        }

        for (int i = 0; i < jobMaintenanceTasks.length; i++) {
          String? constraints =
              jobMaintenanceTasks[i].jobTaskTemplate.constraints;
          if (constraints != null &&
              jobMaintenanceTasks[index].jobTaskTemplate.inputType !=
                  JobTaskInputType.checkbox &&
              jobMaintenanceTasks[index].jobTaskTemplate.inputType !=
                  JobTaskInputType.selection) {
            /// a<=b
            ///get job of a when slide b
            JobMaintenanceTask? job =
                _jobMaintenanceTask(constraints, jobMaintenanceTasks, 0);

            int minConstraintIndex = jobMaintenanceTasks.indexWhere((element) =>
                element.jobTaskTemplate.id == job?.jobTaskTemplate.id);

            try {
              if (event.value <=
                  jobMaintenanceTasks[minConstraintIndex].value) {
                jobMaintenanceTasks[minConstraintIndex].value = event.value;
                jobMaintenanceTasks[minConstraintIndex].isChangedValue =
                    event.isChangedValue;
              }
            } catch (e) {
              print(e.toString());
              break;
            }

            if (jobMaintenanceTasks[index].jobTaskTemplate.id !=
                    job?.jobTaskTemplate.id &&
                minConstraintIndex != -1) {
              jobMaintenanceTasks[minConstraintIndex]
                  .jobTaskTemplate
                  .maxConstraintValue = event.value;
            }
          }
        }

        _cacheService.setJobTaskMaintenance(jobMaintenanceTasks);

        // emit(
        //   JobDetailsUpdateSuccess(
        //     job: data.job,
        //     maintenanceTasks: jobMaintenanceTasks,
        //     taskId: jobMaintenanceTasks[index].jobTaskTemplate.id,
        //     maxValue: event.value,
        //     isChangedValue: event.isChangedValue,
        //     jobMaintenanceTask: jobMaintenanceTasks[index],
        //     listState: ListState.update,
        //   ),
        // );

        emit(
          JobDetailsUpdateSuccess(
            data: state.data.copyWith(
                listState: ListState.update,
                maxValue: event.value,
                isChangedValue: event.isChangedValue,
                maintenanceTasks: jobMaintenanceTasks),
            task: jobMaintenanceTasks[index],
            jobMaintenanceTask: jobMaintenanceTasks[index],
          ),
        );
      }),
      onCancel: () => {},
    );

    await _cancellableOperation?.value;
  }

  JobMaintenanceTask? _jobMaintenanceTask(String constraints,
      List<JobMaintenanceTask> jobMaintenanceTasks, int index) {
    List<String> _char = constraints.split(' ');
    String _idMaxConstraint =
        _char[index].replaceAll('{', '').replaceAll('}', '');

    final findIndex = jobMaintenanceTasks.indexWhere(
        (element) => element.jobTaskTemplate.id == _idMaxConstraint);

    if (findIndex > -1) {
      JobMaintenanceTask? job = jobMaintenanceTasks[findIndex];
      return job;
    }

    return null;
  }

  dynamic _convertValue(JobTask jobTask) {
    if (jobTask.inputType == JobTaskInputType.number) {
      // return double.parse(jobTask.defaultValue ?? '0');
      return double.parse('0');
    } else if (jobTask.inputType == JobTaskInputType.checkbox) {
      return jobTask.defaultValue?.toLowerCase() == 'true';
    } else if (jobTask.inputType == JobTaskInputType.selection) {
      return jobTask.defaultValue;
    } else if (jobTask.inputType == JobTaskInputType.calculation) {
      return double.parse(jobTask.defaultValue ?? '0');
    }
    return null;
  }

  Future<void> _onReviewingLoaded(
    JobDetailsReviewingLoaded event,
    Emitter<JobDetailsState> emit,
  ) async {
    // if (_cacheService.jobDateStart == null || _cacheService.jobStart == null) {
    //   _cacheService.setJobDateStart(DateTime.now());
    // }

    // Gps? curLocation;
    // try {
    //   await _locationService.requestPermission();
    //
    //   final bool isGpsEnabled =
    //   await _locationService.isLocationServiceEnabled();
    //
    //   if (!isGpsEnabled) {
    //     throw Exception(
    //       'Can not get your location. Please enable GPS and press refresh button!',
    //     );
    //   }
    //
    //   curLocation = await _locationService.getCurrentPosition();
    // } catch (_) {}

    _taskCompleteStreamSubscription?.cancel();
    _taskCompleteStreamSubscription =
        _taskService.taskCompleteStream.listen((task) {
      add(JobDetailsTaskUpdated(task));
    });

    Job? jobStart = _cacheService.jobStart;

    final taskTemplates = _cacheService.getJobTaskTemplates;

    final smsTemplate = _cacheService.smsTemplate;

    List<MaintenanceTask> maintenanceTasks = _job.details.maintenanceTasks;

    List<JobMaintenanceTask> jobMaintenanceTasks = <JobMaintenanceTask>[];

    for (final maintenanceTask in maintenanceTasks) {
      final matchTemplateIndex = taskTemplates
          .indexWhere((template) => template.id == maintenanceTask.templateId);
      if (matchTemplateIndex != -1) {
        final jobMaintenanceTask = JobMaintenanceTask(
          jobTaskTemplate: taskTemplates[matchTemplateIndex],
          maintenanceTask: maintenanceTask,
          value: maintenanceTask.value,
          isChangedValue: false,
        );

        jobMaintenanceTasks.add(jobMaintenanceTask);
      }
    }
    _cacheService.setJobTaskMaintenance(jobMaintenanceTasks);

    List<String> _groupNameList = [];
    final Map<String, List<JobMaintenanceTask>> jobTaskGroup =
        jobMaintenanceTasks.groupBy((m) {
      if (!_groupNameList
          .any((element) => element == m.jobTaskTemplate.group)) {
        _groupNameList.add(m.jobTaskTemplate.group);
      }

      return m.jobTaskTemplate.group;
    });

    for (int i = 0; i < jobMaintenanceTasks.length; i++) {
      String? constraints = jobMaintenanceTasks[i].jobTaskTemplate.constraints;
      if (constraints != null) {
        JobMaintenanceTask? job =
            _jobMaintenanceTask(constraints, jobMaintenanceTasks, 2);
        jobMaintenanceTasks[i].jobTaskTemplate.maxConstraintValue = job?.value;
      }
    }

    // final now = DateTime.now();
    // final isTimeExpires = ((_job.details.timer) -
    //         now.difference(_cacheService.jobDateStart ?? now).inSeconds)
    //     .toInt();
    emit(
      JobDetailsLoadSuccess(
        state.data.copyWith(
          maintenanceTasks: jobMaintenanceTasks,
          jobTaskGroup: jobTaskGroup,
          groupNameList: _groupNameList,
          jobDateStart: _cacheService.jobDateStart,
          job: _cacheService.jobStart,
          listState: ListState.scroll,
          smsTemplate: smsTemplate,
          // currentLocation: curLocation,
          isBusy: false,
          // isTimeExpires: isTimeExpires <= 0,
        ),
      ),
    );

    _locationSubscription?.cancel();
    _locationSubscription =
        _locationService.onCurrentPositionChanged.listen((gps) {
      final now = DateTime.now();
      if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
        _lastLocationUpdateTime = now;
        add(JobDetailsGpsUpdated(gps));
      }
    });
  }

  ///Load data event
  Future<void> _onLoaded(
    JobDetailsLoaded event,
    Emitter<JobDetailsState> emit,
  ) async {
    if (_cacheService.jobDateStart == null || _cacheService.jobStart == null) {
      _cacheService.setJobDateStart(DateTime.now());
    }

    Gps? curLocation;
    try {
      await _locationService.requestPermission();

      final bool isGpsEnabled =
          await _locationService.isLocationServiceEnabled();

      if (!isGpsEnabled) {
        throw Exception(
          'Can not get your location. Please enable GPS and press refresh button!',
        );
      }

      curLocation = await _locationService.getCurrentPosition();
    } catch (_) {}

    _taskCompleteStreamSubscription?.cancel();
    _taskCompleteStreamSubscription =
        _taskService.taskCompleteStream.listen((task) {
      add(JobDetailsTaskUpdated(task));
    });

    Job? jobStart = _cacheService.jobStart;

    final taskTemplates = _cacheService.getJobTaskTemplates;

    final smsTemplate = _cacheService.smsTemplate;

    List<MaintenanceTask> maintenanceTasks = _job.details.maintenanceTasks;

    List<JobMaintenanceTask> jobMaintenanceTasks = <JobMaintenanceTask>[];
    if (jobStart != null) {
      jobMaintenanceTasks = _cacheService.getJobTaskMaintenances;
    } else {
      _cacheService.setJobStart(_job);
      for (final maintenanceTask in maintenanceTasks) {
        final matchTemplateIndex = taskTemplates.indexWhere(
            (template) => template.id == maintenanceTask.templateId);
        if (matchTemplateIndex != -1) {
          final jobMaintenanceTask = JobMaintenanceTask(
              jobTaskTemplate: taskTemplates[matchTemplateIndex],
              maintenanceTask: maintenanceTask,
              value: _convertValue(taskTemplates[matchTemplateIndex]),
              isChangedValue: false);

          jobMaintenanceTasks.add(jobMaintenanceTask);
        }
      }
      _cacheService.setJobTaskMaintenance(jobMaintenanceTasks);
    }

    List<String> _groupNameList = [];
    final Map<String, List<JobMaintenanceTask>> jobTaskGroup =
        jobMaintenanceTasks.groupBy((m) {
      if (!_groupNameList
          .any((element) => element == m.jobTaskTemplate.group)) {
        _groupNameList.add(m.jobTaskTemplate.group);
      }

      return m.jobTaskTemplate.group;
    });

    for (int i = 0; i < jobMaintenanceTasks.length; i++) {
      String? constraints = jobMaintenanceTasks[i].jobTaskTemplate.constraints;
      if (constraints != null) {
        JobMaintenanceTask? job =
            _jobMaintenanceTask(constraints, jobMaintenanceTasks, 2);
        jobMaintenanceTasks[i].jobTaskTemplate.maxConstraintValue = job?.value;
      }
    }

    final now = DateTime.now();
    final isTimeExpires = ((_job.details.timer) -
            now.difference(_cacheService.jobDateStart ?? now).inSeconds)
        .toInt();
    emit(
      JobDetailsLoadSuccess(
        state.data.copyWith(
          maintenanceTasks: jobMaintenanceTasks,
          jobTaskGroup: jobTaskGroup,
          groupNameList: _groupNameList,
          jobDateStart: _cacheService.jobDateStart,
          job: _cacheService.jobStart,
          listState: ListState.scroll,
          smsTemplate: smsTemplate,
          currentLocation: curLocation,
          isBusy: false,
          isTimeExpires: isTimeExpires <= 0,
        ),
      ),
    );

    _locationSubscription?.cancel();
    _locationSubscription =
        _locationService.onCurrentPositionChanged.listen((gps) {
      final now = DateTime.now();
      if (now.difference(_lastLocationUpdateTime).inSeconds >= 30) {
        _lastLocationUpdateTime = now;
        add(JobDetailsGpsUpdated(gps));
      }
    });
  }

  DateTime _lastLocationUpdateTime = DateTime.now();

  Future<void> _onIndexChanged(
    JobDetailsIndexChanged event,
    Emitter<JobDetailsState> emit,
  ) async {
    emit(
      JobDetailsLoadSuccess(
        state.data.copyWith(
          groupIndex: event.index,
          listState: ListState.scroll,
        ),
      ),
    );
  }

  Future<void> _onJobDetailsCompleted(
    JobDetailsCompleted event,
    Emitter<JobDetailsState> emit,
  ) async {
    try {
      List<JobTaskRequest> tasks = [];
      final maintenanceTasks = state.data.maintenanceTasks;
      for (int i = 0; i < maintenanceTasks.length; i++) {
        //Check required
        if (maintenanceTasks[i].maintenanceTask.isRequired &&
            !maintenanceTasks[i].isChangedValue &&
            maintenanceTasks[i].jobTaskTemplate.inputType !=
                JobTaskInputType.selection) {
          emit(
            JobDetailsError(
              data: state.data,
              error: 'Please select all required reports',
            ),
          );
          return;
        }

        //Check required selection
        if (maintenanceTasks[i].maintenanceTask.isRequired &&
            (maintenanceTasks[i].jobTaskTemplate.inputType ==
                    JobTaskInputType.selection ||
                maintenanceTasks[i].jobTaskTemplate.inputType ==
                    JobTaskInputType.checkbox)) {
          final mtTask = maintenanceTasks[i].maintenanceTask;
          List<String> domain = [];
          final name=maintenanceTasks[i].maintenanceTask.name;
          if (maintenanceTasks[i].jobTaskTemplate.inputType ==
              JobTaskInputType.checkbox) {
            var listDomain = [];
            if (mtTask.domain.isEmpty) {
              listDomain = maintenanceTasks[i].jobTaskTemplate.domain;
            } else {
              listDomain = mtTask.domain;
            }
            for (var item in listDomain) {
              if (item.toUpperCase() == "TRUE") {
                domain.add('Yes');
              } else if (item.toUpperCase() == "FALSE") {
                domain.add('No');
              } else {
                domain.add(item);
              }
            }
          } else {
            if (mtTask.domain.isEmpty) {
              domain = maintenanceTasks[i].jobTaskTemplate.domain;
            } else {
              domain = mtTask.domain;
            }
          }
          if (!domain.contains(maintenanceTasks[i].value)) {
            emit(
              JobDetailsError(
                data: state.data,
                error:
                    'Selected option for ${maintenanceTasks[i].maintenanceTask.name} is not valid. Please try again.',
              ),
            );
            return;
          }
        }

        //Add Task
        if (maintenanceTasks[i].isChangedValue ||
            maintenanceTasks[i].jobTaskTemplate.inputType ==
                JobTaskInputType.calculation) {
          final task = maintenanceTasks[i];

          if (task.jobTaskTemplate.inputType == "checkbox") {
            if (maintenanceTasks[i].value != '') {
              tasks.add(
                JobTaskRequest(
                  id: maintenanceTasks[i].maintenanceTask.id,
                  value: maintenanceTasks[i].value.toString().toUpperCase() ==
                          "YES"
                      ? true
                      : maintenanceTasks[i].value.toString().toUpperCase() ==
                              "NO"
                          ? false
                          : maintenanceTasks[i].value,
                ),
              );
            }
          } else {
            tasks.add(
              JobTaskRequest(
                id: maintenanceTasks[i].maintenanceTask.id,
                value: maintenanceTasks[i].value,
              ),
            );
          }
        }
      }

      for (JobMaintenanceTask item in (maintenanceTasks ?? [])) {
        if (item.jobTaskTemplate.inputType == JobTaskInputType.number) {
          print(
              "${item.maintenanceTask.name}:${item.value}/${item.isChangedValue}");
          final domains = item.jobTaskTemplate.domain;
          if (item.isChangedValue == true &&
              (double.parse(item.value.toString()) < double.parse(domains[0]) ||
                  double.parse(item.value.toString()) >
                      double.parse(domains[2]))) {
            DialogHelper.showError(
                content:
                    "Some maintenance report items are out of the Min/Max value");
            return;
          }
        }
      }



      final dateStart = state.data.jobDateStart;
      final now = DateTime.now();

      final JobTaskListRequest jobTaskListRequest = JobTaskListRequest(
        reason: event.request.reason,
        startDate: dateStart,
        endDate: now,
        tasks: tasks,
      );

      // final result = await _jobApi.completeJob(
      //   id: _job.id,
      //   request: JobTaskListRequest(
      //     reason: event.request.reason,
      //     duration: event.request.duration,
      //     date: event.request.date,
      //     tasks: tasks,
      //   ),
      // );
      // final data = state.data;

      //COMMENT
      emit(
        JobDetailsCompleteSuccess(
          data: state.data,
          jobTaskListRequest: jobTaskListRequest,
        ),
      );

      // emit(state.copyWith(isCompleted: result, listState: ListState.scroll));
    } catch (e) {
      // DialogHelper.showError(content: e.toString());
      // emit(state.copyWith(isCompleted: true, listState: ListState.scroll));
    }
    // emit(state.copyWith(isBusy: false, listState: ListState.scroll));
  }

  Future<void> _onJobDetailsTimeExpired(
    JobDetailsTimeExpired event,
    Emitter<JobDetailsState> emit,
  ) async {
    emit(
      JobDetailsLoadSuccess(
        state.data.copyWith(
          isTimeExpires: true,
          listState: ListState.scroll,
        ),
      ),
    );
  }

  @override
  Future<void> close() {
    _locationSubscription?.cancel();
    _taskCompleteStreamSubscription?.cancel();
    return super.close();
  }
}
