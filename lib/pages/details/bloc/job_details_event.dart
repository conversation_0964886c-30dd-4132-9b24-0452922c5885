part of 'job_details_bloc.dart';

@immutable
abstract class JobDetailsEvent {}

class JobDetailsLoaded extends JobDetailsEvent {}
class JobDetailsReviewingLoaded extends JobDetailsEvent {}
class JobDetailsIndexChanged extends JobDetailsEvent {
  JobDetailsIndexChanged({required this.index});

  final int index;
}

class JobDetailsGpsUpdated extends JobDetailsEvent {
  final Gps gps;

  JobDetailsGpsUpdated(this.gps);
}

//Insert image to screen
class ImageInputsInserted extends JobDetailsEvent {
  ImageInputsInserted(
      {required this.files, required this.index, required this.description});

  final List<XFile> files;
  final int index;
  final String description;

  @override
  String toString() {
    return 'RepairTicketImagesInserted{url: $files}';
  }
}

class JobDetailsUnableAccessed extends JobDetailsEvent {
  final String reason;

  JobDetailsUnableAccessed(this.reason);
}

class JobDetailsTaskUpdated extends JobDetailsEvent {
  final DataTask task;

  JobDetailsTaskUpdated(this.task);
}

class <PERSON>DetailsCompleted extends JobDeta<PERSON>Event {
  JobDetailsCompleted({required this.request});

  final JobTaskListRequest request;
}

class JobDetailsCommentPosted extends JobDetailsEvent {
  JobDetailsCommentPosted(this.comment);

  final String comment;
}

class JobDetailsEmailPosted extends JobDetailsEvent {
  JobDetailsEmailPosted(this.reason);

  final String reason;
}

class JobDetailsCustomerCommentPosted extends JobDetailsEvent {
  JobDetailsCustomerCommentPosted(this.comment);

  final String comment;
}


class JobDetailsTimeExpired extends JobDetailsEvent {}

class JobDetailsDataChanged extends JobDetailsEvent {
  JobDetailsDataChanged({
    required this.jobMaintenanceTask,
    required this.value,
    required this.isChangedValue,
  });

  final JobMaintenanceTask jobMaintenanceTask;
  final dynamic value;
  final bool isChangedValue;
}


class JobDetailsCommentDeleted extends JobDetailsEvent {
  final JobComment comment;
  final Job job;

  JobDetailsCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class JobDetailsCommentUpdated extends JobDetailsEvent {
  final JobComment comment;

  final String body;
  final Job job;

  JobDetailsCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}


class JobDetailsCustomerCommentDeleted extends JobDetailsEvent {
  final JobComment comment;
  final Job job;

  JobDetailsCustomerCommentDeleted({
    required this.comment,
    required this.job,
  });
}

class JobDetailsCustomerCommentUpdated extends JobDetailsEvent {
  final JobComment comment;

  final String body;
  final Job job;

  JobDetailsCustomerCommentUpdated({
    required this.comment,
    required this.body,
    required this.job,
  });
}


// class JobDetailsRepairPosted extends JobDetailsEvent {
//   final Job job;
//   final String description;
//
//   JobDetailsRepairPosted({
//     required this.job,
//     required this.description,
//   });
// }