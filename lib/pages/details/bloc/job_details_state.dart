part of 'job_details_bloc.dart';

class JobDetailsData {
  const JobDetailsData({
    required this.job,
    required this.maintenanceTasks,
    required this.isBusy,
    required this.jobTaskGroup,
    required this.groupNameList,
    required this.groupIndex,
    this.jobDateStart,
    required this.isCompleted,
    required this.isTimeExpires,
    required this.maxValue,
    required this.isChangedValue,
    required this.listState,
    required this.currentLocation,
    this.smsTemplate,
  });

  final Job job;
  final bool isBusy;
  final List<JobMaintenanceTask> maintenanceTasks;
  final Map<String, List<JobMaintenanceTask>> jobTaskGroup;
  final List<String> groupNameList;
  final int groupIndex;
  final DateTime? jobDateStart;
  final bool isCompleted;
  final bool isTimeExpires;
  final dynamic maxValue;
  final bool isChangedValue;
  final ListState listState;
  final Gps? currentLocation;
  final SmsTemplate? smsTemplate;

  JobDetailsData copyWith({
    Job? job,
    bool? isBusy,
    List<JobMaintenanceTask>? maintenanceTasks,
    Map<String, List<JobMaintenanceTask>>? jobTaskGroup,
    List<String>? groupNameList,
    int? groupIndex,
    DateTime? jobDateStart,
    bool? isCompleted,
    bool? isTimeExpires,
    dynamic maxValue,
    bool? isChangedValue,
    ListState? listState,
    Gps? currentLocation,
    SmsTemplate? smsTemplate,
  }) {
    return JobDetailsData(
      job: job ?? this.job,
      smsTemplate: smsTemplate ?? this.smsTemplate,
      currentLocation: currentLocation ?? this.currentLocation,
      isBusy: isBusy ?? this.isBusy,
      maintenanceTasks: maintenanceTasks ?? this.maintenanceTasks,
      jobTaskGroup: jobTaskGroup ?? this.jobTaskGroup,
      groupNameList: groupNameList ?? this.groupNameList,
      groupIndex: groupIndex ?? this.groupIndex,
      jobDateStart: jobDateStart ?? this.jobDateStart,
      isCompleted: isCompleted ?? this.isCompleted,
      isTimeExpires: isTimeExpires ?? this.isTimeExpires,
      maxValue: maxValue ?? this.maxValue,
      isChangedValue: isChangedValue ?? this.isChangedValue,
      listState: listState ?? this.listState,
    );
  }
}

@immutable
class JobDetailsState {
  const JobDetailsState(this.data);

  final JobDetailsData data;
}

class JobDetailsLoading extends JobDetailsState {
  JobDetailsLoading(Job job)
      : super(
          JobDetailsData(
            isBusy: true,
            job: job,
            maintenanceTasks: const [],
            jobTaskGroup: const {},
            groupNameList: const [],
            groupIndex: 0,
            isCompleted: false,
            isTimeExpires: false,
            maxValue: null,
            isChangedValue: false,
            listState: ListState.scroll,
            currentLocation: null,
          ),
        );
}

class JobDetailsLoadSuccess extends JobDetailsState {
  const JobDetailsLoadSuccess(JobDetailsData data) : super(data);
}

class JobDetailsBusy extends JobDetailsState {
  JobDetailsBusy(JobDetailsData data) : super(data.copyWith(isBusy: true));
}

class JobDetailsUpdateSuccess extends JobDetailsState {
  const JobDetailsUpdateSuccess({
    required JobDetailsData data,
    required this.jobMaintenanceTask,
    required this.task,
  }) : super(data);

  final JobMaintenanceTask task;
  final JobMaintenanceTask jobMaintenanceTask;
}

class JobDetailsCompleteSuccess   extends JobDetailsState{
  const JobDetailsCompleteSuccess({
    required JobDetailsData data,
    required this.jobTaskListRequest,
  }) : super(data);

  final JobTaskListRequest jobTaskListRequest;
}

class JobDetailsUnableAccessSuccess extends JobDetailsState {
  const JobDetailsUnableAccessSuccess(JobDetailsData data) : super(data);
}

class JobDetailsError extends JobDetailsState {
  const JobDetailsError({
    required JobDetailsData data,
    required this.error,
  }) : super(data);

  final String error;
}


enum ListState { scroll, update }
