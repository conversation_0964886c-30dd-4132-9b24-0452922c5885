import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/styles/app_colors.dart';
import 'package:ticking_app/core/styles/app_text_style.dart';
import 'package:ticking_app/core/styles/dimens.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/extend/view_extend.dart';
import 'package:ticking_app/widgets/button/circle_button.dart';

import 'job_details_page.dart';

class JobTaskSlider extends StatefulWidget {
  const JobTaskSlider({
    Key? key,
    required this.maintenanceTask,
    this.maintenanceTasks,
    required this.onChanged,
    required this.isReviewing,
  }) : super(key: key);
  final JobMaintenanceTask maintenanceTask;
  final List<JobMaintenanceTask>? maintenanceTasks;
  final Function(JobTaskValue) onChanged;
  final bool isReviewing;

  @override
  _JobTaskSliderState createState() => _JobTaskSliderState();
}

class _JobTaskSliderState extends State<JobTaskSlider> {
  ///Origin slider  check if slider value changed
  double _originSliderValue = 0;
  double _currentSliderValue = 0;
  double _maxValue = 0;
  double _minValue = 0;
  double _increaseValue = 1;
  List<String> _domain = [];
  double? _maxConstraintValue;
  String _constraintsId = '';
  late bool _isChangedValue;

  @override
  void initState() {
    super.initState();
    _isChangedValue = widget.maintenanceTask.isChangedValue;
    _constraintsId = widget.maintenanceTask.jobTaskTemplate.constraints ?? '';
    _domain = widget.maintenanceTask.jobTaskTemplate.domain;
    _originSliderValue = double.parse(
        widget.maintenanceTask.jobTaskTemplate.defaultValue ?? '0');
    _currentSliderValue =
        double.parse((widget.maintenanceTask.value ?? '0').toString());

    _maxConstraintValue = _getMaxConstraintValue();
    _loadValueByInputType();

    if (_maxValue < _minValue) {
      _maxValue = _minValue;
    }

    if (_currentSliderValue > _maxValue) {
      _currentSliderValue = _maxValue;
    }
    if (_currentSliderValue < _minValue) {
      _currentSliderValue = _minValue;
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<JobDetailsBloc, JobDetailsState>(
      buildWhen: (_, current) {
        if (current is JobDetailsUpdateSuccess) {
          return (_constraintsId.contains(current.task.jobTaskTemplate.id) &&
                  _isScrollSlider() == true) ||
              (current.task.jobTaskTemplate.id ==
                          widget.maintenanceTask.jobTaskTemplate.id &&
                      current.data.isChangedValue == false) ==
                  true;
        }

        return false;
      },
      listener: (context, state) {
        if (state is JobDetailsUpdateSuccess) {
          _handleUpdatedSlider(state);
        }
      },
      builder: (context, state) {
        return AbsorbPointer(
          absorbing: widget.isReviewing,
          child: Column(
            children: [
              _buildTitle(),
              _buildSlider(),
              _buildReset(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          child: Text(
            '${widget.maintenanceTask.jobTaskTemplate.name}: ${_currentSliderValue.convertIfInt()} ${widget.maintenanceTask.jobTaskTemplate.unit}',
            style: Theme.of(context).normalStyle.copyWith(
                color: widget.isReviewing ? AppColors.black1 : AppColors.black,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ).pt(value: 8),
        ),
        if (widget.maintenanceTask.maintenanceTask.isRequired)
          Text(
            "Required",
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: AppColors.redAccentColor, fontSize: 15),
          ).pt(value: 8),
      ],
    );
  }

  Color get widgetColor =>
      widget.isReviewing ? AppColors.grey12 : AppColors.grey;

  Widget _buildSlider() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      children: [
        Text(
          _minValue.convertIfInt().toString(),
          style: Theme.of(context).normalStyle.copyWith(color: widgetColor),
        ).pr(value: 15),
        Expanded(
          child: Stack(
            alignment: Alignment.centerRight,
            children: [
              SliderTheme(
                data: SliderThemeData(
                    trackShape: _BuildTrackShape(), trackHeight: 1),
                child: Slider(
                  value: _currentSliderValue,
                  max: _maxValue,
                  min: _minValue,
                  activeColor:
                      _isChangedValue ? AppColors.primaryColor : widgetColor,
                  inactiveColor: AppColors.grey2,
                  divisions: (_maxValue - _minValue) ~/
                      (_increaseValue == 0 ? 1 : _increaseValue),
                  label: _currentSliderValue.convertIfInt().toString(),
                  onChanged: (double value) {
                    setState(() {
                      if (isHasConstraint) {
                        if (value <= _maxConstraintValue!) {
                          _currentSliderValue = value;
                          widget.onChanged(
                              JobTaskValue(_currentSliderValue, true));
                        }
                      } else {
                        _currentSliderValue = value;
                        widget
                            .onChanged(JobTaskValue(_currentSliderValue, true));
                      }

                      // else if (widget
                      //         .maintenanceTask.jobTaskTemplate.constraints ==
                      //     null) {
                      //   _currentSliderValue = value;
                      //   widget
                      //       .onChanged(JobTaskValue(_currentSliderValue, true));
                      // }
                    });
                  },
                ),
              ),
              if (_isChangedValue == false)
                Padding(
                  padding: const EdgeInsets.only(bottom: 20),
                  child: Text(
                    'Not measured',
                    style: Theme.of(context)
                        .normalStyle
                        .copyWith(color: widgetColor, fontSize: 10),
                  ),
                ),
            ],
          ),
        ),
        Text(
          _maxValue.convertIfInt().toString(),
          style: Theme.of(context).normalStyle.copyWith(color: widgetColor),
        ).pl(value: 15),
      ],
    );
  }

  Widget _buildReset() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Not done in '
          '${DateTimeExtensions.daysBetween(widget.maintenanceTask.maintenanceTask.lastDoneOn?.clearTime() ?? DateTime.now().clearTime(), DateTime.now().clearTime())} days',
          style: Theme.of(context).normalStyle.copyWith(color: widgetColor),
        ),
        CircleButton(
          child: Assets.icon.refreshEnable.svg(
            color: _isChangedValue ? AppColors.primaryColor : AppColors.grey3,
          ),
          onPressed: () {
            // widget.onChanged(JobTaskValue(_originSliderValue, false));
            if (_isReset()) {
              widget.onChanged(JobTaskValue(_originSliderValue, false));
              // _isChangedValue = false;
              // setState(() {});
            }
          },
          size: Dimens.ic_XL,
        ),
      ],
    );
  }

  void _handleUpdatedSlider(JobDetailsUpdateSuccess state) {
    if (_constraintsId.isNotEmpty) {
      JobMaintenanceTask? job =
          _jobMaintenanceTask(_constraintsId, widget.maintenanceTasks ?? [], 2);
      if (job != null) {
        _maxConstraintValue = job.value;
        if (_currentSliderValue > _maxConstraintValue!) {
          _currentSliderValue = _maxConstraintValue!;
          _isChangedValue = true;
        }
      }
    }

    if (state.jobMaintenanceTask.jobTaskTemplate.id ==
        widget.maintenanceTask.jobTaskTemplate.id) {
      _isChangedValue = state.data.isChangedValue;
      if (state.data.isChangedValue == false) {
        _currentSliderValue = state.data.maxValue;
      }
    }
  }

  bool _isScrollSlider() {
    bool isScrollSlider = false;
    if (_constraintsId.isNotEmpty) {
      JobMaintenanceTask? minJob =
          _jobMaintenanceTask(_constraintsId, widget.maintenanceTasks ?? [], 0);
      JobMaintenanceTask? maxJob =
          _jobMaintenanceTask(_constraintsId, widget.maintenanceTasks ?? [], 2);
      if (maxJob != null && minJob != null) {
        isScrollSlider = maxJob.value <= minJob.value;
      }
    }
    return isScrollSlider;
  }

  bool _isReset() {
    bool isReset = true;

    if (_constraintsId.isNotEmpty) {
      JobMaintenanceTask? minJob =
          _jobMaintenanceTask(_constraintsId, widget.maintenanceTasks ?? [], 0);
      if (minJob != null && minJob.jobTaskTemplate.maxConstraintValue != null) {
        isReset =
            minJob.jobTaskTemplate.maxConstraintValue! >= _originSliderValue;
      }
    }

    return isReset;
  }

  void _loadValueByInputType() {
    switch (widget.maintenanceTask.jobTaskTemplate.inputType) {
      case JobTaskInputType.number:
        _minValue = double.parse(_domain[0]);
        _increaseValue = double.parse(_domain[1]);
        _increaseValue = _increaseValue == 0 ? 1 : _increaseValue;
        _maxValue = double.parse(_domain[2]);
        break;

      case JobTaskInputType.calculation:
        _minValue = 0;
        _increaseValue = _calculator(_domain[0]);
        _increaseValue = _increaseValue == 0 ? 1 : _increaseValue;
        _maxValue = 180;
        break;
    }

    if (_currentSliderValue < _minValue) {
      _currentSliderValue = _minValue;
    }

    if (_currentSliderValue > _maxValue) {
      _currentSliderValue = _maxValue;
    }
  }

  double? _getMaxConstraintValue() {
    if (_constraintsId.isNotEmpty) {
      JobMaintenanceTask? job =
          _jobMaintenanceTask(_constraintsId, widget.maintenanceTasks ?? [], 2);
      List<JobMaintenanceTask> maintenanceTasks = widget.maintenanceTasks ?? [];

      ///get job has min constain
      int? minConstraintIndex = maintenanceTasks.indexWhere(
          (element) => element.jobTaskTemplate.id == job?.jobTaskTemplate.id);
      if (minConstraintIndex != -1) {
        _maxConstraintValue = maintenanceTasks[minConstraintIndex].value;
      }
    }
    return _maxConstraintValue;
  }

  JobMaintenanceTask? _jobMaintenanceTask(String constraints,
      List<JobMaintenanceTask> jobMaintenanceTasks, int index) {
    List<String> _char = constraints.split(' ');
    String _idMaxConstraint =
        _char[index].replaceAll('{', '').replaceAll('}', '');
    final findIndex = jobMaintenanceTasks.indexWhere(
        (element) => element.jobTaskTemplate.id == _idMaxConstraint);

    if (findIndex > -1) {
      JobMaintenanceTask? job = jobMaintenanceTasks[findIndex];
      return job;
    }

    return null;
  }

  bool get isHasConstraint =>
      _maxConstraintValue != null &&
      widget.maintenanceTask.jobTaskTemplate.constraints != null;

  double _calculator(String text) {
    try {
      List<JobMaintenanceTask> _maintenanceTasks =
          widget.maintenanceTasks ?? [];
      List<String> _ids = [];
      for (int i = 0; i < text.split(' ').length; i++) {
        if (text.split(' ')[i].contains('{')) {
          _ids.add(text.split(' ')[i]);
        }
      }
      for (int i = 0; i < _maintenanceTasks.length; i++) {
        if (_ids.any((element) =>
            element.replaceAll('{', '').replaceAll('}', '') ==
            _maintenanceTasks[i].jobTaskTemplate.id)) {
          text = text.replaceAll('{${_maintenanceTasks[i].jobTaskTemplate.id}}',
              _maintenanceTasks[i].jobTaskTemplate.domain[1]);
        }
      }
      // Evaluate expression:
      ContextModel cm = ContextModel();
      Parser p = Parser();
      Expression exp = p.parse(text);
      double value = exp.evaluate(EvaluationType.REAL, cm);
      return value;
    } catch (e) {
      return 0;
    }
  }
}

class _BuildTrackShape extends RoundedRectSliderTrackShape {
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 0;
    final double trackLeft = offset.dx;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    final double trackWidth = parentBox.size.width;
    return Rect.fromLTWH(trackLeft, trackTop, trackWidth, trackHeight);
  }
}
