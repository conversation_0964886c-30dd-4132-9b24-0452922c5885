import 'dart:io';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ticking_api_client/ticking_api_client.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/core/arguments/job_detail_arg.dart';
import 'package:ticking_app/core/arguments/preview_photo_arg.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/pages/details/ui/job_auto_calculate.dart';
import 'package:ticking_app/pages/details/ui/job_task_input_completed.dart';
import 'package:ticking_app/pages/home/<USER>/job/job_list_bloc.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/job_detail_bs.dart';
import 'package:ticking_app/pages/home/<USER>/bottom_sheet/request_bs.dart';
import 'package:ticking_app/pages/home/<USER>/share/job_issue_widget.dart';
import 'package:ticking_app/pages/details/ui/job_details_selected_image_page.dart';
import 'package:ticking_app/services/config_service/secure_config_service.dart';
import 'package:ticking_app/services/gelocator_service/location_service.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/widgets/app_button.dart';
import 'package:ticking_app/widgets/button/common_enable_button.dart';
import 'package:ticking_app/widgets/button/common_outline_button.dart';
import 'package:ticking_app/widgets/button/common_primary_button.dart';
import 'package:ticking_app/widgets/drop_down/app_drop_down.dart';
import 'package:ticking_app/widgets/image/add_image_widget.dart';
import 'package:ticking_app/widgets/image/memory_image_widget.dart';
import 'package:ticking_app/widgets/image/network_image_widget.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';
import 'package:timer_builder/timer_builder.dart';
import 'job_task_check.dart';
import 'job_task_input.dart';
import 'job_task_selection.dart';

class JobDetailsPage extends StatefulWidget {
  const JobDetailsPage({Key? key, required this.arg}) : super(key: key);
  final JobDetailArg arg;

  @override
  _JobDetailsPageState createState() => _JobDetailsPageState();
}

class _JobDetailsPageState extends State<JobDetailsPage>
    with TickerProviderStateMixin {
  late JobDetailsBloc _bloc;
  final ScrollController _scrollController = ScrollController();
  final Map<String, GlobalKey> _groupKeys = {};

  bool get isReviewing => widget.arg.jobDetailType == JobDetailType.reviewing;
  List<ImageInputs> imageInputs = [];
  List<bool> isShows = [];
  bool isLoading = true;

  @override
  void initState() {
    _getConfiguration();
    BlocProvider.of<JobListBloc>(App.overlayContext!)
        .add(JobAutoRefreshDisabled());

    _bloc = JobDetailsBloc(job: widget.arg.job);

    if (isReviewing) {
      _bloc.add(JobDetailsReviewingLoaded());
    } else {
      _bloc.add(JobDetailsLoaded());
    }

    bool notifyRepair = widget.arg.job.details.notifyRepair ?? false;
    if (notifyRepair && !isReviewing) {
      _modalBottomSheetMenu();
    }

    super.initState();
  }

  @override
  void dispose() {
    _bloc.close();
    _scrollController.dispose();
    super.dispose();
  }

  _getConfiguration() async {
    try {
      final _secureConfigService = GetIt.I<SecureConfigService>();
      final configurationResult = _secureConfigService.configuration;
      final listRequirements = configurationResult?.jobRequirements ?? [];
      imageInputs = listRequirements
              .firstWhere((element) =>
                  element.serviceType == widget.arg.job.details.serviceType)
              .imageInputs ??
          [];
      for (int i = 0; i < imageInputs.length; i++) {
        isShows.add(false);
      }
    } catch (e) {
      imageInputs = [];
    }
    setState(() {
      isLoading = false;
    });
  }

  void _modalBottomSheetMenu() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (true) {
        showRequestBS(
          context: App.overlayContext,
          // onSubmitted: (comment) =>
          //     BlocProvider.of<JobListBloc>(context).add(
          //   JobRepairPosted(
          //     job: widget.job,
          //     description: comment,
          //   ),
          // ),
          job: widget.arg.job,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<JobListBloc, JobListState>(
      listener: (context, state) {
        if (state is JobListError) {
          DialogHelper.showError(content: state.error);
          return;
        }
      },
      child: BlocProvider(
        create: (context) => _bloc,
        child: Scaffold(
          appBar: _buildAppBar(),
          body: _buildBody(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: AppColors.grey4,
      leading: isReviewing
          ? const BackButton(
              color: Colors.black,
            )
          : Text(
              widget.arg.job.details.serviceType,
              style: Theme.of(context)
                  .normalStyle
                  .copyWith(color: AppColors.black, fontSize: 14),
            ).pl(value: 16).pt(value: 30),
      toolbarHeight: 70,
      leadingWidth: isReviewing ? null : 150,
      title: Text(
        isReviewing ? 'Reviewing' : 'Working',
        style: Theme.of(context)
            .textTheme
            .headline6
            ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
      ),
      centerTitle: true,
      actions: [
        if (!isReviewing)
          BlocBuilder<JobDetailsBloc, JobDetailsState>(
            builder: (context, state) {
              final data = state.data;
              final jobDateStart = data.jobDateStart;
              final int timer = widget.arg.job.details.timer;

              return jobDateStart != null
                  ? _BuildTimer(
                      startTime: jobDateStart,
                      whenTimeExpires: () {
                        _bloc.add(JobDetailsTimeExpired());
                      },
                      secondsRemaining: (timer -
                              DateTime.now().difference(jobDateStart).inSeconds)
                          .toInt(),
                      countDownTimerStyle: Theme.of(context)
                          .normalStyle
                          .copyWith(color: AppColors.black, fontSize: 14),
                      timer: timer,
                    )
                  : const SizedBox();
            },
          ),
      ],
    );
  }

  Widget _buildBody() {
    return BlocConsumer<JobDetailsBloc, JobDetailsState>(
      buildWhen: (_, current) =>
          (current is! JobDetailsUpdateSuccess &&
              current is! JobDetailsError &&
              current is! JobDetailsUnableAccessSuccess) ||
          current.data.listState == ListState.scroll,
      listener: (context, state) async {
        if (state is JobDetailsCompleteSuccess) {
          final XFile? photo =
              await ImagePicker().pickImage(source: ImageSource.camera);

          if (photo != null) {
            App.pushNamed(
              AppRoutes.previewPhoto,
              PreviewPhotoArg(
                // bytes: await photo.readAsBytes(),
                filePath: photo.path,
                job: widget.arg.job,
                reasonValue: ReasonValue.complete,
                jobTaskListRequest: state.jobTaskListRequest,
              ),
            );
          }
        } else if (state is JobDetailsError) {
          DialogHelper.showError(content: state.error);
        } else if (state is JobDetailsUnableAccessSuccess) {
          App.pushNamedAndPopUntil(AppRoutes.home, null, '/');
        }
      },
      builder: (context, state) {
        return _buildList(state, context);
      },
    );
  }

  Widget _buildList(JobDetailsState state, BuildContext context) {
    if (state is JobDetailsLoading) {
      return const Center(
        child: LoadingIndicator(),
      );
    }

    final data = state.data;
    return SafeArea(
      child: Stack(
        children: [
          Container(
            color: AppColors.grey4,
            height: double.infinity,
            child: Scrollbar(
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHeaderButton(context),
                    Text(
                      'Maintenance Reports',
                      style: Theme.of(context).normalStyle.copyWith(
                          color: AppColors.grey, fontWeight: FontWeight.w700),
                    ).pl(value: 16).pb(value: 8),
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
                      child: AppDropDown(
                        groupNameList: data.groupNameList,
                        onSelectedItem: (index) {
                          _scrollToGroup(index, data.groupNameList);
                        },
                        index: data.groupIndex,
                      ),
                    ),
                    _buildMaintenanceReports(state),
                    SizedBox(height: ViewUtils.getPercentHeight(percent: 0.15)), // Bottom padding for button
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildFooterButtons(state),
          ),
          if (data.isBusy == true)
            const Center(
              child: LoadingIndicator(),
            )
        ],
      ),
    );
  }

  Widget _buildGroupName(String text) {
    return Row(children: <Widget>[
      const Expanded(
          child: Divider(
        color: AppColors.grey2,
      )),
      Text(
        text,
        style: Theme.of(context).textTheme.bodyText1?.copyWith(
              color: AppColors.grey,
              fontWeight: FontWeight.w600,
              // height:
            ),
      ).pl(value: 8).pr(value: 8),
      const Expanded(
          child: Divider(
        color: AppColors.grey2,
      )),
    ]).pb(value: 10).pl(value: 16).pr(value: 16);
  }

  Widget _buildHeaderButton(BuildContext context) {
    return isLoading == true
        ? const Center(
            child: LoadingIndicator(),
          )
        : Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.maxFinite,
                margin: const EdgeInsets.only(left: 16, right: 16, bottom: 17),
                height: 64,
                decoration: BoxDecoration(
                  color: AppColors.grey,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: InkWell(
                  onTap: () {
                    final curLocation = _bloc.state.data.currentLocation;
                    final jobGps = widget.arg.job.contact.gps;

                    final smsTemplate = _bloc.state.data.smsTemplate;
                    String distance = '0 mi';
                    if (curLocation != null && jobGps != null) {
                      distance =
                          '${NumberUtils.formatToPattern(GeoLocatorUtils.distanceBetweenGps(curLocation, jobGps))}mi';
                    }

                    final String? groupName =
                        widget.arg.job.scheduleDate?.dateGroupName;

                    showJobDetailBS(
                      context: context,
                      isDoneButtonEnable: false,
                      job: widget.arg.job,
                      distance: distance,
                      group: groupName ?? '',
                      smsTemplate: smsTemplate,
                      isStart: true,
                      jobDetailsBloc: _bloc,
                      // JobDetailsRepairPosted
                      // onRepairPosted: (Job job, String description) {
                      //   BlocProvider.of<JobListBloc>(context).add(
                      //     JobRepairPosted(
                      //       job: job,
                      //       description: description,
                      //     ),
                      //   );
                      // },
                      onUnableToAccess: () {},
                      onCommentPosted: (String comment, Job job) {
                        _bloc.add(JobDetailsCommentPosted(comment));
                      },
                      onCommentDeleted: (JobComment comment, Job job) {
                        _bloc.add(
                          JobDetailsCommentDeleted(
                            comment: comment,
                            job: job,
                          ),
                        );
                      },
                      onCommentEdit:
                          (JobComment comment, Job job, String body) {
                        _bloc.add(JobDetailsCommentUpdated(
                          job: job,
                          comment: comment,
                          body: body,
                        ));
                      },
                      onCustomerCommentPosted: (String comment, Job job) {
                        _bloc.add(JobDetailsCustomerCommentPosted(comment));
                      },
                      onCustomerCommentDeleted: (JobComment comment, Job job) {
                        _bloc.add(
                          JobDetailsCustomerCommentDeleted(
                            comment: comment,
                            job: job,
                          ),
                        );
                      },
                      onCustomerCommentEdit:
                          (JobComment comment, Job job, String body) {
                        _bloc.add(JobDetailsCustomerCommentUpdated(
                          job: job,
                          comment: comment,
                          body: body,
                        ));
                      },
                    );
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          widget.arg.job.employee.name,
                          style: Theme.of(context).normalStyle.copyWith(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w700),
                        ),
                      ),
                      const RotatedBox(
                        quarterTurns: 2,
                        child: Icon(Icons.arrow_back_ios),
                      ).pb(value: 6),
                    ],
                  ).pr(value: 20).pl(value: 16),
                ),
              ),
              if (imageInputs.isNotEmpty)
                for (int i = 0; i < imageInputs.length; i++)
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          final _res = !isShows[i];
                          isShows.removeAt(i);
                          isShows.insert(i, _res);
                          setState(() {
                            isShows;
                          });
                        },
                        child: Container(
                          width: double.maxFinite,
                          margin: const EdgeInsets.only(
                              left: 16, right: 16, bottom: 17),
                          height: 50,
                          decoration: BoxDecoration(
                            color: AppColors.primaryColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  imageInputs[i].section ?? "",
                                  style: Theme.of(context).normalStyle.copyWith(
                                    color: Colors.white,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                              RotatedBox(
                                quarterTurns: isShows[i] == true ? 3 : 2,
                                child: const Icon(Icons.arrow_back_ios),
                              ).pb(value: 6),
                            ],
                          ).pr(value: 20).pl(value: 16),
                        ),
                      ),
                      if (isShows[i] == true)
                        _buildImageInputArea(index: i),
                    ],
                  ),
            ],
          );
  }

  Widget _buildMaintenanceReports(JobDetailsState state) {
    final data = state.data;
    Map<String, List<JobMaintenanceTask>> _jobTaskGroup = data.jobTaskGroup;

    // Initialize group keys if not already done
    for (String groupKey in _jobTaskGroup.keys) {
      if (!_groupKeys.containsKey(groupKey)) {
        _groupKeys[groupKey] = GlobalKey();
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _jobTaskGroup.keys.map((groupItemKey) {
        return Column(
          key: _groupKeys[groupItemKey],
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Group header
            Container(
              color: AppColors.grey4,
              child: _buildGroupName(groupItemKey),
            ),
            // Group content
            Container(
              color: AppColors.grey4,
              padding: const EdgeInsets.only(top: 20),
              child: Column(
                children: [
                  for (int index = 0; index < _jobTaskGroup[groupItemKey]!.length; index++) ...[
                    _MaintenanceTaskWidget(
                      maintenanceTask: (_jobTaskGroup[groupItemKey] ?? [])[index],
                      maintenanceTasks: data.maintenanceTasks,
                      isReviewing: isReviewing,
                    ),
                    if (index < _jobTaskGroup[groupItemKey]!.length - 1) Gaps.vGap12,
                  ],
                ],
              ).py(Dimens.pad_XS).pb(value: 20),
            ),
          ],
        );
      }).toList(),
    );
  }

  void _scrollToGroup(int index, List<String> groupNameList) {
    if (index >= 0 && index < groupNameList.length) {
      final groupKey = groupNameList[index];
      final key = _groupKeys[groupKey];

      if (key?.currentContext != null) {
        Scrollable.ensureVisible(
          key!.currentContext!,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  Widget _buildFooterButtons(JobDetailsState state) {
    final data = state.data;
    return Container(
      decoration: AppDecorStyle.topShadowDecor(
        surfaceColor: AppColors.materialWhite,
      ),
      child: Row(
        children: [
          CommonOutlineButton(
            text: 'REPAIR',
            onPressed: () {
              showRequestBS(
                // onSubmitted: (comment) =>
                //     BlocProvider.of<JobListBloc>(context).add(
                //   JobRepairPosted(
                //     job: widget.job,
                //     description: comment,
                //   ),
                // ),
                job: widget.arg.job,
              );
            },
          ),
          Gaps.hGap12,
          if (!isReviewing)
            CommonOutlineButton(
              text: 'ISSUE',
              onPressed: () {
                handleShowComment(
                  context: context,
                  job: widget.arg.job,
                  smsTemplate: null,
                  onNextStep: _handleValidateGps,
                );
              },
            ),
          Gaps.hGap12,
          if (!isReviewing)
            TimerBuilder.periodic(const Duration(seconds: 1),
                builder: (context) {
              if (data.jobDateStart == null) {
                return const SizedBox();
              }

              final now = DateTime.now();
              final duration = now.difference(data.jobDateStart!);
              int seconds = data.job.details.timer - duration.inSeconds;

              if (seconds > 0) {
                return CommonEnableButton(
                  child: 'DONE'
                      .text
                      .textStyle(Theme.of(context).textTheme.bodyText1!)
                      .size(Dimens.text_XL)
                      .color(AppColors.grey2)
                      .fontWeight(FontWeight.w600)
                      .make(),
                ).expand();
              }

              return CommonPrimaryButton(
                child: 'DONE'
                    .text
                    .textStyle(Theme.of(context).textTheme.bodyText1!)
                    .size(Dimens.text_XL)
                    .color(AppColors.materialWhite)
                    .fontWeight(FontWeight.w600)
                    .make(),
                onPressed: () {
                  DialogHelper.showIosOptionDialog(
                    context,
                    content: 'Would you like to complete this job?',
                    themeData: ThemeData.light(),
                    btnTextStyle: Theme.of(context)
                        .textTheme
                        .bodyText1
                        ?.copyWith(
                            color: AppColors.systemBlueColor,
                            fontWeight: FontWeight.w500,
                            fontSize: Dimens.text_XSL),
                    contentTextStyle: Theme.of(context)
                        .textTheme
                        .bodyText1
                        ?.copyWith(
                            color: AppColors.black,
                            fontWeight: FontWeight.normal,
                            fontSize: Dimens.text_M),
                    negativeText: 'No',
                    positiveText: 'Yes',
                    onNegative: () async {
                      App.pop();
                    },
                    onPositive: () async {
                      App.pop();

                      // Validate image count verification (same logic as Repair Ticket Page)
                      final _secureConfigService = GetIt.I<SecureConfigService>();
                      final configuration = _secureConfigService.configuration;
                      if (configuration != null) {
                        final listRequirements = configuration.jobRequirements ?? [];
                        try {
                          final jobRequirement = listRequirements.firstWhere(
                            (element) => element.serviceType == widget.arg.job.details.serviceType,
                          );
                          final imageInputs = jobRequirement.imageInputs ?? [];

                          for (var item in imageInputs) {
                            if (item.isCountVerification == true) {
                              final _listImage = _bloc.state.data.job.events
                                  .where((element) => element.description == item.section)
                                  .toList();
                              final min = _bloc.state.data.job.imageInputs
                                  .firstWhereOrNull((element) => element.section == item.section)
                                  ?.countVerification
                                  ?.min;

                              if (_listImage.length < (min ?? 0)) {
                                await DialogHelper.showError(
                                  content: 'Incorrect number of images for ${item.section}. Please try again.',
                                );
                                return;
                              }
                            }
                          }
                        } catch (e) {
                          // If no matching service type found, continue without validation
                        }
                      }

                      _bloc.add(
                        JobDetailsCompleted(
                          request: JobTaskListRequest(
                            endDate: DateTime.now(),
                            reason: 'complete',
                            tasks: [],
                            startDate: DateTime.now(),
                          ),
                        ),
                      );
                    },
                  );
                },
              ).expand();
            })
        ],
      ).px16().py20(),
    );
  }

  Future<bool> _validateGps() async {
    final gps = await GetIt.I<LocationService>().getCurrentPosition();
    final configuration = GetIt.I<SecureConfigService>().configuration;
    final jobGps = widget.arg.job.contact.gps;
    if (configuration == null || jobGps == null) {
      DialogHelper.showError(
        content: "Can not get configuration",
      );
      return false;
    }

    final distance = GeoLocatorUtils.distanceBetweenCoordinatesInMiles(
        gps.latitude, gps.longitude, jobGps.latitude, jobGps.longitude);

    if (distance > configuration.issueDistanceThreshold.value) {
      DialogHelper.showError(
        content:
            "Your report location is out of range from the customer's location. Please, move to closer to the customer's location and report the issue request again",
        actions: [
          AppButton(
            borderRadius: Platform.isIOS ? 0 : 8,
            backgroundColor: Platform.isIOS
                ? Colors.transparent
                : Theme.of(context).primaryColor,
            onPressed: () async {
              Navigator.of(context).pop(true);
            },
            child: Center(
              child: Text(
                "Retry",
                style: Theme.of(context).textTheme.bodyText1!.copyWith(
                    color: Platform.isIOS
                        ? Theme.of(context).textColor()
                        : Theme.of(context).backgroundColor),
              ),
            ),
          ),
        ],
        icon: const SizedBox(),
      );
      return false;
    }
    return true;
  }

  Future<void> _handleValidateGps() async {
    bool valid = await _validateGps();
    if (valid) {
      showIssueDialog(
        email: widget.arg.job.contact.email,
        phoneNumber: widget.arg.job.contact.phone,
        job: widget.arg.job,
        smsTemplate: _bloc.state.data.smsTemplate,
        onUnableToAccess: (String reason) {
          _bloc.add(
            JobDetailsUnableAccessed(reason),
          );
        },
      );
    }
  }


  Widget _buildImageInputArea({required int index}) {
    index;
    return BlocConsumer<JobDetailsBloc, JobDetailsState>(
        listenWhen: (previous, current) =>
        previous.data.job != current.data.job,
        listener: (context, state) {
          // Handle any job updates if needed
        },
        buildWhen: (previous, current) =>
        previous.data.job != current.data.job,
        builder: (context, state) {
          if (state is JobDetailsLoading) {
            return const Center(
              child: LoadingIndicator(),
            );
          }
          final description = imageInputs[index].section ?? "";
          List<dynamic> imageUrls = [];
          try {
            final _list2 = (state.data.job.events ?? [])
                .where((element) => element.description == description)
                .toList();
            for (var item in _list2) {
              imageUrls.add(item.image?.link?? item.image?.src);
            }
          } catch (e) {
            e;
          }
          return _imageBuilder(
            index: index,
            imageUrls: imageUrls,
            description: description,
          );
        }).pb(value: 10);
  }

  Widget _imageBuilder({
    required int index,
    required List<dynamic> imageUrls,
    required String description,
  }) {
    final instruction = widget.arg.job.imageInputs
        .firstWhereOrNull((element) => element.section == description)
        ?.instruction;
    final isShowText = imageInputs
        .firstWhereOrNull((element) => element.section == description)
        ?.hasInstructionText;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        (imageUrls.isEmpty)
            ? _buildNoImage(index: index, description: description)
            : _buildImages(
          imageUrls,
          index: index,
          description: description,
        ),
        if (isShowText == true && instruction != null)
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 10,
              horizontal: 20,
            ),
            child: Text(
              instruction,
              style: Theme.of(context).normalStyle.copyWith(
                color: AppColors.error,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildNoImage({int? index, required String description}) {
    return InkWell(
      onTap: () async {
        _insertImage(index: index, description: description);
      },
      child: Container(
        width: double.maxFinite,
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 17),
        height: 64,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Assets.icon.picture.svg(
                width: 36,
                height: 36,
                color: AppColors.grey,
              ),
            ),
            Expanded(
              flex: 5,
              child: Center(
                child: Text(
                  '+ Click to add picture',
                  style: Theme.of(context)
                      .titleTextStyle
                      .copyWith(fontWeight: FontWeight.w700),
                ),
              ),
            ),
            const Expanded(flex: 2, child: SizedBox()),
          ],
        ),
      ),
    );
  }

  Widget _buildImages(List<dynamic> uInt8Lists,
      {int? index, required String description}) {
    return Row(
      children: [
        InkWell(
          onTap: () async {
            _insertImage(index: index, description: description);
          },
          child: const AddImageWidget(),
        ),
        if (uInt8Lists[0] is Uint8List)
          MemoryImageWidget(
            image: uInt8Lists[0],
            isTextOnImage: false,
            textOnImage: '',
          )
        else
          NetworkImageWidget(
            image: uInt8Lists[0],
            isTextOnImage: false,
            textOnImage: '',
          ),
        if (uInt8Lists.length > 1)
          InkWell(
            onTap: () {
              if (uInt8Lists.length > 2) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) {
                      return BlocProvider.value(
                        value: _bloc,
                        child: JobDetailsSelectedImagePage(description: description),
                      );
                    },
                  ),
                );
              }
            },
            child: uInt8Lists[1] is Uint8List
                ? MemoryImageWidget(
              image: uInt8Lists[1],
              isTextOnImage: uInt8Lists.length > 2,
              textOnImage: '${uInt8Lists.length - 2}',
            )
                : NetworkImageWidget(
              image: uInt8Lists[1],
              isTextOnImage: uInt8Lists.length > 2,
              textOnImage: '${uInt8Lists.length - 2}',
            ),
          ),
      ],
    );
  }

  void _insertImage({int? index, required String description}) async {
    List<XFile> files = [];
    await DialogHelper.showAppDialog(
      context,
      title: 'Add Pictures',
      content: "Please select the method to add pictures!",
      textStyle: Theme.of(context).contentTextStyle,
      actions: [
        AppButton(
          backgroundColor: AppColors.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
          onPressed: () async {
            XFile? file =
            await ImagePicker().pickImage(source: ImageSource.camera);
            Navigator.pop(context);
            if (file != null) {
              files.add(file);
            }
          },
          child: Center(
            child: Text(
              "Camera",
              style: Theme.of(context)
                  .textTheme
                  .bodyText1!
                  .copyWith(color: AppColors.background, fontSize: 16),
            ),
          ),
        ),
        AppButton(
          backgroundColor: AppColors.primaryColor,
          padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
          onPressed: () async {
            files = await ImagePicker().pickMultiImage();
            Navigator.pop(context);
          },
          child: Center(
            child: Text(
              "Gallery",
              style: Theme.of(context)
                  .textTheme
                  .bodyText1!
                  .copyWith(color: AppColors.background, fontSize: 16),
            ),
          ),
        ),
      ],
    );
    if (files.isNotEmpty) {
      _bloc.add(ImageInputsInserted(
          files: files, index: index ?? 0, description: description));
    }
  }
}

class _MaintenanceTaskWidget extends StatelessWidget {
  const _MaintenanceTaskWidget({
    Key? key,
    required this.maintenanceTask,
    this.maintenanceTasks,
    required this.isReviewing,
  }) : super(key: key);
  final bool isReviewing;
  final JobMaintenanceTask maintenanceTask;
  final List<JobMaintenanceTask>? maintenanceTasks;

  void _handleDataChanged(BuildContext context, JobTaskValue jobTaskValue) {
    BlocProvider.of<JobDetailsBloc>(context).add(
      JobDetailsDataChanged(
        value: jobTaskValue.value,
        jobMaintenanceTask: maintenanceTask,
        isChangedValue: jobTaskValue.isChangedValue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    switch (maintenanceTask.jobTaskTemplate.inputType) {
      case JobTaskInputType.number:
        if (isReviewing == true) {
          return _buildWrap(
            child: JobTaskInputCompleted(
              maintenanceTask: maintenanceTask,
              onChanged: (_) {},
              isReviewing: isReviewing,
            ),
          );
        }
        return _buildWrap(
          child: JobTaskInput(
            maintenanceTask: maintenanceTask,
            onChanged: (value) {
              _handleDataChanged(context, value);
            },
            isReviewing: isReviewing,
          ),
        );
      case JobTaskInputType.calculation:
        return _buildWrap(
          child: AutoCalculate(
            maintenanceTask: maintenanceTask,
            isReviewing: isReviewing,
          ),
        );
      case JobTaskInputType.checkbox:
        return _buildWrap(
          child: JobTaskCheckbox(
            maintenanceTask: maintenanceTask,
            onChanged: (value) {
              _handleDataChanged(context, value);
            },
            isReviewing: isReviewing,
          ),
        );
      case JobTaskInputType.selection:
        return _buildWrap(
          child: JobTaskSelection(
            maintenanceTask: maintenanceTask,
            onChanged: (value) {
              _handleDataChanged(context, value);
            },
            isReviewing: isReviewing,
          ),
        );
    }
    return const SizedBox();
  }

  Widget _buildWrap({required Widget child}) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: child,
    );
  }
}

class _BuildTimer extends StatefulWidget {
  const _BuildTimer({
    Key? key,
    required this.secondsRemaining,
    required this.whenTimeExpires,
    this.countDownFormatter,
    this.countDownTimerStyle,
    required this.timer,
    required this.startTime,
  }) : super(key: key);
  final int timer;
  final int secondsRemaining;
  final DateTime startTime;
  final VoidCallback whenTimeExpires;
  final TextStyle? countDownTimerStyle;
  final Function(int seconds)? countDownFormatter;

  @override
  State createState() => _BuildTimerState();
}

class _BuildTimerState extends State<_BuildTimer> {
  String get timerDisplayString {
    final now = DateTime.now();
    final duration = now.difference(widget.startTime);
    int seconds = widget.timer - duration.inSeconds;
    if (seconds < 0) {
      seconds = 0;
    }
    if (widget.countDownFormatter != null) {
      return widget.countDownFormatter!(seconds) as String;
    } else {
      return formatHHMMSS(seconds);
    }
  }

  String formatHHMMSS(int seconds) {
    final hours = (seconds / 3600).truncate();
    seconds = (seconds % 3600).truncate();
    final minutes = (seconds / 60).truncate();

    final hoursStr = (hours).toString().padLeft(2, '0');
    final minutesStr = (minutes).toString().padLeft(2, '0');
    final secondsStr = (seconds % 60).toString().padLeft(2, '0');

    if (hours == 0) {
      return '$minutesStr:$secondsStr';
    }

    return '$hoursStr:$minutesStr:$secondsStr';
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TimerBuilder.periodic(const Duration(seconds: 1),
        builder: (context) {
      return Center(
        child: Text(
          timerDisplayString,
          style: widget.countDownTimerStyle,
        ).pr(value: 16),
      );
    });
  }
}

class JobTaskValue {
  JobTaskValue(this.value, this.isChangedValue);

  final dynamic value;
  final bool isChangedValue;
}
