import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';

class AutoCalculate extends StatefulWidget {
  const AutoCalculate({
    Key? key,
    required this.maintenanceTask,
    required this.isReviewing ,
  }) : super(key: key);
  final JobMaintenanceTask maintenanceTask;
  final bool isReviewing;

  @override
  _AutoCalculateState createState() => _AutoCalculateState();
}

class _AutoCalculateState extends State<AutoCalculate> {
  double _currentSliderValue = 0;

  @override
  void initState() {
    _currentSliderValue =
        double.parse((widget.maintenanceTask.value ?? '0').toString());

    _loadValueByInputType();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AbsorbPointer(
      absorbing: widget.isReviewing,
      child: BlocConsumer<JobDetailsBloc, JobDetailsState>(
        // buildWhen: (_, current) {
        //   if (current is JobDetailsUpdateSuccess) {
        //     return (current.task.jobTaskTemplate.id ==
        //                 widget.maintenanceTask.jobTaskTemplate.id &&
        //             current.data.isChangedValue == false) ==
        //         true;
        //   }
        //
        //   return false;
        // },
        listener: (context, state) {
          if (state is JobDetailsUpdateSuccess) {
            _loadValueByInputType();
          }
        },
        builder: (context, state) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildTitle(),
              10.toVSizeBox(),
              Text(
                'Not done in '
                '${DateTimeExtensions.daysBetween(widget.maintenanceTask.maintenanceTask.lastDoneOn?.clearTime() ?? DateTime.now().clearTime(), DateTime.now().clearTime())} days',
                style: Theme.of(context)
                    .normalStyle
                    .copyWith(color: AppColors.grey),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          child: Text(
            '${widget.maintenanceTask.jobTaskTemplate.name}: ${_currentSliderValue.convertIfInt()} ${widget.maintenanceTask.jobTaskTemplate.unit}',
            style: Theme.of(context).normalStyle.copyWith(
                color: AppColors.black,
                fontWeight: FontWeight.bold,
                fontSize: 16),
          ),
        ),
        Text(
          'Auto Calculation',
          style: Theme.of(context).normalStyle.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.w400,
              fontSize: 12,
              fontStyle: FontStyle.italic),
        ),
      ],
    );
  }

  void _loadValueByInputType() {
    _calculator(widget.maintenanceTask.jobTaskTemplate.domain[0]);
  }

  double _calculator(String text) {
    try {
      List<JobMaintenanceTask> _maintenanceTasks =
          BlocProvider.of<JobDetailsBloc>(context).state.data.maintenanceTasks;
      String insertText = text;
      // String insertText = text.replaceAll('{2}', '{42}');

      List<String> _ids = [];
      for (int i = 0; i < insertText.split(' ').length; i++) {
        if (insertText.split(' ')[i].contains('{')) {
          _ids.add(insertText.split(' ')[i]);
        }
      }

      for (int i = 0; i < _maintenanceTasks.length; i++) {
        if (_ids.any((element) =>
                element.replaceAll('{', '').replaceAll('}', '') ==
                _maintenanceTasks[i].jobTaskTemplate.id) &&
            (_maintenanceTasks[i].value is double ||
                _maintenanceTasks[i].value is int)) {
          insertText = insertText.replaceAll(
            '{${_maintenanceTasks[i].jobTaskTemplate.id}}',
            _maintenanceTasks[i].value.toString(),
          );
        }
      }

      // Evaluate expression:
      ContextModel cm = ContextModel();
      Parser p = Parser();
      Expression exp = p.parse(insertText);
      double value = exp.evaluate(EvaluationType.REAL, cm);

      _currentSliderValue = value;
      widget.maintenanceTask.value = value;
      widget.maintenanceTask.isChangedValue = true;
      return value;
    } catch (e) {
      return 0;
    }
  }
}
