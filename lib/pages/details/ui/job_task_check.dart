import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';

import '../../../widgets/drop_down/app_drop_down.dart';
import '../bloc/job_details_bloc.dart';
import 'job_details_page.dart';

class JobTaskCheckbox extends StatefulWidget {
  const JobTaskCheckbox({
    Key? key,
    required this.maintenanceTask,
    required this.onChanged,
    this.isChange,
    required this.isReviewing,
    this.defaultValue,
  }) : super(key: key);
  final JobMaintenanceTask maintenanceTask;
  final Function(JobTaskValue) onChanged;
  final bool isReviewing;
  final bool? isChange;
  final String? defaultValue;
  @override
  _JobTaskCheckboxState createState() => _JobTaskCheckboxState();
}

class _JobTaskCheckboxState extends State<JobTaskCheckbox> {
  final List<String> _templateDomains = ['', 'Yes', 'No'];
  List<String> _domains = [];
  String _currentValue = '';
  late bool _isRequired;

  @override
  void initState() {
    super.initState();
    final domainTask = widget.maintenanceTask.maintenanceTask.domain;
    if (domainTask.isEmpty) {
      _domains = _templateDomains;
    } else {
      for (String item in domainTask) {
        if (item.toUpperCase() == "TRUE") {
          _domains.add("Yes");
        } else if (item.toUpperCase() == "FALSE") {
          _domains.add("No");
        } else {
          _domains.add(item);
        }
        item;
        _domains;
      }
      if (!_domains.contains("")) {
        _domains.insert(0, "");
      }
    }
    _updateCurrentValue();
    if (widget.isReviewing == true) {
      if (_currentValue.toUpperCase() == "TRUE") {
        setState(() {
          _currentValue = "Yes";
        });
      } else if (_currentValue.toUpperCase() == "FALSE") {
        setState(() {
          _currentValue = "No";
        });
      }
    }
  }

  void _updateCurrentValue() {
    _isRequired = widget.maintenanceTask.maintenanceTask.isRequired;
    if (widget.defaultValue != null) {
      _currentValue = widget.defaultValue!;
      return;
    }
    if (widget.isReviewing == true) {
      if (widget.maintenanceTask.value == true ||
          widget.maintenanceTask.value == 'Yes') {
        _currentValue = 'Yes';
      } else if (widget.maintenanceTask.value == false ||
          widget.maintenanceTask.value == 'No') {
        _currentValue = 'No';
      }
    } else {
      if (widget.isChange ?? widget.maintenanceTask.isChangedValue) {
        _currentValue = widget.maintenanceTask.value;
      }

      if (_isRequired == false) {
        widget.onChanged(JobTaskValue(_currentValue, true));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<JobDetailsBloc, JobDetailsState>(
        buildWhen: (_, current) {
          if (current is JobDetailsUpdateSuccess) {
            return current.task.jobTaskTemplate.id ==
                    widget.maintenanceTask.jobTaskTemplate.id &&
                widget.maintenanceTask.isChangedValue == false;
          }

          return false;
        },
        listener: (context, state) {},
        builder: (context, state) {
          print(
              "]================${widget.maintenanceTask.jobTaskTemplate.name}(${widget.maintenanceTask.jobTaskTemplate.id})===============[");
          print("Job id: ${widget.maintenanceTask.maintenanceTask.id}");
          print(
              "Template [${_templateDomains.length}]: ${_templateDomains.toList().toString()}");
          print("Domains [${_domains.length}]: ${_domains.toList()}");
          print("Value [${widget.maintenanceTask.maintenanceTask.value}]");

          return AbsorbPointer(
              absorbing: widget.isReviewing,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  color: AppColors.grey4,
                ),
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          widget.maintenanceTask.jobTaskTemplate.name,
                          style: Theme.of(context).normalStyle.copyWith(
                              color: AppColors.black,
                              fontWeight: FontWeight.bold,
                              fontSize: 16),
                        ),
                        if (widget.maintenanceTask.maintenanceTask.isRequired)
                          Text(
                            "(Required)",
                            style: Theme.of(context).normalStyle.copyWith(
                                color: AppColors.redAccentColor, fontSize: 15),
                          ).pl(value: 5),
                      ],
                    ).pb(value: 8).pt(value: 8),
                    SizedBox(
                      width: double.maxFinite,
                      child: AppDropDown(
                        groupNameList: _templateDomains,
                        enableNameList: _domains,
                        onSelectedItem: (index) {
                          _currentValue = _templateDomains[index];

                          if (_isRequired == true && index == 0) {
                            widget
                                .onChanged(JobTaskValue(_currentValue, false));
                          } else {
                            widget.onChanged(JobTaskValue(_currentValue, true));
                          }
                        },
                        index: _templateDomains
                            .indexWhere((element) => element == _currentValue),
                        color: Colors.white,
                        margin: const EdgeInsets.only(left: 0, right: 0),
                        textColor: AppColors.grey,
                        icon: const Icon(
                          Icons.arrow_drop_down,
                          color: AppColors.grey,
                        ),
                        popUpLeft: 0,
                        decoration: BoxDecoration(
                            border: Border.all(color: AppColors.grey3),
                            borderRadius: BorderRadius.circular(8)),
                        textPopUpColor: AppColors.grey,
                      ),
                    ),
                  ],
                ),
              ));
        });
  }
}
