import 'dart:convert';
import 'dart:typed_data';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/gen/assets.gen.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/widgets/app_button.dart';
import 'package:ticking_app/widgets/loading_indicator.dart';

class JobDetailsSelectedImagePage extends StatelessWidget {
  const JobDetailsSelectedImagePage({Key? key, required this.description}) : super(key: key);
  final String description;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        toolbarHeight: 70,
        leading: const BackButton(
          color: Colors.black,
        ),
        title: Text(
          'Pictures',
          style: Theme.of(context)
              .textTheme
              .headline6
              ?.copyWith(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
      ),
      body: BlocBuilder<JobDetailsBloc, JobDetailsState>(
        buildWhen: (previous, current) =>
            current is JobDetailsLoadSuccess ||
            current is JobDetailsLoading ||
            previous.data.job != current.data.job,
        builder: (context, state) {
          final listEvents = state.data.job.events
                  .where((element) => element.description == description)
                  .toList();
          List<dynamic> imageUrls = [];

          // Add images from events (both online and offline)
          for (var item in listEvents) {
            final imageData = item.image?.link ?? item.image?.src;
            if (imageData != null) {
              imageUrls.add(imageData);
            }
          }

          return Stack(
            children: [
              _buildContent(context, imageUrls),
              BlocBuilder<JobDetailsBloc, JobDetailsState>(
                  builder: (context, state) {
                if (state is JobDetailsLoading) {
                  return const LoadingIndicator();
                }
                return const SizedBox();
              }),
            ],
          );
        },
      ),
    );
  }

  Widget _buildContent(
    BuildContext context,
    List<dynamic> imageBytesList,
  ) {
    if (imageBytesList.isEmpty) {
      return _BuildNoImage(
        description: description,
      );
    }
    return _BuildImageList(
      listWidget: _buildImageList(
        context,
        imageBytesList,
      ),
      description: description,
    );
  }

  List<Widget> _buildImageList(
    BuildContext context,
    List<dynamic> imageBytesList,
  ) {
    List<Widget> listWidget = [];

    // Add "Add Picture" button as first item
    listWidget.add(
      Container(
        width: 101,
        height: 131,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: AppColors.grey4,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.add,
              color: AppColors.grey,
              size: 30,
            ),
            12.toVSizeBox(),
            Text(
              'Add\n Picture',
              style: Theme.of(context).titleTextStyle,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );

    // Add existing images
    for (int i = 0; i < imageBytesList.length; i++) {
      if (imageBytesList[i] is Uint8List) {
        listWidget.add(ClipRRect(
          child: Container(
            width: 101,
            height: 131,
            decoration: BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.cover,
                  image: MemoryImage(
                    imageBytesList[i],
                  )),
              borderRadius: BorderRadius.circular(4),
              color: AppColors.grey4,
            ),
          ),
        ));
      } else if (imageBytesList[i] is String && imageBytesList[i] != null) {
        // Handle base64 encoded images (offline mode)
        if ((imageBytesList[i] as String).startsWith('data:') ||
            (imageBytesList[i] as String).length > 100) {
          // This is likely a base64 encoded image
          try {
            String base64String = imageBytesList[i] as String;
            if (base64String.startsWith('data:')) {
              base64String = base64String.split(',')[1];
            }
            final bytes = base64Decode(base64String);
            listWidget.add(ClipRRect(
              child: Container(
                width: 101,
                height: 131,
                decoration: BoxDecoration(
                  image: DecorationImage(
                      fit: BoxFit.cover,
                      image: MemoryImage(bytes)),
                  borderRadius: BorderRadius.circular(4),
                  color: AppColors.grey4,
                ),
              ),
            ));
          } catch (e) {
            // If base64 decode fails, treat as URL
            listWidget.add(ClipRRect(
              child: SizedBox(
                width: 101,
                height: 131,
                child: CachedNetworkImage(
                  imageUrl: imageBytesList[i],
                  imageBuilder: (context, imageProvider) => Container(
                    decoration: BoxDecoration(
                      image: DecorationImage(
                        image: imageProvider,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  placeholder: (context, url) => Container(
                    width: 101,
                    height: 131,
                    color: AppColors.grey4,
                    child: const Center(
                      child: LoadingIndicator(
                        size: 20,
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) => Container(
                    width: 101,
                    height: 131,
                    color: AppColors.grey4,
                    child: const Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.broken_image,
                          color: AppColors.grey,
                          size: 32,
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Unavailable on\nOffline mode',
                          style: TextStyle(
                            color: AppColors.grey,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
          }
        } else {
          // This is a URL
          listWidget.add(ClipRRect(
            child: SizedBox(
              width: 101,
              height: 131,
              child: CachedNetworkImage(
                imageUrl: imageBytesList[i],
                imageBuilder: (context, imageProvider) => Container(
                  decoration: BoxDecoration(
                    image: DecorationImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                placeholder: (context, url) => const LoadingIndicator(
                  size: 15,
                ),
                errorWidget: (context, url, error) =>
                    const Icon(Icons.error),
              ),
            ),
          ));
        }
      }
    }
    return listWidget;
  }
}

class _BuildImageList extends StatelessWidget {
  const _BuildImageList({
    Key? key,
    required this.listWidget,
    required this.description,
  }) : super(key: key);
  final List<Widget> listWidget;
  final String description;

  @override
  Widget build(BuildContext context) {
    return Scrollbar(
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
            childAspectRatio: 111 / 132),
        padding: const EdgeInsets.all(8),
        itemCount: listWidget.length,
        itemBuilder: (context, index) {
          return InkWell(
              onTap: () async {
                if (index == 0) {
                  // First item is "Add Picture" button
                  List<XFile> files = [];
                  await DialogHelper.showAppDialog(
                    context,
                    title: 'Add Pictures',
                    content: "Please select the method to add pictures!",
                    textStyle: Theme.of(context).contentTextStyle,
                    actions: [
                      AppButton(
                        backgroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 22, vertical: 8),
                        onPressed: () async {
                          XFile? file = await ImagePicker()
                              .pickImage(source: ImageSource.camera);
                          Navigator.pop(context);
                          if (file != null) {
                            files.add(file);
                          }
                        },
                        child: Center(
                          child: Text(
                            "Camera",
                            style: Theme.of(context)
                                .textTheme
                                .bodyText1!
                                .copyWith(
                                    color: AppColors.background, fontSize: 16),
                          ),
                        ),
                      ),
                      AppButton(
                        backgroundColor: AppColors.primaryColor,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 22, vertical: 8),
                        onPressed: () async {
                          files = await ImagePicker().pickMultiImage();
                          Navigator.pop(context);
                        },
                        child: Center(
                          child: Text(
                            "Gallery",
                            style: Theme.of(context)
                                .textTheme
                                .bodyText1!
                                .copyWith(
                                    color: AppColors.background, fontSize: 16),
                          ),
                        ),
                      ),
                    ],
                  );
                  if (files.isNotEmpty) {
                    context.read<JobDetailsBloc>().add(
                        ImageInputsInserted(
                            files: files, index: 0, description: description));
                  }
                }
              },
              child: listWidget[index]);
        },
      ),
    );
  }
}

class _BuildNoImage extends StatelessWidget {
  const _BuildNoImage({Key? key, required this.description}) : super(key: key);
  final String description;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        List<XFile> files = [];
        await DialogHelper.showAppDialog(
          context,
          title: 'Add Pictures',
          content: "Please select the method to add pictures!",
          textStyle: Theme.of(context).contentTextStyle,
          actions: [
            AppButton(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
              onPressed: () async {
                XFile? file =
                    await ImagePicker().pickImage(source: ImageSource.camera);
                Navigator.pop(context);
                if (file != null) {
                  files.add(file);
                }
              },
              child: Center(
                child: Text(
                  "Camera",
                  style: Theme.of(context)
                      .textTheme
                      .bodyText1!
                      .copyWith(color: AppColors.background, fontSize: 16),
                ),
              ),
            ),
            AppButton(
              backgroundColor: AppColors.primaryColor,
              padding: const EdgeInsets.symmetric(horizontal: 22, vertical: 8),
              onPressed: () async {
                files = await ImagePicker().pickMultiImage();
                Navigator.pop(context);
              },
              child: Center(
                child: Text(
                  "Gallery",
                  style: Theme.of(context)
                      .textTheme
                      .bodyText1!
                      .copyWith(color: AppColors.background, fontSize: 16),
                ),
              ),
            ),
          ],
        );
        if (files.isNotEmpty) {
          context.read<JobDetailsBloc>().add(ImageInputsInserted(
              files: files, index: 0, description: description));
        }
      },
      child: Container(
        width: double.maxFinite,
        margin: const EdgeInsets.only(left: 16, right: 16, bottom: 17),
        height: 64,
        decoration: BoxDecoration(
          color: AppColors.grey4,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: Assets.icon.picture.svg(
                width: 36,
                height: 36,
                color: AppColors.grey,
              ),
            ),
            Expanded(
                flex: 5,
                child: Center(
                    child: Text(
                  '+ Click to add picture',
                  style: Theme.of(context).titleTextStyle,
                ))),
            const Expanded(flex: 2, child: SizedBox()),
          ],
        ),
      ),
    );
  }
}
