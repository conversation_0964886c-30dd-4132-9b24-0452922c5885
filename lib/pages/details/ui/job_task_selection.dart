import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:ticking_app/core/app_single_ton.dart';
import 'package:ticking_app/core/styles/app_colors.dart';
import 'package:ticking_app/core/styles/app_text_style.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';
import 'package:ticking_app/utils/extend/data_extend.dart';
import 'package:ticking_app/utils/extend/view_extend.dart';
import 'package:ticking_app/widgets/drop_down/app_drop_down.dart';

import 'job_details_page.dart';

class JobTaskSelection extends StatefulWidget {
  const JobTaskSelection({
    Key? key,
    required this.maintenanceTask,
    required this.onChanged,
    required this.isReviewing,
    this.defaultValue,
  }) : super(key: key);
  final JobMaintenanceTask maintenanceTask;
  final Function(JobTaskValue) onChanged;
  final bool isReviewing;
  final String? defaultValue;

  @override
  _JobTaskSelectionState createState() => _JobTaskSelectionState();
}

class _JobTaskSelectionState extends State<JobTaskSelection> {
  List<String> _templateDomains = [];
  List<String> _domains = [];
  String _currentValue = '';
  String _value = '';

  @override
  void initState() {
    final taskDomain = widget.maintenanceTask.maintenanceTask.domain;
    _templateDomains = widget.maintenanceTask.jobTaskTemplate.domain;
    _domains = taskDomain.isEmpty ? _templateDomains : taskDomain;

    _value = widget.defaultValue ??
        widget.maintenanceTask.jobTaskTemplate.defaultValue ??
        '';
    _currentValue =
        widget.defaultValue ?? widget.maintenanceTask.value ?? _value;
    widget.onChanged(JobTaskValue(_currentValue, true));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // print(
    //     "]================${widget.maintenanceTask.jobTaskTemplate.name}(${widget.maintenanceTask.jobTaskTemplate.id})===============[");
    // print("Job id: ${widget.maintenanceTask.maintenanceTask.id}");
    // print(
    //     "Template [${_templateDomains.length}]: ${_templateDomains.toList().toString()}");
    // print("Domains [${_domains.length}]: ${_domains.toList()}");
    // print("DefaultValue [${widget.maintenanceTask.jobTaskTemplate.defaultValue}]");
    final now = DateTime.now();
    return AbsorbPointer(
      absorbing: widget.isReviewing,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                widget.maintenanceTask.jobTaskTemplate.name,
                style: Theme.of(context).normalStyle.copyWith(
                    color: AppColors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 16),
              ),
              if (widget.maintenanceTask.maintenanceTask.isRequired)
                Text(
                  "(Required)",
                  style: Theme.of(context)
                      .normalStyle
                      .copyWith(color: AppColors.redAccentColor, fontSize: 15),
                ).pl(value: 5),
            ],
          ).pb(value: 8).pt(value: 8),
          SizedBox(
            width: double.maxFinite,
            child: AppDropDown(
              groupNameList: _templateDomains,
              enableNameList: _domains,
              onSelectedItem: (index) {
                _currentValue = _templateDomains[index];
                widget.onChanged(JobTaskValue(_currentValue, true));
              },
              index: _templateDomains
                  .indexWhere((element) => element == _currentValue),
              color: Colors.white,
              margin: const EdgeInsets.only(left: 0, right: 0),
              textColor: AppColors.grey,
              icon: const Icon(
                Icons.arrow_drop_down,
                color: AppColors.grey,
              ),
              popUpLeft: 0,
              decoration: BoxDecoration(
                  border: Border.all(color: AppColors.grey3),
                  borderRadius: BorderRadius.circular(8)),
              textPopUpColor: AppColors.grey,
            ),
          ),
          Text(
            'Not done in '
            '${DateTimeExtensions.daysBetween(widget.maintenanceTask.maintenanceTask.lastDoneOn?.clearTime() ?? now, now)} days',
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: AppColors.grey2, fontSize: 12),
          ).pt(value: 5),
        ],
      ),
    );
  }
}
