import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:ticking_app/all_file/all_file.dart';
import 'package:ticking_app/pages/details/bloc/job_details_bloc.dart';
import 'package:ticking_app/pages/details/model/job_maintance_task.dart';

import '../../../utils/input_format.dart';
import 'job_details_page.dart';

class JobTaskInputCompleted extends StatefulWidget {
  const JobTaskInputCompleted({
    Key? key,
    required this.maintenanceTask,
    required this.onChanged,
    this.isChange,
    this.defaultValue,
    required this.isReviewing,
  }) : super(key: key);
  final JobMaintenanceTask maintenanceTask;
  final Function(JobTaskValue) onChanged;
  final bool isReviewing;
  final bool? isChange;
  final String? defaultValue;

  @override
  _JobTaskInputCompletedState createState() => _JobTaskInputCompletedState();
}

class _JobTaskInputCompletedState extends State<JobTaskInputCompleted> {
  double _currentInputValue = 0;
  String _currentInput = '';
  String _estimateValue = '';
  bool isShowError = false;
  List<String> domains = [];
  @override
  void initState() {
    super.initState();
    domains = widget.maintenanceTask.jobTaskTemplate.domain;

    if (widget.maintenanceTask.jobTaskTemplate.defaultValue?.isEmpty == false) {
      _estimateValue =
          widget.maintenanceTask.jobTaskTemplate.defaultValue.toString();
    }

    _currentInputValue =
        double.tryParse((widget.maintenanceTask.value ?? '0').toString()) ?? 0;

    if (widget.maintenanceTask.value == null) {
      widget.onChanged(JobTaskValue("", false));
      return;
    }

    if (_currentInputValue > 0) {
      _currentInput = _currentInputValue.toString();
      widget.onChanged(JobTaskValue(_currentInputValue, true));
    } else {
      if ((widget.isChange ?? widget.maintenanceTask.isChangedValue) == false) {
        _currentInput = '';
      } else {
        _currentInput = '0';
        widget.onChanged(JobTaskValue(_currentInputValue, true));
      }
    }
  }

  _buildMinMax() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Text(
            'Min: ${domains[0]}, Max: ${domains[2]}, Estimated: ${_estimateValue.toString()} ${widget.maintenanceTask.jobTaskTemplate.unit}',
            style: Theme.of(context).textTheme.bodyText2?.copyWith(
                color: AppColors.grey,
                fontSize: Dimens.text,
                fontWeight: FontWeight.w400,
                height: 1.5),
          ),
        ),
      ],
    );
  }

  _buildError() {
    final domains = widget.maintenanceTask.jobTaskTemplate.domain;

    if ((widget.isChange ?? widget.maintenanceTask.isChangedValue) == true &&
        ((_currentInputValue < double.parse(domains[0]) ||
            _currentInputValue > double.parse(domains[2])))) {
      return Padding(
        padding: const EdgeInsets.only(top: 4),
        child: Text(
          'The entered value must be between ${domains[0]} and ${domains[2]}',
          style: Theme.of(context).textTheme.bodyText2?.copyWith(
              color: AppColors.error,
              fontSize: Dimens.text,
              fontWeight: FontWeight.w400,
              height: 1.5),
        ),
      );
    }

    return const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<JobDetailsBloc, JobDetailsState>(
      buildWhen: (_, current) {
        if (current is JobDetailsUpdateSuccess) {
          if (current.task.jobTaskTemplate.id ==
              widget.maintenanceTask.jobTaskTemplate.id) {
            return widget.maintenanceTask.isChangedValue == false;
          }
        }

        return false;
      },
      listener: (context, state) {},
      builder: (context, state) {
        return AbsorbPointer(
            absorbing: widget.isReviewing,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                color: AppColors.grey4,
              ),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
              // margin: const EdgeInsets.only(left: 16, right: 16, bottom: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildTitle(),
                  _buildMinMax(),
                  Row(
                    children: [
                      Text(
                        'Used',
                        style: Theme.of(context).textTheme.bodyText2?.copyWith(
                            color: AppColors.grey,
                            fontSize: Dimens.text,
                            fontWeight: FontWeight.w400,
                            height: 1.5),
                      ),
                      8.toHSizeBox(),
                      SizedBox(
                        width: 80,
                        child: TextFormField(
                          onTapOutside: (event) {
                            FocusManager.instance.primaryFocus?.unfocus();
                          },
                          initialValue: widget.maintenanceTask.value,
                          style: const TextStyle(fontSize: 14),
                          keyboardType: const TextInputType.numberWithOptions(
                            decimal: true,
                          ),
                          onChanged: (value) {},
                          onEditingComplete: () {
                            FocusScope.of(context).unfocus();
                          },
                          inputFormatters: <TextInputFormatter>[
                            RegExInputFormatter.withRegex(
                                r'^([0-9]+([.,][0-9]*)?|[.,][0-9]+)$'),
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^\d+[.,]?\d{0,2}')),
                            LengthLimitingTextInputFormatter(15),
                          ],
                          decoration: const InputDecoration(
                            filled: true,
                            fillColor: Colors.white,
                            border: OutlineInputBorder(
                                borderSide: BorderSide(color: AppColors.grey5)),
                            isDense: true,
                            isCollapsed: true,
                            contentPadding: EdgeInsets.all(8), // Added this
                          ),
                        ),
                      ),
                      16.toHSizeBox(),
                      Text(
                        '${widget.maintenanceTask.jobTaskTemplate.unit}',
                        style: Theme.of(context).textTheme.bodyText2?.copyWith(
                            color: AppColors.grey,
                            fontSize: Dimens.text,
                            fontWeight: FontWeight.w400,
                            height: 1.5),
                      ).minWidth(minWidth: 30)
                    ],
                  ),
                  _buildError(),
                ],
              ),
            ));
      },
    );
  }

  Widget _buildTitle() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Flexible(
          child: Text(
            widget.maintenanceTask.jobTaskTemplate.name,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(context).textTheme.bodyText1?.copyWith(
                  fontWeight: FontWeight.w800,
                  fontSize: Dimens.font_sp18,
                ),
          ),
        ),
        if (widget.maintenanceTask.maintenanceTask.isRequired)
          Text(
            "(Required)",
            style: Theme.of(context)
                .normalStyle
                .copyWith(color: AppColors.redAccentColor, fontSize: 15),
          ).pl(value: 5),
      ],
    );
  }

  Color get widgetColor =>
      widget.isReviewing ? AppColors.grey12 : AppColors.grey;
}
