name: ticking_api_client
description: A new Flutter project.
version: 0.0.1
homepage:

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  dio: ^4.0.6 #Null Safety
  http: ^1.0.0
  get_it: ^6.0.0 #Null Safety
  equatable: ^2.0.0
  logger: ^1.0.0
  mime_type: ^1.0.0
  cookie_jar: ^3.0.1
  dio_cookie_manager: ^2.0.0
  path_provider: ^2.0.6
  http_interceptor: ^2.0.0-beta.7
  # flutter_native_timezone: ^2.0.0  # Temporarily disabled due to Kotlin version conflict
  intl: ^0.18.0
  ticking_localizations:
    path: ../ticking_localizations
dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

# To add assets to your package, add an assets section, like this:
# assets:
#   - images/a_dot_burr.jpeg
#   - images/a_dot_ham.jpeg
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
