import 'dart:convert';
import 'dart:io';

import 'package:cookie_jar/cookie_jar.dart';
import 'package:dio/src/cancel_token.dart';
import 'package:dio/src/options.dart';
import 'package:flutter/foundation.dart';
// import 'package:flutter_native_timezone/flutter_native_timezone.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:ticking_api_client/ticking_api_client.dart';

extension IsOk on int {
  bool get ok {
    return (this ~/ 100) == 2;
  }
}

class HttpApiClient implements ApiClient {
  HttpApiClient() {
    _logger = Logger(
      filter: DevelopmentFilter(),
      printer: PrefixPrinter(PrettyPrinter()),
    );
  }

  late ApiConfig _config;
  late Logger _logger;
  late CookieJar _cookieJar;
  late String _currentTimeZone;

  @override
  ApiConfig get apiConfig => throw UnimplementedError();

  String? buildHtmlUrlEncodeFromParam(Map<String, dynamic>? param) {
    if (param != null && param.isNotEmpty) {
      final parameters = param.keys.map((key) {
        final data = param[key];

        if (data is List) {
          return data.map((item) => '$key=$item').join('&');
        }

        return '$key=$data';
      }).join('&');

      return parameters;
    } else
      return null;
  }

  @override
  Future<void> init() async {
    Directory appDocDir = await getApplicationDocumentsDirectory();
    String appDocPath = appDocDir.path;
    // _currentTimeZone = await FlutterNativeTimezone.getLocalTimezone();
    _currentTimeZone = 'Asia/Ho_Chi_Minh'; // Default timezone for Vietnam

    _cookieJar =
        PersistCookieJar(storage: FileStorage(appDocPath + '/.cookies/'));
  }

  @override
  Future<void> clearSession() async {
    await _cookieJar.deleteAll();
  }

  Future<void> setup() async {
    final url = _config.url;
    if (url != null) {
      await _cookieJar.loadForRequest(Uri.parse(url));
    }
  }

  @override
  Future<void> config(ApiConfig config) async {
    _config = config;
    await setup();
  }

  @override
  Future<T?> httpDelete<T>(String path,
      {data,
      Map<String, dynamic>? parameters,
      CancelToken? cancelToken,
      bool catchDefaultException = true}) async {
    final cookies =
        await _cookieJar.loadForRequest(Uri.parse(_config.url ?? ''));

    String cookie = getCookies(cookies);

    String? queryString = buildHtmlUrlEncodeFromParam(parameters);

    String url =
        '${_config.url}$path${queryString != null ? '?$queryString' : ''}';

    Uri uri = Uri.parse(url);

    var headers = <String, String>{
      'Content-Type': 'application/json',
      'Cookie': cookie,
      'X-Time-Zone': _currentTimeZone
    };

    var request = http.Request('DELETE', uri);

    if (data != null) {
      request.body = json.encode(_createJsonRPC(data));
    } else {
      request.body = json.encode(
          {'jsonrpc': '2.0', 'method': 'call', 'params': {}, 'id': null});
    }

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    if (kDebugMode) {
      _logger.i(convertRequestToCurl(request));
    }

    if (response.statusCode.ok) {
      final responseData = await response.stream.bytesToString();

      final result = json.decode(responseData);

      _updateCookie(response.headers, Uri.parse(_config.url ?? ''));

      _logger.i('Http DELETE=============================>');
      _logger.i(result);
      _logger.i('======================================');
      return result;
    } else {
      final responseData = await response.stream.bytesToString();
      Map<String, dynamic>? data;
      try {
        data = json.decode(responseData);
      } catch (_) {}

      throw _getException(
        statusCode: response.statusCode,
        data: data != null ? data : responseData,
      );
    }
  }

  static String getCookies(List<Cookie> cookies) {
    return cookies.map((cookie) => '${cookie.name}=${cookie.value}').join('; ');
  }

  Map<String, dynamic>? _createJsonRPC(Map<String, dynamic>? data) {
    final jsonData = {
      'jsonrpc': '2.0',
      'method': 'call',
      'params': data,
      'id': null
    };

    if (data == null) {
      jsonData.remove('params');
    }

    return jsonData;
  }

  String convertRequestToCurl(http.Request request) {
    final url = request.url.toString();
    final method = request.method;
    final headers = request.headers;
    final body = request.body;

    final headerList = headers.entries
        .map((entry) => '-H "${entry.key}: ${entry.value}"')
        .toList();
    final headerString = headerList.join(' ');

    final bodyString = body.isNotEmpty ? '-d \'$body\'' : '';

    return 'curl -X $method $headerString $bodyString "$url"';
  }

  void _updateCookie(Map<String, String> headers, Uri uri) {
    String? rawCookie = headers['set-cookie'];
    if (rawCookie != null) {
      int index = rawCookie.indexOf(';');
      String cookie = (index == -1) ? rawCookie : rawCookie.substring(0, index);
      _cookieJar.saveFromResponse(uri, [Cookie.fromSetCookieValue(cookie)]);
    }
  }

  @override
  Future<T?> httpGet<T>(
    String path, {
    Map<String, dynamic>? parameters,
    Map<String, dynamic>? data,
    CancelToken? cancelToken,
    bool catchDefaultException = true,
  }) async {
    final cookies =
        await _cookieJar.loadForRequest(Uri.parse(_config.url ?? ''));

    String cookie = getCookies(cookies);

    String? queryString = buildHtmlUrlEncodeFromParam(parameters);

    String url =
        '${_config.url}$path${queryString != null ? '?$queryString' : ''}';

    Uri uri = Uri.parse(url);

    var headers = <String, String>{
      'Content-Type': 'application/json',
      'Cookie': cookie,
      'X-Time-Zone': _currentTimeZone
    };

    var request = http.Request('GET', uri);

    request.body = json.encode(_createJsonRPC(data));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    if (kDebugMode) {
      _logger.i(convertRequestToCurl(request));
    }
    if (response.statusCode.ok) {
      try {
        final responseData = await response.stream.bytesToString();

        final result = json.decode(responseData);

        _updateCookie(response.headers, Uri.parse(_config.url ?? ''));

        if (kDebugMode) {
          _logger.i('Http GET=============================>${_config.url}');
          // _logger.i(result);
          _logger.i('======================================');
        }

        return result;
      } catch (e) {
        throw ApiExceptions.defaultE('No internet connection');
      }
    } else {
      final responseData = await response.stream.bytesToString();
      Map<String, dynamic>? data;
      try {
        data = json.decode(responseData);
      } catch (_) {}

      throw _getException(
        statusCode: response.statusCode,
        data: data != null ? data : responseData,
      );
    }
  }

  @override
  Future<T?> httpPatch<T>(String path,
      {data,
      Map<String, dynamic>? parameters,
      CancelToken? cancelToken,
      bool catchDefaultException = true}) async {
    final cookies =
        await _cookieJar.loadForRequest(Uri.parse(_config.url ?? ''));

    String cookie = getCookies(cookies);

    String? queryString = buildHtmlUrlEncodeFromParam(parameters);

    String url =
        '${_config.url}$path${queryString != null ? '?$queryString' : ''}';

    Uri uri = Uri.parse(url);

    var headers = <String, String>{
      'Content-Type': 'application/json',
      'Cookie': cookie,
      'X-Time-Zone': _currentTimeZone
    };

    var request = http.Request('PATCH', uri);

    request.body = json.encode(_createJsonRPC(data));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    if (kDebugMode) {
      _logger.i(convertRequestToCurl(request));
    }
    if (response.statusCode.ok) {
      try {
        final responseData = await response.stream.bytesToString();

        final result = json.decode(responseData);

        _updateCookie(response.headers, Uri.parse(_config.url ?? ''));

        if (kDebugMode) {
          _logger.i('Http PATCH=============================>');
          _logger.i(result);
          _logger.i('======================================');
        }
        return result;
      } catch (e) {
        throw ApiExceptions.defaultE('No internet connection');
      }
    } else {
      final responseData = await response.stream.bytesToString();
      Map<String, dynamic>? data;
      try {
        data = json.decode(responseData);
      } catch (_) {}

      throw _getException(
        statusCode: response.statusCode,
        data: data != null ? data : responseData,
      );
    }
  }

  @override
  Future<T?> httpPost<T>(
    String path, {
    data,
    Map<String, dynamic>? parameters,
    CancelToken? cancelToken,
    bool catchDefaultException = true,
    bool saveHeader = false,
  }) async {
    final cookies =
        await _cookieJar.loadForRequest(Uri.parse(_config.url ?? ''));

    String cookie = getCookies(cookies);

    String? queryString = buildHtmlUrlEncodeFromParam(parameters);

    String url =
        '${_config.url}$path${queryString != null ? '?$queryString' : ''}';

    Uri uri = Uri.parse(url);

    var headers = <String, String>{
      'Content-Type': 'application/json',
      'Cookie': cookie,
      'X-Time-Zone': _currentTimeZone,
    };
    if (data['X-Offline-Mode'].toString() == "1") {
      headers['X-Offline-Mode'] = data['X-Offline-Mode'].toString();
    }

    if (kDebugMode) {
      _logger.i('Http POST=============================>');
      _logger.i("Headers: $headers");
      _logger.i("url: $url");
      _logger.i("parameters: $parameters");
      _logger.i("data: $data");
      _logger.i('======================================');
    }

    var request = http.Request('POST', uri);

    request.body = json.encode(_createJsonRPC(data));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    if (kDebugMode) {
      _logger.i(convertRequestToCurl(request));
    }
    if (response.statusCode.ok) {
      try {
        final responseData = await response.stream.bytesToString();

        final result = json.decode(responseData);

        _updateCookie(response.headers, Uri.parse(_config.url ?? ''));

        if (kDebugMode) {
          _logger.i('Http POST=============================>');
          _logger.i(request);
          _logger.i(result);
          _logger.i('======================================');
        }

        return result;
      } catch (e) {
        throw ApiExceptions.defaultE('No internet connection');
      }
    } else {
      final responseData = await response.stream.bytesToString();
      Map<String, dynamic>? data;
      try {
        data = json.decode(responseData);
      } catch (_) {}

      throw _getException(
        statusCode: response.statusCode,
        data: data != null ? data : responseData,
      );
    }
  }

  @override
  Future<T?> httpPut<T>(
    String path, {
    String? url,
    Map<String, dynamic>? parameters,
    data,
    Options? options,
    CancelToken? cancelToken,
    bool catchDefaultException = true,
  }) async {
    final cookies =
        await _cookieJar.loadForRequest(Uri.parse(_config.url ?? ''));

    String cookie = getCookies(cookies);

    String? queryString = buildHtmlUrlEncodeFromParam(parameters);

    String url =
        '${_config.url}$path${queryString != null ? '?$queryString' : ''}';

    Uri uri = Uri.parse(url);

    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Cookie': cookie,
      'X-Time-Zone': _currentTimeZone
    };

    final request = http.Request('PUT', uri);

    request.body = json.encode(_createJsonRPC(data));

    request.headers.addAll(headers);

    http.StreamedResponse response = await request.send();
    if (kDebugMode) {
      _logger.i(convertRequestToCurl(request));
    }
    if (response.statusCode.ok) {
      try {
        final responseData = await response.stream.bytesToString();

        final result = json.decode(responseData);

        _updateCookie(response.headers, Uri.parse(_config.url ?? ''));

        if (kDebugMode) {
          _logger.i('Http PUT=============================>');
          _logger.i(result);
          _logger.i('======================================');
        }

        return result;
      } catch (e) {
        throw ApiExceptions.defaultE('No internet connection');
      }
    } else {
      final responseData = await response.stream.bytesToString();
      Map<String, dynamic>? data;
      try {
        data = json.decode(responseData);
      } catch (_) {}

      throw _getException(
        statusCode: response.statusCode,
        data: data != null ? data : responseData,
      );
    }
  }

  Exception _getException({required dynamic statusCode, dynamic data}) {
    _logger.e('Http error ${data}');
    _logger.e('Http status code ${statusCode}');
    switch (statusCode) {
      case 401:
        return ApiBadRequestException.fromJson(
          "Your session has expired, please login again to continue.",
          statusCode,
        );
      case 403:
      case 400:
        return ApiBadRequestException.fromJson(
          data,
          statusCode,
        );

      case 429:
      case 500:
      case 404:
      case 503:
      case 422:
      case "JOB-0004":
        // case 504:
        return ApiBadRequestException.fromJson(data, statusCode);

      // return ApiExceptions.notFound();
      // case 503:
      //   return ApiExceptions.serviceUnavailable();
      case 504:
        return ApiTimeoutException();
      default:
        return ApiExceptions.defaultE('$data');
    }
  }
}
