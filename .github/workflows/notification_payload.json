{"cardsV2": [{"cardId": "THREAD_KEY", "card": {"sections": [{"collapsible": false, "widgets": [{"decoratedText": {"startIcon": {"knownIcon": "VIDEO_PLAY"}, "wrapText": "true", "topLabel": "Executed Job", "text": "<a href='BUILD_URL' style='color: #0000FF; text-decoration: underline;' target='_blank'>BUILD_URL</a>", "onClick": {"openLink": {"url": "BUILD_URL"}}}}, {"decoratedText": {"startIcon": {"knownIcon": "STORE"}, "wrapText": "true", "topLabel": "Git <PERSON>o", "text": "GIT_REPO"}}, {"decoratedText": {"startIcon": {"knownIcon": "STAR"}, "wrapText": "true", "topLabel": "Git Branch", "text": "GIT_BRANCH"}}, {"decoratedText": {"startIcon": {"knownIcon": "BOOKMARK"}, "wrapText": "true", "topLabel": "Build Type", "text": "BUILD_TYPE"}}, {"decoratedText": {"startIcon": {"knownIcon": "TICKET"}, "wrapText": "true", "topLabel": "Commit ID", "text": "COMMIT_ID"}}, {"decoratedText": {"startIcon": {"knownIcon": "BOOKMARK"}, "wrapText": "true", "topLabel": "Status", "text": "STATUS"}}, {"decoratedText": {"startIcon": {"knownIcon": "STORE"}, "wrapText": "true", "topLabel": "Download APK", "text": "<a href='APK_URL' style='color: #0000FF; text-decoration: underline;' target='_blank'>APK_URL</a>"}}]}]}}], "thread": {"threadKey": "THREAD_KEY"}, "threadReply": false, "text": ""}