name: Build & deploy Android BSP Mobile for Staging Environment

on:
  push:
    branches:
      - mobile-staging

jobs:
  deployAndroid:
    name: Build & deploy Android BSP-Mobile for Staging Environment
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: mobile-staging

      - name: Get version from versions.yaml
        run: |
          BUILD_NUMBER=$(awk 'NR==2 {gsub(":", ""); print $1}' versions.yaml)
          VERSION=$(awk '/'"$BUILD_NUMBER"':/,/^$/{if ($1 == "build_version:") print $2}' versions.yaml)
          sed -i "s/^version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "BUILD_NUMBER=$BUILD_NUMBER" >> $GITHUB_ENV

      - name: Generate Notification Thread Key
        id: generate_thread_key
        run: echo "::set-output name=key::$(echo ${{ github.sha }} | head -c 8)"

      - name: Modify Notification Payload
        run: |
          sed -i "s|THREAD_KEY|${{ steps.generate_thread_key.outputs.key }}|g" .github/workflows/notification_payload.json
          sed -i "s|BUILD_URL|${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|g" .github/workflows/notification_payload.json
          sed -i "s|GIT_REPO|${{ github.repository }}|g" .github/workflows/notification_payload.json
          sed -i "s|GIT_BRANCH|${{ github.ref_name }}|g" .github/workflows/notification_payload.json
          sed -i "s|BUILD_TYPE|Android Staging Build|g" .github/workflows/notification_payload.json
          sed -i "s|COMMIT_ID|${{ github.sha }}|g" .github/workflows/notification_payload.json
          sed -i "s|STATUS|Start Build|g" .github/workflows/notification_payload.json
          sed -i "s|APK_URL|https://github.com/${{ github.repository }}/releases/download/mobile-staging-${{ env.VERSION }}-${{ env.BUILD_NUMBER }}-${{  github.run_number }}/bsp-staging-${{ env.VERSION }}-${{ env.BUILD_NUMBER }}-${{  github.run_number }}.apk|g" .github/workflows/notification_payload.json

      - name: Google Chat Notification Start Build
        run: |
          curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.16.0"
          channel: "stable"
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Create Android BSP-Mobile APK
        run: |
          flutter build apk --release
          mv ./build/app/outputs/flutter-apk/app-release.apk ./build/app/outputs/flutter-apk/bsp-staging-$VERSION-$BUILD_NUMBER-${{  github.run_number }}.apk

      - name: Create Staging Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          tag: mobile-staging-${{  env.VERSION }}-${{  env.BUILD_NUMBER }}-${{  github.run_number }}
        run: gh release create "$tag" --repo="$GITHUB_REPOSITORY" --title="Release ${tag#v}" --generate-notes --target mobile-staging

      - name: Upload APK to Staging Release
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          tag: mobile-staging-${{  env.VERSION }}-${{  env.BUILD_NUMBER }}-${{  github.run_number }}
        run: gh release upload "$tag" ./build/app/outputs/flutter-apk/bsp-staging-$VERSION-$BUILD_NUMBER-${{  github.run_number }}.apk

      - name: Modify Notification Payload
        if: always()
        run: |
          if [ "${{ job.status }}" = "success" ]; then
            sed -i "s|Start Build|<font color='#80e27e'>SUCCESS</font>|g" .github/workflows/notification_payload.json
          elif [ "${{ job.status }}" = "failure" ]; then
            sed -i "s|Start Build|<font color='#ff0000'>FAILURE</font>|g" .github/workflows/notification_payload.json
            sed -i "s|\"text\": \"\"|\"text\": \"<users/all>\"|g" .github/workflows/notification_payload.json
          else
            sed -i "s|Start Build|<font color='#ffc300'>CANCELLED</font>|g" .github/workflows/notification_payload.json
          fi
          sed -i "s|"threadReply": false|"threadReply": true|g" .github/workflows/notification_payload.json

      - name: Google Chat Notification Build Status
        if: always()
        run: |
          curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'
