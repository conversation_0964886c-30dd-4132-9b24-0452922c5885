name: Build & deploy IOS BSP Mobile for Softlaunch Environment

on:
  push:
    branches:
      - mobile-softlaunch

jobs:
  deployIOS:
    name: Build & deploy IOS BSP-Mobile release
    if: github.actor == 'vantran-novobi' || github.actor == 'khiemtran-novobi' || github.actor == 'quantran-novobi' || github.actor == 'summernguyen-novobi' || github.actor == 'kienle-novobi' && github.ref == 'refs/heads/mobile-softlaunch'
    runs-on: macos-14
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: mobile-softlaunch

      - name: Read versions.yaml and validate release target
        id: validate
        run: |
          BUILD_NUMBER=$(awk 'NR==2 {gsub(":", ""); print $1}' versions.yaml)
          RELEASE_TARGET=$(awk '/'"$BUILD_NUMBER"':/,/^$/{if ($1 == "release_target:") print $2}' versions.yaml)

          if [[ "$RELEASE_TARGET" == "softlaunch" && "$GITHUB_REF" != "refs/heads/mobile-softlaunch" ]]; then
            echo "Release target is softlaunch but the branch is not mobile-softlaunch."
            exit 1
          elif [[ "$RELEASE_TARGET" == "production" && "$GITHUB_REF" != "refs/heads/mobile-production" ]]; then
            echo "Release target is production but the branch is not mobile-production."
            exit 1
          fi
          
      # - name: Generate Notification Thread Key
      #   id: generate_thread_key
      #   run: echo "::set-output name=key::$(echo ${{ github.sha }} | head -c 8)"

      # - name: Modify Notification Payload
      #   run: |
      #     sed -i '' "s|THREAD_KEY|${{ steps.generate_thread_key.outputs.key }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|BUILD_URL|${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|GIT_REPO|${{ github.repository }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|GIT_BRANCH|${{ github.ref_name }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|BUILD_TYPE|IOS Softlaunch Build|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|COMMIT_ID|${{ github.sha }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|COMMIT_AUTHOR|${{ github.event.head_commit.author.name }}|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|STATUS|Start Build|g" .github/workflows/notification_payload.json
      #     sed -i '' "s|APK_URL|Download on Test Flight|g" .github/workflows/notification_payload.json

      # - name: Google Chat Notification Start Build
      #   run: |
      #     curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'

      - name: Install the Apple certificate and provisioning profile
        env:
          BUILD_CERTIFICATE_BASE64: ${{ secrets.BUILD_CERTIFICATE_BASE64 }}
          P12_PASSWORD: ${{ secrets.P12_PASSWORD }}
          BUILD_PROVISION_PROFILE_BASE64: ${{ secrets.BUILD_PROVISION_PROFILE_BASE64 }}
          KEYCHAIN_PASSWORD: ${{ secrets.KEYCHAIN_PASSWORD }}
          EXPORT_OPTIONS_BASE64: "${{ secrets.EXPORT_OPTIONS_BASE64_SOFTLAUNCH }}"
        run: |
          # create variables
          CERTIFICATE_PATH=$RUNNER_TEMP/build_certificate.p12
          PP_PATH=$RUNNER_TEMP/build_pp.mobileprovision
          KEYCHAIN_PATH=$RUNNER_TEMP/app-signing.keychain-db
          EXPORT_OPTIONS_PATH=./ios/Runner/ExportOptions.plist

          # import certificate and provisioning profile from secrets
          echo -n "$BUILD_CERTIFICATE_BASE64" | base64 --decode -o $CERTIFICATE_PATH
          echo -n "$BUILD_PROVISION_PROFILE_BASE64" | base64 --decode -o $PP_PATH
          echo -n "$EXPORT_OPTIONS_BASE64" | base64 --decode -o $EXPORT_OPTIONS_PATH`

          # create temporary keychain
          security create-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security set-keychain-settings -lut 21600 $KEYCHAIN_PATH
          security unlock-keychain -p "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH

          # import certificate to keychain
          security import $CERTIFICATE_PATH -P "$P12_PASSWORD" -A -t cert -f pkcs12 -k $KEYCHAIN_PATH
          security set-key-partition-list -S apple-tool:,apple: -k "$KEYCHAIN_PASSWORD" $KEYCHAIN_PATH
          security list-keychain -d user -s $KEYCHAIN_PATH

          # apply provisioning profile
          mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles
          cp $PP_PATH ~/Library/MobileDevice/Provisioning\ Profiles
          ls -l ~/Library/MobileDevice/Provisioning\ Profiles

      - name: Get version from versions.yaml
        run: |
          BUILD_NUMBER=$(awk 'NR==2 {gsub(":", ""); print $1}' versions.yaml)
          VERSION=$(awk '/'"$BUILD_NUMBER"':/,/^$/{if ($1 == "build_version:") print $2}' versions.yaml)
          sed -i '' "s/^version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml
          echo "VERSION=$VERSION" >> $GITHUB_ENV
          echo "BUILD_NUMBER=$BUILD_NUMBER" >> $GITHUB_ENV

      - name: Update version in iOS
        run: |
          VERSION=${{ env.VERSION }}
          BUILD_NUMBER=${{ env.BUILD_NUMBER }}
          sed -i '' "s/MARKETING_VERSION = [^;]*/MARKETING_VERSION = ${VERSION}/" ios/Runner.xcodeproj/project.pbxproj
          sed -i '' "s/CURRENT_PROJECT_VERSION = [^;]*/CURRENT_PROJECT_VERSION = ${BUILD_NUMBER}/" ios/Runner.xcodeproj/project.pbxproj

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.16.0"
          channel: "stable"
          cache: true

      - name: Create iOS BSP-Mobile appbundle release
        run: |
          flutter build ipa --release --export-options-plist=./ios/Runner/ExportOptions.plist
          cd ${{ github.workspace }}/build/ios/ipa/
          mv "BSP ticket.ipa" bsp-ticket.ipa

      - name: Install private API key P8
        env:
          PRIVATE_API_KEY_BASE64: ${{ secrets.PRIVATE_API_KEY_BASE64 }}
          API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
        run: |
          mkdir -p ~/private_keys
          echo -n "${{ secrets.APP_STORE_CONNECT_API_PRIVATE_KEY }}" | base64 --decode -o ~/private_keys/AuthKey_$API_KEY.p8

      - name: Upload app to TestFlight
        env:
          API_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY_ID }}
          API_ISSUER: ${{ secrets.APP_STORE_CONNECT_ISSUER_ID }}
        run: xcrun altool --output-format xml --upload-app -f ${{ github.workspace }}/build/ios/ipa/bsp-ticket.ipa -t ios --apiKey $API_KEY --apiIssuer $API_ISSUER

      # - name: Modify Notification Payload
      #   if: always()
      #   run: |
      #     if [ "${{ job.status }}" = "success" ]; then
      #       sed -i '' "s|Start Build|<font color='#80e27e'>SUCCESS</font>|g" .github/workflows/notification_payload.json
      #     elif [ "${{ job.status }}" = "failure" ]; then
      #       sed -i '' "s|Start Build|<font color='#ff0000'>FAILURE</font>|g" .github/workflows/notification_payload.json
      #       sed -i '' "s|\"text\": \"\"|\"text\": \"<users/all>\"|g" .github/workflows/notification_payload.json
      #     else
      #       sed -i '' "s|Start Build|<font color='#ffc300'>CANCELLED</font>|g" .github/workflows/notification_payload.json
      #     fi
      #     sed -i '' "s|"threadReply": false|"threadReply": true|g" .github/workflows/notification_payload.json

      # - name: Google Chat Notification Build Status
      #   if: always()
      #   run: |
      #     curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'