name: Build & deploy Android BSP Mobile for Softlaunch Environment

on:
  push:
    branches:
      - mobile-softlaunch

jobs:
  deployAndroid:
    name: Build & deploy Android BSP-Mobile release
    if: github.actor == 'vantran-novobi' || github.actor == 'khiemtran-novobi' || github.actor == 'quantran-novobi' || github.actor == 'summernguyen-novobi' || github.actor == 'kienle-novobi' && github.ref == 'refs/heads/mobile-softlaunch'
    runs-on: ubuntu-latest
    permissions:
      contents: write
    env:
      package-name: com.bluescience.app
      release-status: completed
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: mobile-softlaunch

      - name: Read versions.yaml and validate release target
        id: validate
        run: |
          BUILD_NUMBER=$(awk 'NR==2 {gsub(":", ""); print $1}' versions.yaml)
          RELEASE_TARGET=$(awk '/'"$BUILD_NUMBER"':/,/^$/{if ($1 == "release_target:") print $2}' versions.yaml)

          if [[ "$RELEASE_TARGET" == "softlaunch" && "$GITHUB_REF" != "refs/heads/mobile-softlaunch" ]]; then
            echo "Release target is softlaunch but the branch is not mobile-softlaunch."
            exit 1
          elif [[ "$RELEASE_TARGET" == "production" && "$GITHUB_REF" != "refs/heads/mobile-production" ]]; then
            echo "Release target is production but the branch is not mobile-production."
            exit 1
          fi

      # - name: Generate Notification Thread Key
      #   id: generate_thread_key
      #   run: echo "::set-output name=key::$(echo ${{ github.sha }} | head -c 8)"

      # - name: Modify Notification Payload
      #   run: |
      #     sed -i "s|THREAD_KEY|${{ steps.generate_thread_key.outputs.key }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|BUILD_URL|${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|GIT_REPO|${{ github.repository }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|GIT_BRANCH|${{ github.ref_name }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|BUILD_TYPE|Android Softlaunch Build|g" .github/workflows/notification_payload.json
      #     sed -i "s|COMMIT_ID|${{ github.sha }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|COMMIT_AUTHOR|${{ github.event.head_commit.author.name }}|g" .github/workflows/notification_payload.json
      #     sed -i "s|STATUS|Start Build|g" .github/workflows/notification_payload.json

      # - name: Google Chat Notification Start Build
      #   run: |
      #     curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'

      - name: Get version from versions.yaml
        run: |
          BUILD_NUMBER=$(awk 'NR==2 {gsub(":", ""); print $1}' versions.yaml)
          VERSION=$(awk '/'"$BUILD_NUMBER"':/,/^$/{if ($1 == "build_version:") print $2}' versions.yaml)
          sed -i "s/^version: .*/version: $VERSION+$BUILD_NUMBER/" pubspec.yaml

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: "3.16.0"
          channel: "stable"
          cache: true

      - name: Install dependencies
        run: flutter pub get

      - name: Create Android BSP-Mobile appbundle release
        run: flutter build appbundle

      - name: Upload Android BSP-Mobile Release to Play Store
        uses: r0adkll/upload-google-play@v1.1.3
        with:
          packageName: ${{ env.package-name }}
          track: internal
          status: ${{ env.release-status }}
          releaseFiles: ./build/app/outputs/bundle/release/app-release.aab
          serviceAccountJsonPlainText: "${{ secrets.GOOGLE_PLAY_SERVICE_ACCOUNT_KEY_JSON }}"

      # - name: Modify Notification Payload
      #   if: always()
      #   run: |
      #     if [ "${{ job.status }}" = "success" ]; then
      #       sed -i "s|Start Build|<font color='#80e27e'>SUCCESS</font>|g" .github/workflows/notification_payload.json
      #     elif [ "${{ job.status }}" = "failure" ]; then
      #       sed -i "s|Start Build|<font color='#ff0000'>FAILURE</font>|g" .github/workflows/notification_payload.json
      #       sed -i "s|\"text\": \"\"|\"text\": \"<users/all>\"|g" .github/workflows/notification_payload.json
      #     else
      #       sed -i "s|Start Build|<font color='#ffc300'>CANCELLED</font>|g" .github/workflows/notification_payload.json
      #     fi
      #     sed -i "s|"threadReply": false|"threadReply": true|g" .github/workflows/notification_payload.json

      # - name: Google Chat Notification Build Status
      #   if: always()
      #   run: |
      #     curl -X POST -H 'Content-Type: application/json; charset=UTF-8' --data-binary '@.github/workflows/notification_payload.json' '${{ secrets.GG_CHAT_WEBHOOK }}&messageReplyOption=REPLY_MESSAGE_FALLBACK_TO_NEW_THREAD'
